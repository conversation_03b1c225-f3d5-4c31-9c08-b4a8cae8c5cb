-- bonkstream consolidated initial schema for Supabase

-- PROFILES TABLE
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  wallet_address TEXT UNIQUE NOT NULL,
  username TEXT,
  avatar_url TEXT,
  bio TEXT,
  is_creator <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- STREAMS TABLE
CREATE TABLE IF NOT EXISTS streams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  creator_id UUID REFERENCES profiles(id),
  title TEXT NOT NULL,
  description TEXT,
  thumbnail_url TEXT,
  playback_url TEXT,
  storage_provider TEXT,
  storage_id TEXT,
  arweave_id TEXT,
  status TEXT DEFAULT 'processing',
  duration INTEGER,
  view_count INTEGER DEFAULT 0,
  tip_count INTEGER DEFAULT 0,
  nft_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  livepeer_stream_id TEXT,
  livepeer_playback_id TEXT,
  livepeer_stream_key TEXT,
  is_active BOOLEAN DEFAULT FALSE,
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  retention_expires_at TIMESTAMP WITH TIME ZONE
);

-- TIPS TABLE
CREATE TABLE IF NOT EXISTS tips (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stream_id UUID REFERENCES streams(id),
  tipper_id UUID REFERENCES profiles(id),
  recipient_id UUID REFERENCES profiles(id),
  amount BIGINT NOT NULL,
  platform_fee_percentage DECIMAL(5,2) NOT NULL,
  platform_fee_amount BIGINT NOT NULL,
  creator_amount BIGINT NOT NULL,
  tx_signature TEXT NOT NULL,
  tipper_name TEXT,
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- NFTS TABLE
CREATE TABLE IF NOT EXISTS nfts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  mint_address TEXT NOT NULL,
  owner_id UUID REFERENCES profiles(id),
  stream_id UUID REFERENCES streams(id),
  metadata_uri TEXT NOT NULL,
  timestamp INTEGER NOT NULL,
  platform_fee_at_mint DECIMAL(5,2) NOT NULL,
  tx_signature TEXT NOT NULL,
  minter_id UUID REFERENCES profiles(id),
  creator_id UUID REFERENCES profiles(id),
  cost_bonk INTEGER,
  creator_amount_bonk INTEGER,
  platform_fee_amount_bonk INTEGER,
  bonk_transaction_signature TEXT,
  nft_transaction_signature TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- PLATFORM FEES TABLE
CREATE TABLE IF NOT EXISTS platform_fees (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  percentage DECIMAL(5,2) NOT NULL,
  effective_from TIMESTAMP WITH TIME ZONE DEFAULT now(),
  changed_by UUID REFERENCES profiles(id),
  change_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- VIDEO ARCHIVES TABLE
CREATE TABLE IF NOT EXISTS video_archives (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stream_id UUID REFERENCES streams(id),
  original_url TEXT NOT NULL,
  archive_url TEXT,
  tx_id TEXT,
  size_bytes BIGINT,
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- SYSTEM SETTINGS TABLE
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key TEXT UNIQUE NOT NULL,
  value JSONB NOT NULL,
  description TEXT,
  updated_by UUID REFERENCES profiles(id),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- AUDIT LOGS TABLE
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT,
  user_id UUID REFERENCES profiles(id),
  ip_address TEXT,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- FEATURE FLAGS TABLE
CREATE TABLE IF NOT EXISTS feature_flags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT UNIQUE NOT NULL,
  enabled BOOLEAN DEFAULT false,
  description TEXT,
  config JSONB DEFAULT '{}'::jsonb,
  updated_by UUID REFERENCES profiles(id),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- STREAM LIKES TABLE
CREATE TABLE IF NOT EXISTS stream_likes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stream_id UUID REFERENCES streams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(stream_id, user_id)
);

-- CHAT MESSAGES TABLE
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID REFERENCES streams(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  sender_wallet_address TEXT NOT NULL,
  message_content TEXT NOT NULL CHECK (char_length(message_content) <= 500),
  emotes JSONB DEFAULT '[]'::jsonb,
  is_deleted BOOLEAN DEFAULT FALSE,
  deleted_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- CHAT EMOTES TABLE
CREATE TABLE IF NOT EXISTS chat_emotes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT,
  url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STREAM VIEWER COUNT TABLE
CREATE TABLE IF NOT EXISTS stream_viewer_count (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID REFERENCES streams(id) ON DELETE CASCADE,
  count INTEGER DEFAULT 0,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(stream_id)
);

-- CHAT RATE LIMITS TABLE
CREATE TABLE IF NOT EXISTS chat_rate_limits (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  stream_id UUID REFERENCES streams(id) ON DELETE CASCADE,
  message_count INTEGER DEFAULT 0,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, stream_id)
);

-- INDEXES
CREATE INDEX IF NOT EXISTS idx_profiles_wallet_address ON profiles (wallet_address);
CREATE INDEX IF NOT EXISTS idx_streams_creator_id ON streams (creator_id);
CREATE INDEX IF NOT EXISTS idx_streams_status ON streams (status);
CREATE INDEX IF NOT EXISTS idx_streams_view_count ON streams(view_count);
CREATE INDEX IF NOT EXISTS idx_streams_tip_count ON streams(tip_count);
CREATE INDEX IF NOT EXISTS idx_streams_like_count ON streams(like_count);
CREATE INDEX IF NOT EXISTS idx_streams_livepeer_stream_id ON streams(livepeer_stream_id);
CREATE INDEX IF NOT EXISTS idx_streams_livepeer_playback_id ON streams(livepeer_playback_id);
CREATE INDEX IF NOT EXISTS idx_streams_is_active ON streams(is_active);
CREATE INDEX IF NOT EXISTS idx_streams_ended_at ON streams(ended_at);
CREATE INDEX IF NOT EXISTS idx_streams_updated_at ON streams(updated_at);
CREATE INDEX IF NOT EXISTS idx_tips_stream_id ON tips (stream_id);
CREATE INDEX IF NOT EXISTS idx_tips_tipper_id ON tips (tipper_id);
CREATE INDEX IF NOT EXISTS idx_tips_recipient_id ON tips (recipient_id);
CREATE INDEX IF NOT EXISTS idx_tips_tipper_name ON tips (tipper_name);
CREATE INDEX IF NOT EXISTS idx_tips_has_message ON tips ((message IS NOT NULL));
CREATE INDEX IF NOT EXISTS idx_nfts_owner_id ON nfts (owner_id);
CREATE INDEX IF NOT EXISTS idx_nfts_stream_id ON nfts (stream_id);
CREATE INDEX IF NOT EXISTS idx_nfts_minter_id ON nfts (minter_id);
CREATE INDEX IF NOT EXISTS idx_nfts_creator_id ON nfts (creator_id);
CREATE INDEX IF NOT EXISTS idx_nfts_bonk_tx ON nfts (bonk_transaction_signature);
CREATE INDEX IF NOT EXISTS idx_video_archives_stream_id ON video_archives (stream_id);
CREATE INDEX IF NOT EXISTS idx_video_archives_status ON video_archives (status);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings (key);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs (action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_entity_type ON audit_logs (entity_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_feature_flags_name ON feature_flags (name);
CREATE INDEX IF NOT EXISTS idx_stream_likes_stream_id ON stream_likes(stream_id);
CREATE INDEX IF NOT EXISTS idx_stream_likes_user_id ON stream_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_stream_id ON chat_messages(stream_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_not_deleted ON chat_messages(stream_id, created_at) WHERE is_deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_chat_emotes_name ON chat_emotes(name);
CREATE INDEX IF NOT EXISTS idx_stream_viewer_count_stream_id ON stream_viewer_count(stream_id);
CREATE INDEX IF NOT EXISTS idx_chat_rate_limits_user_stream ON chat_rate_limits(user_id, stream_id);
CREATE INDEX IF NOT EXISTS idx_chat_rate_limits_window_start ON chat_rate_limits(window_start);

-- ENABLE RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE tips ENABLE ROW LEVEL SECURITY;
ALTER TABLE nfts ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_fees ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_archives ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE stream_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_emotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE stream_viewer_count ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_rate_limits ENABLE ROW LEVEL SECURITY;

-- POLICIES (minimal, safe defaults for initial setup)
CREATE POLICY "Profiles are viewable by everyone" ON profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Anyone can create a profile" ON profiles FOR INSERT WITH CHECK (true);

CREATE POLICY "Streams are viewable by everyone" ON streams FOR SELECT USING (true);
CREATE POLICY "Only creators can add streams" ON streams FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE is_creator = true));
CREATE POLICY "Creators can update their own streams" ON streams FOR UPDATE USING (auth.uid() = creator_id);

CREATE POLICY "Tips are viewable by everyone" ON tips FOR SELECT USING (true);
CREATE POLICY "Web3 users can create tips" ON tips FOR INSERT WITH CHECK (true);
CREATE POLICY "Web3 users can update tips" ON tips FOR UPDATE USING (true);

CREATE POLICY "NFTs are viewable by everyone" ON nfts FOR SELECT USING (true);
CREATE POLICY "Owners can update their NFTs" ON nfts FOR UPDATE USING (auth.uid() = owner_id);
CREATE POLICY "Users can insert their own NFT moments" ON nfts FOR INSERT WITH CHECK (auth.uid() = minter_id OR auth.uid() = owner_id);

CREATE POLICY "Platform fees are viewable by everyone" ON platform_fees FOR SELECT USING (true);
CREATE POLICY "Only admins can change platform fees" ON platform_fees FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true));
CREATE POLICY "Only admins can update platform fees" ON platform_fees FOR UPDATE USING (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true));

CREATE POLICY "Video archives are viewable by everyone" ON video_archives FOR SELECT USING (true);
CREATE POLICY "Only admins and stream owners can add archives" ON video_archives FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true) OR auth.uid() IN (SELECT creator_id FROM streams WHERE id = stream_id));

CREATE POLICY "System settings are viewable by everyone" ON system_settings FOR SELECT USING (true);
CREATE POLICY "Only admins can update system settings" ON system_settings FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true));
CREATE POLICY "Only admins can insert system settings" ON system_settings FOR UPDATE USING (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true));

CREATE POLICY "Only admins can view audit logs" ON audit_logs FOR SELECT USING (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true));
CREATE POLICY "System can insert audit logs" ON audit_logs FOR INSERT WITH CHECK (true);

CREATE POLICY "Feature flags are viewable by everyone" ON feature_flags FOR SELECT USING (true);
CREATE POLICY "Only admins can manage feature flags" ON feature_flags FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true));
CREATE POLICY "Only admins can update feature flags" ON feature_flags FOR UPDATE USING (auth.uid() IN (SELECT id FROM profiles WHERE is_admin = true));

CREATE POLICY "Stream likes are viewable by everyone" ON stream_likes FOR SELECT USING (true);
CREATE POLICY "Authenticated users can insert their own likes" ON stream_likes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete their own likes" ON stream_likes FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Public can view chat messages" ON chat_messages FOR SELECT USING (is_deleted = FALSE);
CREATE POLICY "Authenticated users can send messages" ON chat_messages FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
CREATE POLICY "Users can moderate messages" ON chat_messages FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Public can view active emotes" ON chat_emotes FOR SELECT USING (is_active = TRUE);
CREATE POLICY "Service role can manage emotes" ON chat_emotes FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Anyone can view viewer counts" ON stream_viewer_count FOR SELECT USING (TRUE);
CREATE POLICY "System can update viewer counts" ON stream_viewer_count FOR ALL USING (TRUE);

CREATE POLICY "Users can view their own rate limits" ON chat_rate_limits FOR SELECT USING (auth.uid() IS NOT NULL AND user_id = auth.uid());
CREATE POLICY "System can manage rate limits" ON chat_rate_limits FOR ALL USING (TRUE);

-- TRIGGERS AND FUNCTIONS
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_streams_updated_at ON streams;
CREATE TRIGGER update_streams_updated_at
    BEFORE UPDATE ON streams
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE FUNCTION update_stream_like_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE streams SET like_count = like_count + 1 WHERE id = NEW.stream_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE streams SET like_count = GREATEST(like_count - 1, 0) WHERE id = OLD.stream_id;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_like_count_on_insert ON stream_likes;
CREATE TRIGGER trigger_update_like_count_on_insert
  AFTER INSERT ON stream_likes
  FOR EACH ROW
  EXECUTE FUNCTION update_stream_like_count();

DROP TRIGGER IF EXISTS trigger_update_like_count_on_delete ON stream_likes;
CREATE TRIGGER trigger_update_like_count_on_delete
  AFTER DELETE ON stream_likes
  FOR EACH ROW
  EXECUTE FUNCTION update_stream_like_count();

-- SUPABASE REALTIME PUBLICATION
DROP PUBLICATION IF EXISTS supabase_realtime;
CREATE PUBLICATION supabase_realtime;
ALTER PUBLICATION supabase_realtime ADD TABLE profiles, streams, tips, nfts, platform_fees, video_archives, system_settings, audit_logs, feature_flags, stream_likes, chat_messages, chat_emotes, stream_viewer_count, chat_rate_limits;
