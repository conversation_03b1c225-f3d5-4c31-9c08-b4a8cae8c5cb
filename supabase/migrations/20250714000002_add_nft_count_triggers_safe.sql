-- Safe migration to add NFT and TIP count triggers if they don't exist
-- This migration checks for existing triggers before creating them

-- <PERSON>reate function to update stream TIP count (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_proc
        WHERE proname = 'update_stream_tip_count'
    ) THEN
        CREATE OR REPLACE FUNCTION update_stream_tip_count()
        RETURNS TRIGGER AS $func$
        BEGIN
          IF TG_OP = 'INSERT' THEN
            UPDATE streams SET tip_count = tip_count + 1 WHERE id = NEW.stream_id;
          ELSIF TG_OP = 'DELETE' THEN
            UPDATE streams SET tip_count = GREATEST(tip_count - 1, 0) WHERE id = OLD.stream_id;
          END IF;
          RETURN NULL;
        END;
        $func$ LANGUAGE plpgsql;
    END IF;
END
$$;

-- Create function to update stream NFT count (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_proc
        WHERE proname = 'update_stream_nft_count'
    ) THEN
        CREATE OR REPLACE FUNCTION update_stream_nft_count()
        RETURNS TRIGGER AS $func$
        BEGIN
          IF TG_OP = 'INSERT' THEN
            UPDATE streams SET nft_count = nft_count + 1 WHERE id = NEW.stream_id;
          ELSIF TG_OP = 'DELETE' THEN
            UPDATE streams SET nft_count = GREATEST(nft_count - 1, 0) WHERE id = OLD.stream_id;
          END IF;
          RETURN NULL;
        END;
        $func$ LANGUAGE plpgsql;
    END IF;
END
$$;

-- Create triggers for TIP count updates (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_update_tip_count_on_insert'
    ) THEN
        CREATE TRIGGER trigger_update_tip_count_on_insert
          AFTER INSERT ON tips
          FOR EACH ROW
          EXECUTE FUNCTION update_stream_tip_count();
    END IF;
END
$$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_update_tip_count_on_delete'
    ) THEN
        CREATE TRIGGER trigger_update_tip_count_on_delete
          AFTER DELETE ON tips
          FOR EACH ROW
          EXECUTE FUNCTION update_stream_tip_count();
    END IF;
END
$$;

-- Create triggers for NFT count updates (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_update_nft_count_on_insert'
    ) THEN
        CREATE TRIGGER trigger_update_nft_count_on_insert
          AFTER INSERT ON nfts
          FOR EACH ROW
          EXECUTE FUNCTION update_stream_nft_count();
    END IF;
END
$$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_update_nft_count_on_delete'
    ) THEN
        CREATE TRIGGER trigger_update_nft_count_on_delete
          AFTER DELETE ON nfts
          FOR EACH ROW
          EXECUTE FUNCTION update_stream_nft_count();
    END IF;
END
$$;

-- Fix existing streams by recalculating their TIP and NFT counts
-- This ensures all existing streams have correct counts
UPDATE streams SET
  tip_count = (SELECT COUNT(*) FROM tips WHERE tips.stream_id = streams.id),
  nft_count = (SELECT COUNT(*) FROM nfts WHERE nfts.stream_id = streams.id)
WHERE
  tip_count != (SELECT COUNT(*) FROM tips WHERE tips.stream_id = streams.id) OR
  nft_count != (SELECT COUNT(*) FROM nfts WHERE nfts.stream_id = streams.id);
