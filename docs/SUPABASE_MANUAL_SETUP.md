# Manual Supabase Setup for bonkstream

Follow these instructions to manually set up your Supabase project for bonkstream.

## 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com/) and sign in
2. Click **New Project**
3. Select your organization or create a new one
4. Name your project (e.g., "bonkstream")
5. Set a secure database password
6. Choose a region close to your users
7. Click **Create new project**
8. Wait for your project to be provisioned (this may take a few minutes)

## 2. Set Up Database Tables and Policies

1. Once your project is ready, navigate to the **SQL Editor** tab
2. Click **New Query**
3. Copy the contents of `supabase/migrations/00001_create_tables.sql` into the query editor
4. Click **Run** to create all tables and set up RLS policies
5. Create a new query and paste the contents of `supabase/migrations/00002_sample_data.sql`
6. Click **Run** to add sample data
7. Create a new query and paste the contents of `supabase/migrations/00003_backup_procedures.sql`
8. Click **Run** to set up backup procedures

## 3. Get Your API Credentials

1. Go to the **Project Settings** (gear icon in the left sidebar)
2. Select **API** from the settings menu
3. Copy your **Project URL** and **anon/public** key
4. Add these to your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=your_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## 4. Set Up Your Admin Account

1. Go to the **Table Editor** in your Supabase dashboard
2. Click on the `profiles` table
3. Click **Insert Row**
4. Add your admin profile with these values:
   - `wallet_address`: Your Solana wallet address
   - `username`: Your preferred username
   - `is_creator`: true
   - `is_admin`: true
5. Click **Save**

## 5. Configure Realtime

To enable realtime functionality:

1. Go to **Database** > **Replication**
2. Enable the **Realtime** toggle for all tables:
   - profiles
   - streams
   - tips
   - nfts
   - platform_fees
   - video_archives
   - system_settings
   - audit_logs
   - feature_flags
   - db_backups

## 6. Enable Row Level Security

Verify all tables have RLS enabled:

1. Go to **Authentication** > **Policies**
2. Check that each table has the policies defined in the migration files
3. If any policies are missing, you can add them manually following the definitions in `00001_create_tables.sql`

## 7. Set Up Database Backups

1. Go to **Project Settings** > **Database**
2. Scroll down to the **Backups** section
3. Configure daily backups
4. For more advanced backup procedures, connect to your database directly and run the stored procedures from `00003_backup_procedures.sql`

## 8. Additional Configuration

### Enable Extensions

1. Go to **Database** > **Extensions**
2. Enable the following extensions:
   - pg_cron (for scheduled tasks)
   - pgcrypto (for UUID generation)

### Configure CORS

1. Go to **Project Settings** > **API**
2. Under **CORS (Cross-Origin Resource Sharing)**, add your frontend URL:
   - http://localhost:3000 (for development)
   - https://your-production-domain.com (for production)

## 9. Testing Your Setup

To verify your setup is working correctly:

1. Go to the **Table Editor** and check that all tables were created
2. Verify sample data was added correctly
3. Run a test query:

```sql
SELECT * FROM profiles;
```

4. Check RLS is working by making API requests with and without authentication

## 10. Connect Your Application

Update your application's environment variables:

```
NEXT_PUBLIC_SUPABASE_URL=your_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

Start your application and verify the connection is working correctly. 