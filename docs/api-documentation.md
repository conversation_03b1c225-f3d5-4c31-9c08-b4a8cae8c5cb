# bonkstream API Documentation

This document provides detailed information about the bonkstream API endpoints, Server Actions, and Web3 integration points.

## API Routes

### Authentication

#### `POST /api/auth/wallet`

Authenticate a user with their Solana wallet.

**Request:**
```json
{
  "publicKey": "string",
  "signature": "string",
  "message": "string"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "walletAddress": "string",
    "isCreator": "boolean",
    "isAdmin": "boolean"
  },
  "session": "string"
}
```

### BONK Tipping

#### `POST /api/tip`

Send BONK tokens from a viewer to a creator with platform fee.

**Request:**
```json
{
  "amount": "number",
  "creatorWalletAddress": "string",
  "streamId": "string"
}
```

**Response:**
```json
{
  "success": true,
  "transaction": {
    "signature": "string",
    "amount": "number",
    "platformFeePercentage": "number",
    "platformFeeAmount": "number",
    "creatorAmount": "number",
    "timestamp": "string"
  }
}
```

### NFT Minting

#### `POST /api/mint-moment`

Mint an NFT from a stream moment.

**Request:**
```json
{
  "streamId": "string",
  "timestamp": "number",
  "creatorWalletAddress": "string",
  "metadataName": "string",
  "metadataDescription": "string"
}
```

**Response:**
```json
{
  "success": true,
  "nft": {
    "mintAddress": "string",
    "metadataUri": "string",
    "transactionSignature": "string",
    "platformFeeAtMint": "number"
  }
}
```

### Admin API Endpoints

#### `GET /api/admin/platform-fee`

Get the current platform fee percentage.

**Response:**
```json
{
  "percentage": "number",
  "effectiveFrom": "string",
  "changedBy": "string"
}
```

#### `POST /api/admin/platform-fee`

Update the platform fee percentage (Admin only).

**Request:**
```json
{
  "percentage": "number",
  "justification": "string"
}
```

**Response:**
```json
{
  "success": true,
  "fee": {
    "percentage": "number",
    "effectiveFrom": "string",
    "changedBy": "string"
  }
}
```

#### `GET /api/admin/users`

Get a list of users with pagination and filtering (Admin only).

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `role`: Filter by role (viewer, creator, admin)
- `search`: Search term for username or wallet address

**Response:**
```json
{
  "users": [
    {
      "id": "uuid",
      "walletAddress": "string",
      "username": "string",
      "isCreator": "boolean",
      "isAdmin": "boolean",
      "createdAt": "string"
    }
  ],
  "total": "number",
  "page": "number",
  "limit": "number"
}
```

#### `GET /api/admin/storage/stats`

Get storage usage statistics (Admin only).

**Response:**
```json
{
  "primaryStorage": {
    "provider": "string",
    "totalSize": "number",
    "streamCount": "number",
    "costEstimate": "number"
  },
  "archivalStorage": {
    "provider": "string",
    "totalSize": "number",
    "streamCount": "number",
    "costEstimate": "number"
  },
  "pendingArchival": "number"
}
```

#### `POST /api/admin/storage/archive`

Trigger archival process for streams (Admin only).

**Request:**
```json
{
  "streamIds": ["string"]
}
```

**Response:**
```json
{
  "success": true,
  "archiveJobs": [
    {
      "streamId": "string",
      "status": "string"
    }
  ]
}
```

#### `GET /api/admin/analytics/revenue`

Get platform revenue data (Admin only).

**Query Parameters:**
- `period`: Time period (day, week, month, year)
- `start`: Start date (YYYY-MM-DD)
- `end`: End date (YYYY-MM-DD)

**Response:**
```json
{
  "totalRevenue": "number",
  "platformFees": "number",
  "creatorPayouts": "number",
  "transactions": "number",
  "timeData": [
    {
      "date": "string",
      "revenue": "number"
    }
  ]
}
```

## Server Actions

### Profile Management

```typescript
'use server'

// Update user profile
export async function updateProfile(formData: FormData) {
  // Implementation details
}

// Get user profile
export async function getProfile(userId: string) {
  // Implementation details
}
```

### Stream Metadata

```typescript
'use server'

// Create new stream
export async function createStream(formData: FormData) {
  // Implementation details
}

// Get stream details
export async function getStreamDetails(streamId: string) {
  // Implementation details
}
```

### Platform Fee Management

```typescript
'use server'

// Update platform fee (admin only)
export async function updatePlatformFee(formData: FormData) {
  // Implementation details
}

// Get platform fee history
export async function getPlatformFeeHistory() {
  // Implementation details
}
```

### Admin Dashboard Actions

```typescript
'use server'

// Get admin dashboard metrics
export async function getAdminDashboardMetrics() {
  // Implementation details
}

// Update user role (admin only)
export async function updateUserRole(userId: string, role: string) {
  // Implementation details
}
```

## Web3 Integration

### Wallet Connection

The application uses the Solana Wallet Adapter to connect with Phantom and Backpack wallets:

```tsx
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';

function AuthButton() {
  const { publicKey, connected } = useWallet();
  
  useEffect(() => {
    if (connected && publicKey) {
      // User is authenticated
      createOrGetUserProfile(publicKey.toString());
    }
  }, [connected, publicKey]);
  
  return <WalletMultiButton />;
}
```

### BONK Token Transfers

BONK token transfers are implemented using the SPL Token program:

```typescript
import { createTransferInstruction } from '@solana/spl-token';

// For sending BONK tips with platform fee
async function sendBonkTip(amount, fromWallet, creatorWallet) {
  // Calculate fee split
  const { platformFee, creatorAmount } = calculatePlatformFee(
    amount * Math.pow(10, BONK_DECIMALS)
  );
  
  // Create multi-instruction transaction
  const tx = new Transaction()
    .add(
      // Send to creator
      createTransferInstruction(
        fromWallet,
        creatorWallet,
        wallet.publicKey,
        creatorAmount
      )
    )
    .add(
      // Send platform fee
      createTransferInstruction(
        fromWallet,
        PLATFORM_WALLET_ADDRESS,
        wallet.publicKey,
        platformFee
      )
    );
  
  // Sign and send transaction
  const signature = await sendTransaction(tx, connection);
  
  // Return both the transaction signature and the fee information
  return {
    signature,
    platformFeePercentage: getPlatformFeePercentage(),
    platformFeeAmount: platformFee,
    creatorAmount
  };
}
```

### NFT Minting

NFT minting is implemented using the Metaplex JS SDK:

```typescript
import { Metaplex } from '@metaplex-foundation/js';

async function mintNftMoment(metadataUri, name) {
  const { nft } = await metaplex.nfts().create({
    uri: metadataUri,
    name: name,
    sellerFeeBasisPoints: 500, // 5% royalties
  });
  
  return nft.address.toString();
}
```

## Error Handling

All API endpoints follow a consistent error handling pattern:

```json
{
  "success": false,
  "error": {
    "code": "string",
    "message": "string",
    "details": {}
  }
}
```

Common error codes:
- `AUTH_REQUIRED`: Authentication required
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `INVALID_INPUT`: Invalid request data
- `BLOCKCHAIN_ERROR`: Error in blockchain interaction
- `NOT_FOUND`: Resource not found
- `RATE_LIMITED`: Too many requests
- `SERVER_ERROR`: Internal server error

## Rate Limiting

All API endpoints are protected with rate limiting:

- Public endpoints: 60 requests per minute
- Authenticated endpoints: 120 requests per minute
- Admin endpoints: 240 requests per minute

## Authentication and Authorization

The application uses a combination of Web3 wallet authentication and role-based access control:

1. Users connect their Solana wallet (Phantom/Backpack)
2. The application verifies the wallet signature
3. User roles (viewer, creator, admin) determine access permissions
4. Admin endpoints require the `isAdmin` flag on the user profile

## Database Integration

All API endpoints interact with the Supabase PostgreSQL database using Row Level Security (RLS) policies for secure data access.

See the [Schema Design](./schema-design.md) document for database structure details. 