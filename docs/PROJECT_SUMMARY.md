# bonkstream

## Project Overview
- **Web3-native streaming platform** combining TikTok-style UX with Solana blockchain integration
- **Stream-only platform** for recorded content with BONK token tipping and NFT minting
- **Creator-focused** with transparent monetization and decentralized ownership

## Problem Statement & Solution

### The Creator Economy Crisis

#### Current Pain Points
- **Exploitative Platform Fees**: Traditional platforms like TikTok take up to 80% of creator earnings through opaque fee structures
- **Algorithmic Manipulation**: Creators' content visibility controlled by black-box algorithms that favor paid promotion
- **Advertisement Dependency**: Revenue tied to intrusive ads that degrade user experience and creator autonomy
- **Middleman Control**: Centralized platforms act as gatekeepers, controlling creator-audience relationships
- **Delayed & Uncertain Payouts**: Complex payment systems with delayed settlements and hidden fees
- **No True Ownership**: Creators have no real control over their content, audience, or earnings

#### The Transparency Problem
- **Hidden Fee Structures**: Platforms obscure how much creators actually receive
- **Algorithm Black Box**: No visibility into how content is distributed or promoted
- **Centralized Decision Making**: Platform policies change without creator input
- **Data Ownership**: Platforms own all creator and audience data

### Our Solution: True Creator Empowerment

#### Direct Value Transfer
- **No Middleman**: BONK tokens go directly from viewer to creator wallet
- **Transparent Fees**: Only 5% platform fee, clearly displayed to all parties
- **Instant Settlements**: Immediate token transfer upon tip confirmation
- **Real-time Tracking**: Complete transaction history on Solana blockchain

#### Algorithmic Freedom
- **No Algorithm Manipulation**: Content discovery based on genuine engagement, not paid promotion
- **Equal Visibility**: All creators have fair opportunity for content discovery
- **Community-Driven Curation**: Users control what they see through direct following and engagement
- **Merit-Based Growth**: Success determined by content quality and audience value

#### Advertisement-Free Experience
- **No Intrusive Ads**: Clean viewing experience focused on creator content
- **Direct Monetization**: Creators earn directly from their audience, not advertiser budgets
- **Authentic Engagement**: Viewer interactions driven by genuine interest, not ad interruptions
- **Creator Control**: Full autonomy over monetization strategies and audience relationships

### Why This Benefits Everyone

#### For Creators
- **Maximum Earnings**: Keep 95-98% of tips vs. 20% on traditional platforms
- **Transparent Revenue**: Real-time earnings tracking with blockchain verification
- **True Ownership**: Own your content, audience relationships, and earnings data
- **Predictable Income**: Direct fan support independent of algorithm changes
- **Global Reach**: Borderless payments with no currency conversion fees
- **NFT Opportunities**: Additional revenue through timestamp moment minting

#### For Viewers
- **Ad-Free Experience**: Pure content consumption without interruptions
- **Direct Impact**: Know exactly how much support reaches your favorite creators
- **Unique Collectibles**: Own NFTs from memorable stream moments
- **Fair Discovery**: Find content based on quality, not advertising spend
- **Blockchain Ownership**: True digital ownership of tips sent and NFTs collected
- **Community Building**: Direct connection with creators

#### For the Platform
- **Sustainable Model**: Lower fees create higher volume and creator satisfaction
- **Community Growth**: Transparent practices build trust and organic adoption
- **Innovation Focus**: Resources dedicated to features, not advertisement optimization
- **Decentralized Foundation**: Future-proof architecture aligned with Web3 principles
- **Creator Loyalty**: Fair treatment creates long-term platform advocacy

### The Decentralization Advantage

#### No Central Authority Control
- **Creator Independence**: No single entity can demonetize or shadow-ban creators
- **Censorship Resistance**: Content Metadata stored on decentralized networks (Arweave)
- **Open Source**: Core protocols can be audited and improved by anyone

#### Blockchain Transparency
- **Public Transaction Records**: All tips and fees verifiable on Solana blockchain
- **Immutable History**: Cannot manipulate or hide transaction history
- **Smart Contract Automation**: Fee splits executed automatically without human intervention
- **Audit Trail**: Complete platform activity auditable by creators and community

## Core Features

### User Features
- **Web3 Wallet Authentication** (Phantom & Backpack support)
- **TikTok-style Stream Viewing** with vertical scrolling feed
- **BONK Token Tipping** with transparent platform fee (5%)
- **NFT Moment Minting** from specific stream timestamps
- **Multi-language Support** (English/German with next-intl)
- **Mobile-responsive Design** with modern UI/UX

### Creator Features
- **Creator Dashboard** with earnings overview and analytics
- **Stream Management** with recorded streams and metadata editing
- **Tip History** with platform fee breakdown
- **NFT Mint Tracking** for monetized stream moments

### Admin Features
- **Platform Fee Management** (5% adjustable with audit trail)
- **Storage Management** with archival controls
- **Analytics Dashboard** with revenue and user metrics

## Technical Architecture

### Frontend & Backend
- **Framework**: Next.js 15.3.1 with App Router
- **Language**: TypeScript with strict typing
- **Styling**: Tailwind CSS 4.x + shadcn/ui components
- **State Management**: React Context + Server Components
- **Authentication**: Hybrid wallet + signature-based auth

### Blockchain Integration
- **Network**: Solana mainnet
- **Token**: BONK SPL Token for all transactions
- **NFT Framework**: Metaplex for minting and metadata
- **Wallet Adapters**: Multi-wallet support with persistent sessions
- **Signature Verification**: nacl + bs58 for security

### Database & Storage
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Video Storage**: Multi-tier system (Livepeer → Arweave Metadata archival)
- **File Storage**: Supabase Storage for assets and avatars
- **Security**: RLS policies, audit logging, GDPR compliance

### Key Libraries & Tools
- **UI**: @radix-ui primitives with custom theming
- **Validation**: Zod schemas for type-safe API calls
- **Video**: React Player with custom controls
- **Animation**: Framer Motion for smooth interactions
- **Internationalization**: next-intl with locale routing

## Monetization Strategy

### Revenue Streams
- **Platform Fees**: 5% commission on all BONK tips (adjustable)
- **NFT Minting Fees**: Small fee for timestamp NFT creation
- **Premium Features**: Future subscription tiers for creators, enabling recurring income streams and allowing viewers to access past (ended) recorded streams
- **Storage Fees**: Potential fees for extended archival storage, supporting long-term access to recorded content and additional creator monetization

### Fee Structure
- **Transparent Fees**: All platform fees clearly displayed to users
- **Creator-friendly**: Low fees to encourage creator adoption
- **Adjustable Rates**: Admin-controlled fee percentages with audit trail
- **Token-based**: All transactions in BONK tokens for ecosystem growth

## Business Model

### Target Market
- **Primary**: Web3-native content creators and crypto enthusiasts
- **Secondary**: Traditional streamers looking for Web3 monetization
- **Tertiary**: NFT collectors and Solana ecosystem participants

### Growth Strategy
- **Creator Incentives**: Low platform fees and transparent earnings
- **Community Building**: Integration with BONK and Solana communities
- **Viral Features**: Shareable NFT moments and social features
- **Partnership Opportunities**: Collaboration with Solana projects

### Competitive Advantages
- **Web3-native**: Built for blockchain from ground up
- **BONK Integration**: Leverage popular meme coin ecosystem
- **NFT Innovation**: Unique timestamp-based NFT minting
- **Transparent Economics**: Open fee structure and creator earnings

### Success Metrics
- **Creator Adoption**: Number of active creators and content
- **Transaction Volume**: BONK tips and NFT sales
- **User Engagement**: Stream views and interaction rates
- **Revenue Growth**: Platform fee collection and sustainability

## User Journey Flows & Feature Specifications

### New User Onboarding Flow

#### Viewer Journey (5-7 minutes)
1. **Landing Page** → Discover trending streams without registration
2. **Wallet Connection** → Connect Phantom/Backpack (optional for viewing)
3. **Profile Creation** → Auto-generate profile on first wallet connection
4. **BONK Acquisition** → Guide to acquire BONK tokens for tipping
5. **First Tip** → Tutorial overlay for tipping process
6. **NFT Discovery** → Introduction to timestamp NFT minting

#### Creator Journey (15-20 minutes)
1. **Creator Registration** → Enhanced profile setup with verification
2. **Stream Setup** → Start live stream
3. **Monetization Config** → Set tip goals and NFT pricing
4. **Dashboard Tour** → Analytics, earnings, and management tools
5. **First Payout** → BONK token withdrawal to personal wallet

### Core Feature Specifications

#### Stream Viewing Interface
- **Vertical Feed**: TikTok-style infinite scroll with 9:16 aspect ratio
- **Gesture Controls**: Swipe up/down for navigation, double-tap for like
- **Live Indicators**: Real-time viewer count and engagement metrics
- **Interactive Elements**: Tip button, NFT mint button, share options
- **Quality Selection**: Auto-adjust based on connection (360p-4K)
- **Accessibility**: Screen reader support, keyboard navigation

#### Tipping System
- **Quick Amounts**: Preset tip amounts (10, 50, 100, 500 BONK)
- **Custom Amounts**: Manual input
- **Fee Transparency**: Clear breakdown of platform fee vs. creator amount
- **Transaction Confirmation**: Multi-step confirmation with gas fee display
- **Receipt Generation**: Downloadable transaction receipts
- **Tip History**: Complete history with Solana Explorer links

#### NFT Minting Flow
- **Preview Generation**: Auto-generate thumbnail from selected moment
- **Collection Management**: Personal NFT gallery

## Future Integrations
- **Staking Rewards**: Reduced fees for token holders
- **Creator Rewards Program**: Token rewards for top-performing creators
- **Viewer Subscriptions**: Viewers can subscribe to access past recorded streams, enabling recurring revenue for creators and recorded content access for fans
- **Rarity Tiers**: NFT Common/Rare/Epic based on stream popularity
