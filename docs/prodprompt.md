Review the code quality across the project. Look for:
•⁠  ⁠Proper error handling and null safety
•⁠  ⁠Memory leaks or performance issues
•⁠  ⁠Hardcoded values that should be in config files
•⁠  ⁠TODO or FIXME comments that indicate unfinished work
•⁠  ⁠Deprecated APIs or packages
•⁠  ⁠Code duplication that should be refactored

also
  - Validate all authenticated endpoints
  - Review wallet integration for security issues
  - Secure admin API endpoints
  - are authentication checks to all `/api/admin/*` routes added?
  - check if all apis are rate limiting among abusive usage or attacks

Check for security issues:
•⁠  ⁠Exposed API keys or secrets in the code
•⁠  ⁠Proper HTTPS usage for all API calls
•⁠  ⁠Input validation and sanitization
•⁠  ⁠Secure storage of sensitive data
•⁠  ⁠Authentication token handling"

Analyze the state management approach. Is it consistent throughout the app? Check for:
•⁠  ⁠Proper separation of business logic and UI
•⁠  ⁠Clean architecture implementation
•⁠  ⁠Dependency injection setup
•⁠  ⁠Proper use of providers/bloc/riverpod (whatever they're using)"


•⁠  ⁠Proper logging implementation (not console.log everywhere)
Check for performance optimizations:
•⁠  ⁠Unnecessary rebuilds

Give me a production readiness report with:
1.⁠ ⁠Critical issues that MUST be fixed before production
2.⁠ ⁠Important issues that SHOULD be fixed
3.⁠ ⁠Nice-to-have improvements
4.⁠ ⁠Overall assessment: Is this code production-ready?"