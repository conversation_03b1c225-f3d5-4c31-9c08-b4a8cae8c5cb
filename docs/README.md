# bonkstream

A Web3-native, stream-only platform where users watch recorded streams, tip creators with BONK tokens, and mint NFTs as proof-of-support from stream moments.

## Key Features

- Web3 wallet authentication (Phantom/Backpack)
- TikTok-style stream viewing interface
- B<PERSON><PERSON> tipping with transparent platform fee (2-5%)
- NFT moment minting from timestamps
- Creator earnings dashboard
- Admin dashboard (fee/user/storage management)
- GDPR compliance
- Multi-tier video storage (Mux/Livepeer → Arweave)

## Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/bonkstream.git
cd bonkstream
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set Up Supabase Database

We provide an automated script to set up your Supabase project, including creating tables, configuring Row Level Security policies, and setting up database backup procedures:

```bash
npm run setup-db
```

The script will guide you through:
- Creating a new Supabase project
- Choosing a region
- Applying all database migrations
- Setting up initial data
- Saving Supabase credentials to `.env.local`

If you prefer a manual setup, see detailed instructions in [supabase/README.md](supabase/README.md).

### 4. Configure Environment Variables

Create a `.env.local` file in the root directory with the following variables (the Supabase values will be populated by the setup script):

```
# Supabase Configuration 
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Solana Configuration
NEXT_PUBLIC_SOLANA_RPC=https://api.mainnet-beta.solana.com
NEXT_PUBLIC_BONK_MINT=DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263
NEXT_PUBLIC_PLATFORM_FEE_PERCENTAGE=2.5
PLATFORM_WALLET_ADDRESS=your_platform_wallet_address

# Video Storage Configuration
MUX_TOKEN_ID=your_mux_token_id
MUX_TOKEN_SECRET=your_mux_token_secret

# Additional Configuration
# Add any additional environment variables as needed
```

### 5. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the app.

### 6. Build for Production

```bash
npm run build
npm start
```

## Project Structure

```
├── app/                     # Next.js app directory
│   ├── page.tsx            # Landing page
│   ├── stream/[id]/        # Stream viewing pages
│   ├── creator/dashboard/  # Creator dashboard
│   ├── admin/              # Admin dashboard
│   ├── legal/              # Legal pages (GDPR)
│   ├── api/                # API Routes
├── components/             # React components
├── lib/                    # Utility functions
│   ├── supabase/           # Supabase client and helpers
│   ├── solana.ts           # Solana connection
│   ├── bonk.ts             # BONK token logic
│   ├── nft.ts              # NFT minting logic
├── public/                 # Static assets
├── supabase/               # Supabase database setup
│   ├── migrations/         # SQL migration files
└── scripts/                # Utility scripts
```

## Database Schema

Our Supabase PostgreSQL database includes the following tables:

- `profiles`: User profiles with wallet addresses and roles
- `streams`: Stream metadata and storage information
- `tips`: BONK tip transactions with platform fee details
- `nfts`: Minted NFT metadata and ownership
- `platform_fees`: Fee percentage history with audit trail
- `video_archives`: Video archival status and locations
- `system_settings`: System configuration with caching
- `audit_logs`: Comprehensive logging for admin actions
- `feature_flags`: Feature toggle system
- `db_backups`: Database backup logs

All tables have Row Level Security enabled with appropriate policies.

## Row Level Security (RLS) Issue

If you encounter the error message:
```
"new row violates row-level security policy for table 'profiles'"
```

This means you need to add an INSERT policy to the profiles table in your Supabase project:

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Run the following SQL:

```sql
-- Add INSERT policy for profiles table
-- This policy allows anyone to create a profile
CREATE POLICY "Anyone can create a profile" 
ON profiles 
FOR INSERT 
WITH CHECK (true);
```

This enables creating new profiles when wallet connections occur.


# 🚀 bonkstream — BONK Hackathon Project (Germany)

## 🧠 Why This Exists

I joined the BONK Hackathon in Germany to reimagine how creators get rewarded — without platforms like TikTok taking a cut, pushing ads, or controlling reach through opaque algorithms.

**bonkstream** is a Web3-native, stream-only platform where:
- Viewers **tip creators using BONK**
- Tips come with **NFTs as proof-of-support**
- There are **no uploads** — only recorded streams
- A small **platform fee (2-5%)** ensures sustainability

---

## 🔥 MVP Concept

> A decentralized TikTok-style platform for *recorded-only* streaming content — no uploads, no ads. Viewers tip in BONK and mint NFT "moments" from the stream.

### Core User Value:
- **Creators** earn 95-98% of their BONK tips
- **Viewers** collect stream-based NFTs that prove support
- **Transparent platform fee** (2-5%) with no hidden costs
- **GDPR compliant** for the European market

---

## 🛠️ Tech Stack

| Layer       | Tech Used |
|-------------|-----------|
| **Frontend** | Next.js 15.2.4 (App Router) with Server Components |
| **Styling** | Tailwind CSS 4.x + shadcn/ui components |
| **Wallets** | Phantom / Backpack via Solana Wallet Adapter |
| **Blockchain** | Solana (no Anchor for MVP) |
| **NFTs** | Metaplex JS SDK + Arweave |
| **Tokens** | BONK (SPL Token) |
| **Streaming** | Mux or Livepeer (recorded only) |
| **Database** | Supabase PostgreSQL |
| **Storage** | Multi-tier: Mux/Livepeer + Arweave for archival |

---

## ✅ MVP Features

- [x] Web3 authentication with Phantom/Backpack wallet
- [x] View recorded streams with TikTok-style interface
- [x] Tip creators with BONK (95-98% to creator, 2-5% platform fee)
- [x] Mint NFT moments (timestamp + metadata)
- [x] Creator dashboard with BONK earnings and analytics
- [x] Comprehensive admin dashboard for platform management
- [x] Multi-tier video storage with archival system
- [x] GDPR compliance for European market
- [ ] NFT marketplace to buy/sell "moments" (v2)


---


## 👥 User Flow

### Viewer

1. Connect Phantom/Backpack wallet
2. Browse and watch streams in TikTok-style interface
3. Tip creator with BONK (transparent fee display)
4. Mint a timestamped NFT moment as proof-of-support
5. View minted NFTs in profile collection

### Creator

1. Connect wallet and create profile
2. Set up stream via streaming credentials
3. Access dashboard to view:
   - BONK earnings (before and after platform fee)
   - Tip history and analytics
   - NFT mint activity
   - Stream performance metrics

### Admin

1. Access admin dashboard with wallet authentication
2. Manage platform fee percentage (2-5% range)
3. View user and creator analytics
4. Monitor storage usage and archival status
5. Generate reports and analytics

---

## 📜 API Structure

### API Routes (for Web3 integration)

| Endpoint                  | Method | Description                           |
| ------------------------- | ------ | ------------------------------------- |
| `/api/tip`                | POST   | Send BONK from viewer to creator      |
| `/api/mint-moment`        | POST   | Mint NFT for a specific stream moment |
| `/api/admin/platform-fee` | POST   | Update platform fee percentage        |

### Server Actions (for database operations)

- Profile management
- Stream metadata handling
- Tip history and analytics
- Admin dashboard operations
- Platform fee management

---

## ✅ QA Checklist

* [ ] Wallet connection works with multiple providers
* [ ] Platform fee (2-5%) is clearly displayed before transactions
* [ ] BONK tips transfer correctly with fee split
* [ ] NFT mints with complete metadata including fee information
* [ ] Creator dashboard displays earnings before/after fee
* [ ] Admin dashboard allows platform fee management
* [ ] GDPR compliance features function properly
* [ ] Video storage tiers function as expected

---

## 🪄 Future Features (v2)

* [ ] NFT marketplace for buying/selling "moments"
* [ ] Creator subscriptions with BONK
* [ ] Enhanced analytics dashboard
* [ ] Stream scheduling and notifications
* [ ] Mobile app development
* [ ] Multi-chain support beyond Solana
* [ ] Tiered fee structure for high-volume creators

---

## 📈 Success Metrics

* Number of active creators
* Total BONK transaction volume
* Average tips per stream
* Number of NFT moments minted
* User retention and engagement
* Platform growth metrics
* Creator earnings statistics
* Platform fee revenue

---

## 📍 Summary

bonkstream is a revolutionary Web3-native streaming platform that connects creators directly with their audience through BONK tips and NFT moments. With a transparent platform fee of 2-5%, the platform ensures sustainability while providing creators with 95-98% of all tips. The comprehensive admin dashboard enables efficient platform management, while GDPR compliance ensures accessibility in the European market.

Built with Next.js 15.2.4, Solana, and a modern tech stack, bonkstream offers a powerful solution for decentralized content monetization.

```

A serialized transaction (as a Buffer)
Platform fee percentage: 2.5%
Platform fee amount: 250000 (which is 2.5 BONK given the 5 decimal places)
Creator amount: 9750000 (which is 97.5 BONK)
This shows that for a 100 BONK tip, the platform takes 2.5 BONK and the creator gets 97.5 BONK, which is correct.