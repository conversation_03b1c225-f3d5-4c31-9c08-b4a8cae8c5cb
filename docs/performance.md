# bonkstream Performance Analysis & Optimization Guide

## Overview

This document outlines performance issues identified in the bonkstream application and provides recommendations for optimization. The analysis covers client-side rendering, server-side operations, database queries, and real-time features.

## 🔴 Critical Performance Issues

### 1. Excessive Polling & Intervals

#### Problem Description

Multiple components are running aggressive polling intervals that create unnecessary load:

- **BONK Price Updates**: Every 30 seconds across all page loads (`bonk-price-card.tsx:174`)
- **Header Broadcasting Status**: Every 30 seconds when wallet connected (`Header.tsx:159`)  
- **Live Stream Status**: Every 10 seconds for live streams (`go-live/page.tsx:280`, `stream/[id]/page.tsx:209`)
- **Viewer Heartbeat**: Every 30 seconds per viewer (`useViewerTracking.ts:74`)
- **Stream Status Monitoring**: Every 15 seconds (`LiveStream.tsx:102`)
- **Live Streams Discovery**: Every 60 seconds (`discover/page.tsx:196`)

#### Performance Impact

- High server load with multiple concurrent users
- Unnecessary bandwidth consumption  
- Poor user experience on slower connections
- Increased API costs

#### Solution

```typescript
// Current intervals (too aggressive)
setInterval(fetchBonkPrice, 30000);        // 30s
setInterval(checkBroadcastingStatus, 30000); // 30s
setInterval(checkStreamStatus, 10000);       // 10s

// Recommended intervals
setInterval(fetchBonkPrice, 300000);         // 5 minutes
setInterval(checkBroadcastingStatus, 120000); // 2 minutes
setInterval(checkStreamStatus, 30000);        // 30s for live only
```

### 2. Database N+1 Query Problem

#### Problem Description

The streams API makes separate database queries for each stream to get tip and NFT counts:

```typescript
// In /api/streams/route.ts - lines 73-95
const streams = await Promise.all(data.map(async (stream) => {
  // Separate query for each stream
  const { count: actualTipCount } = await supabase
    .from('tips')
    .select('*', { count: 'exact', head: true })
    .eq('stream_id', stream.id);

  const { count: actualNftCount } = await supabase
    .from('nfts')
    .select('*', { count: 'exact', head: true })
    .eq('stream_id', stream.id);
}));
```

#### Performance Impact

- N+1 database queries (1 main query + N queries for counts)
- Slow API response times
- High database load
- Poor scalability

#### Solution

```sql
-- Add aggregated columns to streams table
ALTER TABLE streams ADD COLUMN tip_count INTEGER DEFAULT 0;
ALTER TABLE streams ADD COLUMN nft_count INTEGER DEFAULT 0;

-- Create triggers to update counts automatically
CREATE OR REPLACE FUNCTION update_stream_tip_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE streams SET tip_count = (
    SELECT COUNT(*) FROM tips WHERE stream_id = COALESCE(NEW.stream_id, OLD.stream_id)
  ) WHERE id = COALESCE(NEW.stream_id, OLD.stream_id);
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;
```

### 3. Inefficient Supabase Client Management

#### Problem Description

Complex singleton pattern with unnecessary promise handling and potential memory leaks in `supabase/server.ts` lines 39-156.

#### Performance Impact

- Memory leaks from uncleaned promises
- Connection pool exhaustion
- Inconsistent client state
- Poor error handling

#### Solution

```typescript
// Simplified client creation
let supabaseClient: SupabaseClient | null = null;

export async function createClient(): Promise<SupabaseClient> {
  if (supabaseClient) return supabaseClient;
  
  const cookieStore = await cookies();
  supabaseClient = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    { cookies: { /* simplified cookie handling */ } }
  );
  
  return supabaseClient;
}
```

### 4. Memory Leaks & Resource Issues

#### Problem Description

Multiple components have interval/subscription leaks:

- Uncleaned setInterval calls in various components
- Media stream tracks not properly stopped
- Supabase real-time subscriptions not always cleaned up
- Event listeners not removed on unmount

#### Performance Impact

- Browser memory usage grows over time
- Poor mobile performance
- WebRTC connection leaks
- Degraded user experience

#### Solution

```typescript
// Proper cleanup pattern
useEffect(() => {
  const interval = setInterval(callback, delay);
  
  return () => {
    clearInterval(interval);
    // Clean up any other resources
  };
}, [dependencies]);
```

## 🟡 Moderate Performance Issues

### 5. Redundant API Calls

#### Problems

- Multiple components fetch same profile data independently
- Stream page makes sequential API calls instead of batching
- Viewer tracking creates duplicate sessions

#### Solutions

- Implement SWR or React Query for caching
- Batch API calls where possible
- Use global state for shared data

### 6. Real-time Chat Performance

#### Problems

- Loads 100 messages on every chat initialization
- No message pagination
- Heavy real-time subscription overhead

#### Solutions

- Implement message pagination (load 20, fetch more on scroll)
- Cache recent messages client-side
- Optimize Supabase subscription patterns

### 7. Poor Caching Strategies

#### Problems

- No request caching on API routes
- Client-side state not persisted
- No CDN for static assets

#### Solutions

```typescript
// Add caching headers to API routes
export async function GET() {
  const data = await fetchData();
  
  return NextResponse.json(data, {
    headers: {
      'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=300'
    }
  });
}
```

## 🟢 Minor Performance Issues

### 8. Bundle Size Optimization

#### Problems

- Heavy TradingView widget loaded on every page
- Large component bundles
- Limited use of dynamic imports

#### Solutions

```typescript
// Lazy load heavy components
const TradingViewWidget = lazy(() => import('./TradingViewWidget'));
const AdminDashboard = lazy(() => import('./AdminDashboard'));

// Use dynamic imports for conditional features
const { heavy } = await import('./heavy-feature');
```

### 9. Image & Video Optimization

#### Problems

- No optimization for avatar/thumbnail loading
- Video streams not properly cleaned up
- No lazy loading for images

#### Solutions

- Use Next.js Image component everywhere
- Implement proper video cleanup
- Add intersection observer for lazy loading

## 📊 Performance Metrics & Monitoring

### Key Metrics to Track

1. **API Response Times**
   - Target: < 200ms for most endpoints
   - Current: Some endpoints > 1s due to N+1 queries

2. **Database Query Performance**
   - Target: < 50ms average query time
   - Current: Slow due to missing indexes

3. **Client-Side Memory Usage**
   - Target: Stable memory usage over time
   - Current: Memory leaks from intervals

4. **Bundle Size**
   - Target: < 500KB initial bundle
   - Current: Heavy due to TradingView widget

### Monitoring Tools

- Add performance monitoring to critical API routes
- Implement client-side error tracking
- Monitor database query performance
- Track bundle size in CI/CD

## 🔧 Implementation Priority

### Phase 1 (Immediate - High Impact)

1. Reduce polling intervals by 50-75%
2. Fix N+1 database queries in streams API
3. Clean up memory leaks in components
4. Add proper cleanup to all useEffect hooks

### Phase 2 (Short-term - Medium Impact)

1. Implement request caching
2. Optimize database queries with indexes
3. Add React Query for client-side caching
4. Batch API operations

### Phase 3 (Long-term - Scalability)

1. Implement CDN for static assets
2. Add database connection pooling
3. Optimize bundle splitting
4. Add comprehensive monitoring

## 📈 Expected Performance Improvements

After implementing these optimizations:

- **API Response Time**: 60-80% improvement
- **Database Load**: 70% reduction in query count
- **Client Memory Usage**: Stable, no leaks
- **Server Load**: 50-60% reduction in requests
- **User Experience**: Faster page loads, smoother interactions

## 🛠️ Tools & Technologies

### Recommended Performance Tools

- **React DevTools Profiler**: Component performance analysis
- **Lighthouse**: Core Web Vitals monitoring
- **SWR/React Query**: Client-side caching
- **Vercel Analytics**: Real-time performance metrics

### Database Optimization

- **pg_stat_statements**: Query performance monitoring
- **Database indexes**: On frequently queried columns
- **Connection pooling**: via Supabase or external pool

## 🔍 Monitoring & Alerting

### Performance Alerts

- API response time > 500ms
- Database query time > 100ms
- Error rate > 1%
- Memory usage growth > 50MB/hour

### Regular Performance Reviews

- Weekly API performance review
- Monthly bundle size analysis
- Quarterly database optimization review
- User experience metrics tracking

---

*Last updated: July 2025*  
*Next review: Monthly*
