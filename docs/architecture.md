# bonkstream Architecture Documentation

This document outlines the technical architecture of the bonkstream platform, including system components, data flow, and integration points.

## System Architecture Overview

```
┌────────────────┐      ┌───────────────────┐      ┌──────────────────┐
│                │      │                   │      │                  │
│  Next.js App   │◄────►│  Supabase         │◄────►│  Solana          │
│  (Frontend +   │      │  (Database +      │      │  Blockchain      │
│   Backend)     │      │   Authentication) │      │                  │
│                │      │                   │      │                  │
└────────┬───────┘      └────────┬──────────┘      └─────────┬────────┘
         │                       │                           │
         │                       │                           │
         ▼                       ▼                           ▼
┌────────────────┐      ┌───────────────────┐      ┌──────────────────┐
│                │      │                   │      │                  │
│  Video Storage │      │  IPFS             │      │  BONK Token      │
│  (Livepeer)│   │      │  (Metadata)       │      │  (SPL Token)     │
│                │      │                   │      │                  │
│                │      │                   │      │                  │
└────────────────┘      └───────────────────┘      └──────────────────┘
```

## Component Breakdown

### 1. Next.js Application (Frontend + Backend)

The application is built using Next.js 15.2.4 with the App Router architecture, offering both client and server components.

#### Key Components:

- **App Router**: `/app` directory with nested routing
- **API Routes**: `/app/api` endpoints for Web3 interactions
- **Server Actions**: Database operations and form handling
- **React Components**: UI elements for stream viewing, wallet connection, etc.
- **Middleware**: Authentication and route protection

#### Frontend Technology Stack:

- **UI Components**: shadcn/ui + Tailwind CSS 4.x
- **Wallet Integration**: Solana Wallet Adapter (Phantom/Backpack)
- **Video Player**: React Player with custom controls
- **State Management**: React Context + Server Components
- **Forms**: React Hook Form + Zod validation

### 2. Supabase Integration (Database + Authentication)

Supabase provides PostgreSQL database with Row Level Security (RLS) policies for secure data access.

#### Database Tables:

- `profiles`: User profiles and wallet addresses
- `streams`: Stream metadata and storage information
- `tips`: BONK tip transactions with platform fee records
- `nfts`: Minted NFT metadata and ownership records
- `platform_fees`: Fee percentage history with audit trail
- `video_archives`: Video archival status and locations

#### Security Features:

- Row Level Security (RLS) policies for each table
- Role-based access control via user profiles
- Secure API access with anon and service keys

### 3. Solana Blockchain Integration

The application interacts with the Solana blockchain for wallet authentication, BONK token transfers, and NFT minting.

#### Blockchain Operations:

- **Wallet Authentication**: Verify wallet ownership via signatures
- **BONK Transfers**: Send tokens from viewer to creator + platform fee
- **NFT Minting**: Create NFTs for stream moments with Metaplex

#### Technology Integration:

- **@solana/web3.js**: Core Solana blockchain interactions
- **@solana/spl-token**: BONK token transfers
- **@metaplex-foundation/js**: NFT creation and metadata
- **Custom Fee Logic**: Platform fee split implementation (2-5%)

### 4. Video Storage System

Multi-tier storage approach for optimal performance and cost management.

#### Storage Tiers:

- **Primary Storage** (Mux/Livepeer):
  - Initial stream recording and delivery
  - High-performance playback for recent/popular content
  
- **Archival Storage** (Arweave):
  - Permanent, decentralized storage for long-term retention
  - Triggered based on age, popularity, or manual admin action

#### Archive Process Flow:

1. Stream is initially recorded on Mux/Livepeer
2. Stream metadata stored in Supabase `streams` table
3. Based on criteria or admin action, archival process is triggered
4. Stream content is downloaded and uploaded to Arweave
5. Arweave transaction ID stored in `video_archives` table
6. `streams` table updated with `arweave_id` reference
7. Playback fallback mechanism implemented if primary storage fails

### 5. Admin Dashboard

Comprehensive dashboard for platform management with role-based access control.

#### Dashboard Sections:

- **Overview**: Real-time platform metrics and KPIs
- **Platform Fee Management**: Fee adjustment and history (2-5% range)
- **User Management**: User directory with role and status controls
- **Storage Management**: Storage usage and archival controls
- **Analytics**: Reporting and data visualization tools
- **Settings**: System configuration and maintenance

#### Security Controls:

- Admin-only access requiring wallet authentication
- Role verification through database checks
- Audit logging for all administrative actions
- Multi-signature requirements for critical changes

## Data Flow Diagrams

### 1. User Authentication Flow

```
┌──────────┐    ┌───────────┐    ┌─────────────┐    ┌──────────┐
│          │    │           │    │             │    │          │
│  User    │───►│  Wallet   │───►│  Next.js    │───►│ Supabase │
│          │    │  Adapter  │    │  Auth API   │    │          │
│          │◄───│           │◄───│             │◄───│          │
└──────────┘    └───────────┘    └─────────────┘    └──────────┘
```

### 2. BONK Tip Transaction Flow

```
┌──────────┐    ┌───────────┐    ┌─────────────┐    ┌──────────┐
│          │    │           │    │             │    │          │
│  Viewer  │───►│  Wallet   │───►│  Next.js    │───►│  Solana  │
│          │    │  Adapter  │    │  API        │    │          │
│          │◄───│           │◄───│             │◄───│          │
└──────────┘    └───────────┘    └─────────────┘    └──────────┘
                                       │
                                       ▼
                                 ┌──────────┐
                                 │          │
                                 │ Supabase │
                                 │          │
                                 │          │
                                 └──────────┘
```

### 3. NFT Minting Flow

```
┌──────────┐   ┌─────────┐   ┌─────────┐   ┌──────────┐   ┌─────────┐
│          │   │         │   │         │   │          │   │         │
│  Viewer  │──►│ Next.js │──►│ Arweave │──►│ Metaplex │──►│ Solana  │
│          │   │ API     │   │         │   │          │   │         │
│          │◄──│         │◄──│         │◄──│          │◄──│         │
└──────────┘   └─────────┘   └─────────┘   └──────────┘   └─────────┘
                    │
                    ▼
              ┌──────────┐
              │          │
              │ Supabase │
              │          │
              │          │
              └──────────┘
```

### 4. Video Storage Flow

```
┌──────────┐   ┌─────────────┐   ┌───────────┐   ┌─────────┐
│          │   │             │   │           │   │         │
│ Streaming│──►│ Mux/Livepeer│──►│ Admin     │──►│ Arweave │
│ Service  │   │             │   │ Dashboard │   │         │
│          │   │             │   │           │   │         │
└──────────┘   └─────────────┘   └───────────┘   └─────────┘
                      │                │              │
                      │                │              │
                      ▼                ▼              ▼
                 ┌────────────────────────────────────┐
                 │                                    │
                 │            Supabase               │
                 │                                    │
                 └────────────────────────────────────┘
```

## API Architecture

The application follows a hybrid API architecture:

1. **API Routes**: For Web3 interactions and external service integrations
   - `/api/tip`: BONK token transfers
   - `/api/mint-moment`: NFT minting
   - `/api/admin/*`: Admin-only endpoints
   
2. **Server Actions**: For database operations and form submissions
   - Profile management
   - Stream metadata handling
   - Admin dashboard operations
   - Platform fee management

## Deployment Architecture

```
┌────────────────────────────────────────────┐
│              Vercel Platform               │
│                                            │
│  ┌────────────┐         ┌────────────┐     │
│  │            │         │            │     │
│  │  Next.js   │         │  API       │     │
│  │  Frontend  │         │  Backend   │     │
│  │            │         │            │     │
│  └────────────┘         └────────────┘     │
│                                            │
└────────────────────────────────────────────┘
             │                  │
             │                  │
             ▼                  ▼
┌───────────────┐      ┌─────────────────────┐
│               │      │                     │
│  Supabase     │      │   Solana            │
│  PostgreSQL   │      │   Blockchain        │
│               │      │                     │
└───────────────┘      └─────────────────────┘
```

## Security Architecture

Security is implemented at multiple layers:

1. **Authentication**: Web3 wallet-based authentication
2. **Authorization**: Row Level Security (RLS) in Supabase
3. **API Security**: Rate limiting and input validation
4. **Admin Security**: Role verification and audit logging
5. **Blockchain Security**: Transaction signature verification
6. **GDPR Compliance**: Data minimization and consent management

## Scalability Considerations

The architecture supports scalability through:

1. **Stateless API Design**: Enables horizontal scaling
2. **Multi-tier Storage**: Cost-effective archival for growing content
3. **Caching Strategy**: Performance optimization for frequent data
4. **Database Indexing**: Optimized for common query patterns
5. **Edge Functions**: Distributed API processing where appropriate

## Monitoring and Observability

The system includes:

1. **Error Tracking**: Structured error logging
2. **Performance Monitoring**: Critical path metrics
3. **User Analytics**: Usage patterns and platform growth
4. **Storage Metrics**: Utilization and cost tracking
5. **Transaction Monitoring**: Blockchain operation status

## Future Architecture Extensions

The architecture is designed to accommodate future features:

1. **NFT Marketplace**: Secondary market for stream moments
2. **Creator Subscriptions**: Recurring BONK payments
3. **Mobile App Integration**: Native applications with shared backend
4. **Multi-chain Support**: Integration with additional blockchains
5. **Machine Learning**: Content recommendation system 

# bonkstream Platform Architecture

## Overview
bonkstream is a Web3-native streaming platform that eliminates traditional platform middlemen by enabling direct BONK token tipping between viewers and creators, with optional NFT minting for special moments.

## 🏗️ Technology Stack

### Blockchain Layer
- **Solana Mainnet**: Primary blockchain for BONK token transfers
- **BONK Token**: `DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263`
- **SPL Token Program**: `TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA`

### Video Infrastructure
- **Livepeer**: Decentralized video streaming and processing
  - Stream ingestion (RTMP)
  - Video transcoding
  - HLS/WebRTC delivery
  - Real-time viewer statistics

### Storage Systems
- **Arweave**: Permanent storage for NFT metadata and video archives
- **Supabase**: PostgreSQL database for user profiles, streams, tips, and platform data
- **IPFS**: Optional additional decentralized storage

### Frontend
- **Next.js 15.2.4**: React framework with App Router
- **Tailwind CSS**: Utility-first styling
- **shadcn/ui**: Component library
- **Solana Wallet Adapter**: Web3 wallet integration

## 👥 User Roles & Responsibilities

### 🎬 Creator
- **Wallet**: Dedicated Solana wallet for receiving tips
- **Permissions**: 
  - Create and manage streams
  - Access creator dashboard
  - View earnings and analytics
  - Manage profile and content
- **Revenue**: Receives 97.5% of all BONK tips
- **Database**: `is_creator = true` in profiles table

### 👤 Viewer
- **Wallet**: Personal Solana wallet (Phantom/Backpack)
- **Permissions**:
  - Watch streams
  - Send BONK tips
  - Mint NFT moments
  - Manage personal collection
- **Costs**: 
  - BONK tokens for tips
  - SOL for gas fees (~0.001 SOL per transaction)

### 🏦 Platform
- **Wallet**: Central treasury wallet
- **Revenue**: 5% platform fee from all tips
- **Responsibilities**:
  - Video infrastructure costs
  - Platform development
  - Content moderation
  - Storage management

### 👑 Admin
- **Wallet**: Verified admin wallet addresses
- **Permissions**:
  - Platform fee management (5%)
  - User account management
  - Storage and archival control
  - Analytics and reporting
  - Feature flag management
- **Database**: `is_admin = true` in profiles table

## 🔄 Core User Flows

### 1. User Onboarding
```
1. User visits bonkstream.com
2. Connects Solana wallet (Phantom/Backpack)
3. Profile auto-created with wallet_address
4. User chooses role:
   - Creator: Apply for creator status
   - Viewer: Browse content immediately
5. Profile stored in Supabase with appropriate flags
```

### 2. Creator Stream Setup
```
1. Creator accesses dashboard
2. Sets up new stream:
   - Title, description, thumbnail
   - Stream category/tags
3. System creates Livepeer stream:
   - Generates unique stream key
   - Sets up transcoding profiles
4. Creator uses OBS/streaming software:
   - RTMP to Livepeer endpoint
   - Stream key for authentication
5. Stream goes live:
   - HLS/WebRTC endpoints available
   - Real-time status updates
```

### 3. BONK Tipping Process
```
1. Viewer watches stream
2. Clicks "Tip BONK" button
3. Tip modal opens:
   - Shows current BONK balance
   - Displays fee breakdown (95% / 5%)
   - Optional name and message fields
4. User enters tip amount
5. System validates:
   - Sufficient BONK balance
   - Not tipping themselves
6. Creates SPL token transaction:
   - Transfer to creator (97.5%)
   - Transfer to platform (5%)
   - Memo instruction with details
7. User signs transaction in wallet
8. Transaction broadcasted to Solana
9. Confirmation and Explorer link provided
10. Database updated with tip record
```

### 4. NFT Moment Minting
```
1. Viewer pauses at desired timestamp
2. Clicks "Mint NFT Moment"
3. System captures frame and metadata
4. Uploads to Arweave:
   - Image data
   - Stream metadata
   - Timestamp information
   - Creator attribution
5. Creates NFT mint transaction using Metaplex
6. User signs transaction
7. NFT minted to user's wallet
8. Added to user's collection
9. Creator receives royalties
```

## 💰 Economics & Fee Structure

### Platform Fee Distribution
- **Creator**: 97.5% of tip amount
- **Platform**: 5% of tip amount
- **Gas Fees**: Paid by tipper (~0.001 SOL)

### Fee Calculation Example
```
Tip Amount: 1000 BONK
Creator Receives: 975 BONK (97.5%)
Platform Fee: 25 BONK (5%)
Gas Fee: ~0.001 SOL (separate)
```

### Admin Fee Management
- Configurable between 5%
- Requires admin wallet signature
- All changes logged and auditable
- Historical fee tracking

## 🗄️ Database Schema

### Core Tables

#### `profiles`
- User wallet addresses and metadata
- Role flags (is_creator, is_admin)
- Profile information (username, avatar)

#### `streams`
- Stream metadata and Livepeer integration
- Creator association
- View counts and engagement metrics

#### `tips`
- BONK transaction records
- Platform fee amounts and percentages
- Transaction signatures for verification

#### `nfts`
- Minted NFT records
- Arweave metadata URIs
- Stream timestamp associations

#### `platform_fees`
- Fee percentage history
- Admin change tracking
- Effective date management

## 🔐 Security & Compliance

### Wallet Security
- No private key storage
- Client-side transaction signing
- Signature verification for all operations
- Multi-signature for admin functions

### Database Security
- Row Level Security (RLS) policies
- Wallet-based authentication
- Input validation with Zod schemas
- Rate limiting on API endpoints

### GDPR Compliance
- Cookie consent management
- Data export functionality
- Account deletion options
- Transparent fee disclosure

## 🌐 Network Configuration

### Helius RPC Endpoints
1. **Primary**: `mainnet.helius-rpc.com` (with API key)
2. **Fallback**: `mainnet.helius-rpc.com` (without API key, rate limited)
3. **Additional Fallbacks**: 
   - `api.mainnet-beta.solana.com`
   - `solana-mainnet.g.alchemy.com`
   - `rpc.ankr.com/solana`

### Development Configuration
- **Devnet**: `devnet.helius-rpc.com`
- **Network Detection**: Automatic endpoint selection based on `NEXT_PUBLIC_SOLANA_NETWORK`
- **API Key Support**: Optional Helius API key for higher rate limits

### Error Handling
- Automatic RPC fallback with Helius priority
- Connection timeout detection
- Transaction retry logic
- User-friendly error messages

## 📈 Analytics & Monitoring

### Creator Analytics
- Total BONK earnings
- Tip history and trends
- Stream performance metrics
- NFT minting statistics

### Platform Analytics
- Total revenue and fees collected
- User growth metrics
- Transaction volume
- Content engagement statistics

### Admin Dashboard
- Real-time platform health
- Revenue tracking
- User management tools
- Storage usage monitoring

## 🎥 Video Archival System

### Storage Tiers
1. **Live/Recent**: Livepeer (immediate access)
2. **Popular**: Extended Livepeer storage
3. **Archive**: Arweave (permanent, decentralized)

### Archival Policy
- Based on view count and age
- Automatic triggering for popular content
- Manual admin controls
- Cost optimization algorithms

## 🚀 Future Enhancements

### Planned Features
- Multi-chain support (Ethereum, Polygon)
- Advanced creator monetization tools
- Enhanced NFT marketplace
- Mobile application
- Advanced analytics dashboard

### Technical Improvements
- WebRTC direct streaming
- Enhanced video quality options
- Improved mobile experience
- Better caching strategies

## 🛠️ Development Environment

### Required Setup
- Node.js 18+
- Solana CLI tools
- Phantom/Backpack wallet
- Environment variables for:
  - Supabase connection
  - Livepeer API keys
  - Solana RPC endpoints
  - Platform wallet addresses

### Testing Strategy
- Unit tests for core functions
- Integration tests for wallet interactions
- End-to-end testing for user flows
- Load testing for streaming infrastructure 