/** @type {import('next').NextConfig} */
const createNextIntlPlugin = require('next-intl/plugin');

// Specify the exact path to the request config
const withNextIntl = createNextIntlPlugin('./src/lib/i18n/request.ts');

const nextConfig = {
  reactStrictMode: true,
  // Use remotePatterns for next/image to avoid deprecation warning
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      },
    ],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Add image optimization
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 86400, // 24 hours
  },
  // Performance optimizations
  poweredByHeader: false,
  // Optimize compilation speed
  compiler: {
    // Remove console.* in production builds
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn'],
    } : false,
  },
  // Improve incremental builds
  webpack: (config, { isServer, dev }) => {
    // Optimize CSS
    config.optimization.splitChunks = {
      ...config.optimization.splitChunks,
      cacheGroups: {
        ...config.optimization.splitChunks.cacheGroups,
        styles: {
          name: 'styles',
          test: /\.css$/,
          chunks: 'all',
          enforce: true,
        },
        // Separate vendor chunks for better caching
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10,
        },
        // Separate Solana related libraries
        solana: {
          test: /[\\/]node_modules[\\/](@solana|@metaplex)/,
          name: 'solana',
          chunks: 'all',
          priority: 20,
        },
      },
    };
    
    // Performance optimizations
    if (!isServer && !dev) {
      // Bundle analyzer in production builds only when needed
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
          })
        );
      }
    }
    
    // Configure for faster development builds
    if (dev) {
      // Reduce locale handling in development
      config.watchOptions = {
        ...config.watchOptions,
        ignored: ['**/node_modules', '**/.git', '**/src/locales/**/*.json'],
      };
      
      // Faster development builds
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: false,
          vendors: false,
        },
      };
    }
    
    return config;
  },
  // Transpile packages for better compatibility
  transpilePackages: ['next-intl'],

  // Add headers for better caching
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, s-maxage=60, stale-while-revalidate=300',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  // Production optimizations
  ...(process.env.NODE_ENV === 'production' && {
    output: 'standalone',
    // Enable static optimization
    trailingSlash: false,
    // Compression
    compress: true,
  }),
};

module.exports = withNextIntl(nextConfig); 