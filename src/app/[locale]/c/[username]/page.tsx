'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { useRouter } from '@/lib/i18n';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { 
  UserCircle, 
  Video, 
  CalendarDays, 
  Eye, 
  Heart,
  Award,
  Gift,
  Coins,
  Share,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Link as I18nLink } from '@/lib/i18n';
import { TipButton } from '@/components/stream/TipButton';
import { useWallet } from '@solana/wallet-adapter-react';
import { LoadingScreen } from '@/components/ui/LoadingSpinner';

// Enhanced Profile & Stream types for comprehensive creator page
interface PublicProfile {
  id: string;
  username: string;
  avatar_url?: string;
  bio?: string;
  wallet_address: string;
  created_at: string;
  is_creator: boolean;
}

interface PublicStream {
  id: string;
  title: string;
  description?: string;
  thumbnail_url?: string;
  view_count?: number;
  tip_count?: number;
  nft_count?: number;
  like_count?: number;
  duration?: number; // in seconds
  status: string;
  created_at: string;
  is_active?: boolean;
  is_actually_live?: boolean;
  ended_at?: string;
}

interface TipSummary {
  total_tips: number;
  total_amount: number;
  recent_tips: {
    id: string;
    amount: number;
    creator_amount: number;
    tipper_name?: string;
    message?: string;
    created_at: string;
    stream_title?: string;
    tx_signature?: string;
  }[];
}

interface NFTSummary {
  total_nfts: number;
  recent_mints: {
    id: string;
    mint_address: string;
    timestamp: number;
    stream_title?: string;
    created_at: string;
  }[];
}

interface CreatorStats {
  total_streams: number;
  total_views: number;
  total_likes: number;
  total_tips_received: number;
  total_bonk_earned: number;
  total_nfts_minted: number;
  avg_stream_duration: number;
  follower_count?: number;
}

interface CreatorPageData {
  profile: PublicProfile | null;
  streams: PublicStream[];
  stats: CreatorStats;
  tips: TipSummary;
  nfts: NFTSummary;
}

// Helper to format date
const formatDate = (dateString: string, locale: string) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString(locale, {
    year: 'numeric', month: 'long', day: 'numeric'
  });
};

// Helper to format duration
const formatDuration = (seconds: number = 0) => {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.floor(seconds % 60);
    const parts = [];
    if (h > 0) parts.push(`${h}h`);
    if (m > 0) parts.push(`${m}m`);
    if (s > 0 || parts.length === 0) parts.push(`${s}s`);
    return parts.join(' ');
};

// Helper to format numbers (for views, likes, etc.)
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  } else {
    return num.toString();
  }
};

export default function CreatorPublicPage() {
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  const username = params.username as string;
  const { connected } = useWallet();

  const t = useTranslations('common.creatorPage');
  const commonT = useTranslations('common');

  const [data, setData] = useState<CreatorPageData>({ 
    profile: null, 
    streams: [],
    stats: {
      total_streams: 0,
      total_views: 0,
      total_likes: 0,
      total_tips_received: 0,
      total_bonk_earned: 0,
      total_nfts_minted: 0,
      avg_stream_duration: 0
    },
    tips: { total_tips: 0, total_amount: 0, recent_tips: [] },
    nfts: { total_nfts: 0, recent_mints: [] }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copiedWallet, setCopiedWallet] = useState(false);
  const [copiedShare, setCopiedShare] = useState(false);

  useEffect(() => {
    if (!username || !locale) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch comprehensive creator data
        const response = await fetch(`/${locale}/api/creator/${username}`); 
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error(t('creatorNotFound'));
          }
          throw new Error(t('fetchFailed'));
        }
        const result = await response.json();
        setData(prevData => ({ 
          profile: result.profile, 
          streams: result.streams || [],
          stats: result.stats || prevData.stats,
          tips: result.tips || prevData.tips,
          nfts: result.nfts || prevData.nfts
        }));
      } catch (err) {
        console.error('Error fetching creator page data:', err);
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [username, locale, t]);

  // Utility functions
  const copyWalletAddress = async () => {
    if (data.profile?.wallet_address) {
      try {
        await navigator.clipboard.writeText(data.profile.wallet_address);
        setCopiedWallet(true);
        setTimeout(() => setCopiedWallet(false), 2000);
      } catch (err) {
        console.error('Failed to copy wallet address:', err);
      }
    }
  };

  const handleShare = async () => {
    const shareUrl = `${window.location.origin}/${locale}/c/${username}`;
    const shareText = `Check out ${data.profile?.username}'s creator profile on bonkstream!`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: `${data.profile?.username} - bonkstream Creator`,
          text: shareText,
          url: shareUrl,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(shareUrl);
        setCopiedShare(true);
        setTimeout(() => setCopiedShare(false), 2000);
      } catch (err) {
        console.error('Failed to copy share URL:', err);
      }
    }
  };

  const formatBONK = (amountInLamports: number) => {
    // Convert from lamports to BONK (divide by 10^5)
    const bonkAmount = amountInLamports / Math.pow(10, 5); // BONK has 5 decimals
    
    if (bonkAmount >= 1000000) {
      return `${(bonkAmount / 1000000).toFixed(2)}M`;
    } else if (bonkAmount >= 1000) {
      return `${(bonkAmount / 1000).toFixed(1)}K`;
    } else if (bonkAmount >= 1) {
      return bonkAmount.toFixed(2);
    } else {
      return bonkAmount.toFixed(5);
    }
  };

  const getSolanaExplorerUrl = (signature: string) => {
    return `https://explorer.solana.com/tx/${signature}`;
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return t('today');
    if (diffDays === 1) return t('yesterday');
    if (diffDays < 7) return t('daysAgo', { days: diffDays });
    if (diffDays < 30) return t('weeksAgo', { weeks: Math.floor(diffDays / 7) });
    if (diffDays < 365) return t('monthsAgo', { months: Math.floor(diffDays / 30) });
    return t('yearsAgo', { years: Math.floor(diffDays / 365) });
  };

  if (loading) {
    return (
      <LoadingScreen />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center p-4">
          <div className="text-center max-w-md mx-auto p-6 bg-red-900/20 rounded-lg border border-red-500/30">
            <UserCircle className="mx-auto h-16 w-16 text-red-400 mb-4"/>
            <h2 className="text-xl font-bold mb-2">{commonT('error')}</h2>
            <p className="mb-4 text-red-300">{error}</p>
            <Button onClick={() => router.push('/discover')} className="bg-gray-700 hover:bg-gray-600">
              {t('backToDiscover')}
            </Button>
          </div>
      </div>
    );
  }

  if (!data.profile) {
    // This case should be covered by the 404 error, but as a fallback:
    return (
         <div className="min-h-screen bg-black text-white flex flex-col items-center justify-center p-4">
            <div className="text-center">
                <UserCircle className="mx-auto h-16 w-16 text-gray-500 mb-4"/>
                <p>{t('creatorNotFound')}</p>
                <Button onClick={() => router.push('/discover')} className="mt-4 bg-gray-700 hover:bg-gray-600">
                    {t('backToDiscover')}
                </Button>
            </div>
      </div>
    );
  }

  const { profile, streams, stats, tips, nfts } = data;

  return (
    <div className="min-h-screen bg-black text-white">


      <main className="container mx-auto px-4 py-6 max-w-6xl">
        {/* Creator Profile Header */}
        <div className="mb-8">
          {/* Cover Image */}
          <div className="h-48 md:h-64 bg-gradient-to-r from-[#FFD700]/20 to-[#FF6B35]/20 rounded-xl mb-6 relative overflow-hidden">
            <div className="absolute inset-0 bg-black/40" />
          </div>

          {/* Profile Info */}
          <div className="flex flex-col md:flex-row items-start gap-6 -mt-16 relative">
            <Avatar className="w-32 h-32 border-4 border-black rounded-full bg-gray-800 shadow-xl">
              <AvatarImage src={profile.avatar_url} alt={profile.username} />
              <AvatarFallback className="bg-[#FFD700] text-black text-4xl font-bold">
                {profile.username?.charAt(0).toUpperCase() || 'C'}
              </AvatarFallback>
            </Avatar>

            <div className="flex-grow mt-16 md:mt-0">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">{profile.username}</h1>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className="bg-[#FFD700] text-black text-xs">{t('creator')}</Badge>
                    <span className="text-gray-400 text-sm">
                      {profile.wallet_address.substring(0,6)}...{profile.wallet_address.substring(profile.wallet_address.length - 4)}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyWalletAddress}
                      className="h-6 w-6 p-0 text-gray-400 hover:text-white"
                    >
                      {copiedWallet ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                    </Button>
                  </div>
                  {profile.bio && (
                    <p className="text-gray-300 text-sm md:text-base leading-relaxed max-w-2xl">{profile.bio}</p>
                  )}
                </div>

                <div className="flex gap-3">
                  {connected ? (
                    <TipButton 
                      streamId="" 
                      creatorWalletAddress={profile.wallet_address}
                      creatorName={profile.username}
                      className="bg-[#FFD700] text-black hover:bg-[#E6C300] font-semibold px-6 py-2 rounded-lg flex items-center gap-2"
                    />
                  ) : (
                    <Button
                      disabled={true}
                      className="bg-gray-500 text-gray-300 cursor-not-allowed font-semibold px-6"
                    >
                      <Gift className="h-4 w-4 mr-2" />
                      {t('connectWalletToTip')}
                    </Button>
                  )}
                  <Button 
                    variant="outline" 
                    className="border-gray-600 text-white hover:bg-gray-800"
                    onClick={handleShare}
                  >
                    {copiedShare ? <Check className="h-4 w-4 mr-2" /> : <Share className="h-4 w-4 mr-2" />}
                    {copiedShare ? t('copied') : t('share')}
                  </Button>
                  
                </div>
              </div>

              {/* Stats Row */}
              <div className="grid grid-cols-6 gap-1 sm:gap-2 md:gap-4 mb-6">
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-white">{stats.total_streams}</div>
                  <div className="text-xs text-gray-400">{t('streams')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-white">{formatNumber(stats.total_views)}</div>
                  <div className="text-xs text-gray-400">{t('views')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-white">{formatNumber(stats.total_likes)}</div>
                  <div className="text-xs text-gray-400">{t('likes')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-[#FFD700]">{formatBONK(stats.total_bonk_earned)}</div>
                  <div className="text-xs text-gray-400">{t('bonk')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-white">{stats.total_tips_received}</div>
                  <div className="text-xs text-gray-400">{t('tips')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg md:text-xl font-bold text-white">{stats.total_nfts_minted}</div>
                  <div className="text-xs text-gray-400">{t('nfts')}</div>
                </div>
              </div>

              <div className="flex items-center text-sm text-gray-500">
                <CalendarDays className="h-4 w-4 mr-2" />
                {t('joined', { date: formatDate(profile.created_at, locale) })}
              </div>
            </div>
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs defaultValue="streams" className="w-full">
          <TabsList className="grid w-full grid-cols-3 md:grid-cols-3 bg-gray-900 border-gray-700">
            <TabsTrigger value="streams" className="data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
              <Video className="h-4 w-4 mr-2" />
              {t('streamsWithCount', { count: stats.total_streams })}
            </TabsTrigger>
            <TabsTrigger value="tips" className="data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
              <Coins className="h-4 w-4 mr-2" />
              {t('tipsWithCount', { count: tips.total_tips })}
            </TabsTrigger>
            <TabsTrigger value="about" className="data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
              <UserCircle className="h-4 w-4 mr-2" />
              {t('about')}
            </TabsTrigger>
          </TabsList>

          {/* Streams Tab */}
          <TabsContent value="streams" className="mt-6">
          {streams.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {streams.map(stream => (
                  <Card key={stream.id} className="bg-gray-900 border-gray-700 hover:border-[#FFD700]/50 transition-all duration-200 group">
                    <div className="relative">
                      <I18nLink href={`/stream/${stream.id}`}>
                        <div className="aspect-video bg-gray-800 rounded-t-lg overflow-hidden relative">
                      {stream.thumbnail_url ? (
                            <Image src={stream.thumbnail_url} alt={stream.title} fill className="object-cover group-hover:scale-105 transition-transform duration-200" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                            <Video className="w-12 h-12 text-gray-500"/>
                        </div>
                      )}
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
                          <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                            {formatDuration(stream.duration)}
                          </div>
                          {stream.is_actually_live && (
                            <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                              {t('labels.live')}
                            </div>
                          )}
                        </div>
                      </I18nLink>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-white mb-2 line-clamp-2 group-hover:text-[#FFD700] transition-colors">
                        <I18nLink href={`/stream/${stream.id}`}>
                          {stream.title}
                  </I18nLink>
                    </h3>
                      {stream.description && (
                        <p className="text-sm text-gray-400 mb-3 line-clamp-2">{stream.description}</p>
                      )}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className="flex items-center">
                          <Eye className="h-3 w-3 mr-1"/>
                          {formatNumber(stream.view_count || 0)}
                        </span>
                        <span className="flex items-center">
                          <Heart className="h-3 w-3 mr-1"/>
                          {stream.like_count || 0}
                        </span>
                        <span className="flex items-center">
                          <Coins className="h-3 w-3 mr-1"/>
                          {stream.tip_count || 0}
                        </span>
                        <span className="flex items-center">
                          <Award className="h-3 w-3 mr-1"/>
                          {stream.nft_count || 0}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">
                        {formatRelativeTime(stream.created_at)}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <Video className="mx-auto h-16 w-16 text-gray-600 mb-4" />
                <h3 className="text-xl font-semibold text-gray-400 mb-2">{t('noStreamsMessage')}</h3>
                <p className="text-gray-500">{t('noStreamsMessage')}</p>
              </div>
            )}
          </TabsContent>

          {/* Tips Tab */}
          <TabsContent value="tips" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="lg:col-span-2 bg-gray-900 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Coins className="h-5 w-5 mr-2 text-[#FFD700]" />
                    {t('sections.recentTips')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {tips.recent_tips.length > 0 ? (
                    <div className="space-y-4">
                      {tips.recent_tips.map(tip => (
                        <div key={tip.id} className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                          <div className="w-8 h-8 bg-[#FFD700] rounded-full flex items-center justify-center flex-shrink-0">
                            <Coins className="h-4 w-4 text-black" />
                          </div>
                          <div className="flex-grow">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-semibold text-white text-sm">
                                {tip.tipper_name || t('labels.anonymous')}
                              </span>
                              <span className="text-[#FFD700] font-bold">
                                {formatBONK(tip.creator_amount)} BONK
                              </span>
                              {tip.tx_signature && (
                                <a
                                  href={getSolanaExplorerUrl(tip.tx_signature)}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-400 hover:text-blue-300 text-xs"
                                  title={t('labels.viewOnExplorer')}
                                >
                                  <ExternalLink className="h-3 w-3" />
                                </a>
                              )}
                            </div>
                            {tip.message && (
                              <p className="text-gray-300 text-sm mb-2">{tip.message}</p>
                            )}
                            <div className="text-xs text-gray-500 flex items-center gap-2">
                              <span>{tip.stream_title} • {formatRelativeTime(tip.created_at)}</span>
                              {tip.amount !== tip.creator_amount && (
                                <span className="text-gray-600">
                                  ({t('labels.totalAmount', { amount: formatBONK(tip.amount) })})
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Gift className="mx-auto h-12 w-12 mb-3" />
                      <p>{t('noTipsMessage')}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div>
                <Card className="bg-gray-900 border-gray-700 mb-4">
                  <CardHeader>
                    <CardTitle className="text-white text-lg">{t('sections.tipSummary')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{t('sections.totalTips')}</span>
                        <span className="font-bold text-white">{tips.total_tips}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{t('sections.totalBonk')}</span>
                        <span className="font-bold text-[#FFD700]">{formatBONK(tips.total_amount)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* NFTs Tab */}
          <TabsContent value="nfts" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="lg:col-span-2 bg-gray-900 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Award className="h-5 w-5 mr-2 text-[#FFD700]" />
                    {t('sections.recentNftMints')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {nfts.recent_mints.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {nfts.recent_mints.map(nft => (
                        <div key={nft.id} className="bg-gray-800 rounded-lg p-4">
                          <div className="aspect-square bg-gray-700 rounded-lg mb-3 flex items-center justify-center">
                            <Award className="h-8 w-8 text-[#FFD700]" />
                          </div>
                          <h4 className="font-semibold text-white text-sm mb-1">
                            {t('labels.streamMoment', { timestamp: nft.timestamp })}
                          </h4>
                          <p className="text-xs text-gray-400 mb-2">{nft.stream_title}</p>
                          <div className="text-xs text-gray-500">
                            {t('labels.minted', { time: formatRelativeTime(nft.created_at) })}
                          </div>
                        </div>
                      ))}
            </div>
          ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Award className="mx-auto h-12 w-12 mb-3" />
                      <p>{t('noNftsMessage')}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div>
                <Card className="bg-gray-900 border-gray-700 mb-4">
                  <CardHeader>
                    <CardTitle className="text-white text-lg">{t('sections.nftCollection')}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{t('sections.totalNfts')}</span>
                        <span className="font-bold text-white">{nfts.total_nfts}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* About Tab */}
          <TabsContent value="about" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-900 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white">{t('labels.about', { username: profile.username })}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {profile.bio ? (
                    <p className="text-gray-300 leading-relaxed">{profile.bio}</p>
                  ) : (
                    <p className="text-gray-500 italic">{t('noBioMessage')}</p>
                  )}
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold text-white">{t('sections.walletAddress')}</h4>
                    <div className="flex items-center gap-2">
                      <code className="bg-gray-800 px-2 py-1 rounded text-sm text-gray-300 font-mono">
                        {profile.wallet_address}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={copyWalletAddress}
                        className="h-8 w-8 p-0"
                      >
                        {copiedWallet ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-semibold text-white">{t('sections.memberSince')}</h4>
                    <p className="text-gray-300">{formatDate(profile.created_at, locale)}</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white">{t('sections.statistics')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div className="text-center p-3 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-white">{stats.total_streams}</div>
                      <div className="text-sm text-gray-400">{t('stats.totalStreams')}</div>
                    </div>
                    <div className="text-center p-3 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-white">{formatNumber(stats.total_views)}</div>
                      <div className="text-sm text-gray-400">{t('stats.totalViews')}</div>
                    </div>
                    <div className="text-center p-3 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-white">{formatNumber(stats.total_likes)}</div>
                      <div className="text-sm text-gray-400">{t('stats.totalLikes')}</div>
                    </div>
                    <div className="text-center p-3 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-[#FFD700]">{formatBONK(stats.total_bonk_earned)}</div>
                      <div className="text-sm text-gray-400">{t('stats.bonkEarned')}</div>
                    </div>
                    <div className="text-center p-3 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-white">{stats.total_tips_received}</div>
                      <div className="text-sm text-gray-400">{t('stats.tipsReceived')}</div>
                    </div>
                    <div className="text-center p-3 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-white">{stats.total_nfts_minted}</div>
                      <div className="text-sm text-gray-400">{t('stats.nftsMinted')}</div>
                    </div>
                  </div>

                  {stats.avg_stream_duration > 0 && (
                    <div className="pt-4 border-t border-gray-700">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{t('sections.avgStreamDuration')}</span>
                        <span className="font-semibold text-white">
                          {formatDuration(stats.avg_stream_duration)}
                        </span>
                      </div>
            </div>
          )}
                </CardContent>
              </Card>
        </div>
          </TabsContent>
        </Tabs>
      </main>

    </div>
  );
} 