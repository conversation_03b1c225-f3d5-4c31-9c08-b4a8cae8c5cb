'use client';

import { useState, useEffect, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { useWallet } from '@solana/wallet-adapter-react';
import { useParams } from 'next/navigation';
import { useWalletAuth } from '@/lib/useWalletAuth';

type DashboardStats = {
  activeUsers: number;
  revenue: {
    last24h: number;
    last7d: number;
    last30d: number;
  };
  transactions: {
    count: number;
    volume: number;
  };
  storage: {
    total: number; // GB
    used: number; // GB
  };
  newUsers: {
    last24h: number;
    last7d: number;
    last30d: number;
  };
  streams: {
    total: number;
    live: number;
    processing: number;
  };
  creators: {
    total: number;
    active: number;
  };
  nfts: {
    total: number;
    minted24h: number;
  };
  tips: {
    averageAmount: number;
    totalAmount: number;
  };
  recentActivity: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
  }>;
};

// Default initial stats with zeros
const initialStats: DashboardStats = {
  activeUsers: 0,
  revenue: {
    last24h: 0,
    last7d: 0,
    last30d: 0,
  },
  transactions: {
    count: 0,
    volume: 0,
  },
  storage: {
    total: 100,
    used: 0,
  },
  newUsers: {
    last24h: 0,
    last7d: 0,
    last30d: 0,
  },
  streams: {
    total: 0,
    live: 0,
    processing: 0,
  },
  creators: {
    total: 0,
    active: 0,
  },
  nfts: {
    total: 0,
    minted24h: 0,
  },
  tips: {
    averageAmount: 0,
    totalAmount: 0,
  },
  recentActivity: []
};

// Stat card component
const StatCard = ({ 
  title, 
  value, 
  change, 
  suffix = '', 
  prefix = '', 
  changeType = 'positive',
  isLoading = false
}: { 
  title: string;
  value: string | number;
  change?: number;
  suffix?: string;
  prefix?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  isLoading?: boolean;
}) => {
  const changeColors = {
    positive: 'text-green-400',
    negative: 'text-red-400',
    neutral: 'text-gray-400',
  };
  
  return (
    <div className="bg-black/40 rounded-lg p-5 border border-white/10">
      <h3 className="text-sm font-medium text-white/60 mb-2">{title}</h3>
      <div className="flex items-baseline">
        {isLoading ? (
          <div className="h-8 w-24 bg-white/10 animate-pulse rounded"></div>
        ) : (
          <>
            {prefix && <span className="mr-1">{prefix}</span>}
            <span className="text-2xl font-bold">{value}</span>
            {suffix && <span className="ml-1 text-white/60">{suffix}</span>}
          </>
        )}
      </div>
      {change !== undefined && !isLoading && (
        <div className={`text-xs mt-2 ${changeColors[changeType]}`}>
          {change > 0 ? '↑' : change < 0 ? '↓' : '→'} {Math.abs(change)}% from last period
        </div>
      )}
    </div>
  );
};

export default function AdminDashboardPage() {
  const params = useParams();
  const locale = params.locale as string || 'en';
  const t = useTranslations('admin');
  const { connected, publicKey } = useWallet();
  const { isAuthenticated, isLoading: authLoading, error: authError, authenticate } = useWalletAuth();
  
  const [stats, setStats] = useState<DashboardStats>(initialStats);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('24h');
  
  // Trigger authentication when wallet connects
  useEffect(() => {
    if (connected && publicKey && !isAuthenticated && !authLoading) {
      authenticate();
    }
  }, [connected, publicKey, isAuthenticated, authLoading, authenticate]);

  const loadDashboardData = useCallback(async () => {
    if (!connected || !publicKey || !isAuthenticated) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const walletAddress = publicKey.toString();
      
      // Create authentication headers using the same pattern as collection page
      const { createAuthHeaders } = await import('@/lib/wallet-auth');
      const headers = {
        'Content-Type': 'application/json',
        ...createAuthHeaders(walletAddress)
      };

      // Load all dashboard data in parallel
      const [dashboardStatsRes, activityRes] = await Promise.all([
        fetch(`/${locale}/api/admin/dashboard/stats?timeRange=${timeRange}`, { headers }),
        fetch(`/${locale}/api/admin/activity?limit=10`, { headers })
      ]);
          
      // Process responses
      const [dashboardStats, activityData] = await Promise.all([
        dashboardStatsRes.ok ? dashboardStatsRes.json() : { data: initialStats },
        activityRes.ok ? activityRes.json() : { data: [] }
      ]);

      setStats({
        activeUsers: dashboardStats.data?.activeUsers || 0,
        revenue: {
          last24h: dashboardStats.data?.revenue?.last24h || 0,
          last7d: dashboardStats.data?.revenue?.last7d || 0,
          last30d: dashboardStats.data?.revenue?.last30d || 0,
        },
        transactions: {
          count: dashboardStats.data?.transactions?.count || 0,
          volume: dashboardStats.data?.transactions?.volume || 0
        },
        storage: {
          total: 100,
          used: 0,
        },
        newUsers: {
          last24h: dashboardStats.data?.newUsers?.last24h || 0,
          last7d: dashboardStats.data?.newUsers?.last7d || 0,
          last30d: dashboardStats.data?.newUsers?.last30d || 0,
        },
        streams: {
          total: dashboardStats.data?.streams?.total || 0,
          live: dashboardStats.data?.streams?.live || 0,
          processing: dashboardStats.data?.streams?.processing || 0,
        },
        creators: {
          total: dashboardStats.data?.creators?.total || 0,
          active: dashboardStats.data?.creators?.active || 0,
        },
        nfts: {
          total: dashboardStats.data?.nfts?.total || 0,
          minted24h: dashboardStats.data?.nfts?.minted24h || 0,
        },
        tips: {
          averageAmount: dashboardStats.data?.tips?.averageAmount || 0,
          totalAmount: dashboardStats.data?.tips?.totalAmount || 0,
        },
        recentActivity: activityData.data || []
      });

    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, [connected, publicKey, isAuthenticated, locale, timeRange]);

  // Load dashboard data when authenticated
  useEffect(() => {
    if (connected && publicKey && isAuthenticated) {
      loadDashboardData();
    }
  }, [connected, publicKey, isAuthenticated, loadDashboardData]);
  
  // Authentication loading state
  if (authLoading || (!isAuthenticated && connected)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="flex flex-col items-center text-center">
          <div className="w-10 h-10 border-2 border-white border-t-transparent rounded-full animate-spin mb-4"></div>
          <h2 className="text-xl mb-2">Authenticating...</h2>
          <p className="text-white/60 max-w-md">
            Please sign the message in your wallet to access admin dashboard.
          </p>
        </div>
      </div>
    );
  }
  
  // Authentication error state
  if (authError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center max-w-md p-6 bg-black/40 rounded-lg border border-red-500/30">
          <h2 className="text-xl mb-2">Authentication Failed</h2>
          <p className="mb-6 text-white/60">{authError}</p>
          <button 
            onClick={() => authenticate()} 
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded transition mr-2"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  
  // No wallet connected
  if (!connected) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center max-w-md p-6 bg-black/40 rounded-lg border border-orange-500/30">
          <h1 className="text-2xl font-bold mb-4">Admin Access Required</h1>
          <p className="mb-6">Please connect your wallet to access the admin dashboard.</p>
        </div>
      </div>
    );
  }
  
  // Loading dashboard data
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>Loading dashboard...</span>
        </div>
      </div>
    );
  }
  
  const getTimeSince = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const handleRefresh = () => {
    loadDashboardData();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{t('dashboard.title')}</h1>
          <p className="text-white/60 mt-1">Overview of platform metrics and activity</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-black/30 border border-white/20 rounded px-3 py-1 text-white"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="all">All Time</option>
          </select>
          <button 
            onClick={handleRefresh}
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded transition"
          >
            {t('refresh.text')}
          </button>
        </div>
      </div>
      
      {/* Error state */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold mb-2 text-red-400">Error Loading Dashboard</h2>
          <p className="text-white/70 mb-4">{error}</p>
          <button 
            onClick={handleRefresh}
            className="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 rounded transition"
          >
            Try Again
          </button>
        </div>
      )}
      
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard 
          title="Active Users"
          value={(stats.activeUsers || 0).toLocaleString()}
          change={15}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title={`Revenue (${timeRange})`}
          value={timeRange === '24h' ? (stats.revenue?.last24h || 0) : timeRange === '7d' ? (stats.revenue?.last7d || 0) : (stats.revenue?.last30d || 0)}
          prefix="$"
          change={8}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title="Total Transactions"
          value={(stats.transactions?.count || 0).toLocaleString()}
          change={-2}
          changeType="negative"
          isLoading={isLoading}
        />
        <StatCard 
          title="Total Streams"
          value={(stats.streams?.total || 0).toLocaleString()}
          change={5}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title="Live Streams"
          value={(stats.streams?.live || 0).toLocaleString()}
          change={12}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title="Total Creators"
          value={(stats.creators?.total || 0).toLocaleString()}
          change={3}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title="Total NFTs"
          value={(stats.nfts?.total || 0).toLocaleString()}
          change={25}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title="Avg Tip Amount"
          value={stats.tips?.averageAmount ? (stats.tips.averageAmount / Math.pow(10, 5)).toFixed(2) : '0.00'}
          suffix=" BONK"
          change={-5}
          changeType="negative"
          isLoading={isLoading}
        />
        <StatCard 
          title="Processing Streams"
          value={(stats.streams?.processing || 0).toLocaleString()}
          change={0}
          changeType="neutral"
          isLoading={isLoading}
        />
        <StatCard 
          title="NFTs (24h)"
          value={(stats.nfts?.minted24h || 0).toLocaleString()}
          change={18}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title="New Users (24h)"
          value={(stats.newUsers?.last24h || 0).toLocaleString()}
          change={22}
          changeType="positive"
          isLoading={isLoading}
        />
        <StatCard 
          title="Total Tip Volume"
          value={stats.tips?.totalAmount ? (stats.tips.totalAmount / Math.pow(10, 5)).toFixed(0) : '0'}
          suffix=" BONK"
          change={12}
          changeType="positive"
          isLoading={isLoading}
        />
      </div>
      
      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-black/40 rounded-lg border border-white/10">
          <div className="p-6 border-b border-white/10">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
          </div>
          <div className="p-6">
            {stats.recentActivity.length > 0 ? (
              <div className="space-y-4">
                {stats.recentActivity.slice(0, 6).map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-white/40 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm">{activity.message}</p>
                      <p className="text-xs text-white/40 mt-1">
                        {getTimeSince(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-white/60 text-center py-8">No recent activity</p>
            )}
          </div>
        </div>
        
        <div className="bg-black/40 rounded-lg border border-white/10">
          <div className="p-6 border-b border-white/10">
            <h2 className="text-xl font-semibold">Platform Overview</h2>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-white/60">New Users (24h)</span>
              <span className="font-semibold">{stats.newUsers?.last24h || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/60">New Users (7d)</span>
              <span className="font-semibold">{stats.newUsers?.last7d || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/60">New Users (30d)</span>
              <span className="font-semibold">{stats.newUsers?.last30d || 0}</span>
            </div>
            <div className="border-t border-white/10 pt-4 mt-4">
              <div className="flex justify-between items-center">
                <span className="text-white/60">Processing Streams</span>
                <span className="font-semibold">{stats.streams?.processing || 0}</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/60">Active Creators</span>
              <span className="font-semibold">{stats.creators?.active || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/60">NFTs Minted (24h)</span>
              <span className="font-semibold">{stats.nfts?.minted24h || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/60">Total Tip Volume</span>
              <span className="font-semibold">{stats.tips?.totalAmount ? (stats.tips.totalAmount / Math.pow(10, 5)).toFixed(0) : '0'} BONK</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 