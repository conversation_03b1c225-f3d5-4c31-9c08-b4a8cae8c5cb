'use client';

import { useState, useEffect, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { Link } from '@/lib/i18n';
import { useWallet } from '@solana/wallet-adapter-react';
import { useParams } from 'next/navigation';
import { useWalletAuth } from '@/lib/useWalletAuth';

type User = {
  id: string;
  wallet_address: string;
  username: string | null;
  avatar_url: string | null;
  is_creator: boolean;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
  status: string;
  last_active: string;
};

export default function UsersPage() {
  const t = useTranslations('admin.users');
  const params = useParams();
  const locale = params.locale as string || 'en';
  const { connected, publicKey } = useWallet();
  const { isAuthenticated, isLoading: authLoading, error: authError, authenticate } = useWalletAuth();
  
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 1
  });
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [isSelectAll, setIsSelectAll] = useState(false);
  const [isBatchDeleting, setIsBatchDeleting] = useState(false);
  const [showBatchDeleteConfirm, setShowBatchDeleteConfirm] = useState(false);
  
  const fetchUsers = useCallback(async () => {
    if (!connected || !publicKey || !isAuthenticated) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const walletAddress = publicKey.toString();
      
      // Create authentication headers
      const { createAuthHeaders } = await import('@/lib/wallet-auth');
      const headers = {
        'Content-Type': 'application/json',
        ...createAuthHeaders(walletAddress)
      };

      // Build query parameters
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search: searchTerm,
        role: roleFilter,
        status: statusFilter
      });

      const response = await fetch(`/${locale}/api/admin/users?${params}`, { headers });
      
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setUsers(data.data.users);
        setPagination(data.data.pagination);
      } else {
        throw new Error(data.error || 'Failed to fetch users');
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError((err as Error).message || 'Failed to load users');
    } finally {
      setIsLoading(false);
    }
  }, [connected, publicKey, isAuthenticated, pagination.page, pagination.limit, searchTerm, roleFilter, statusFilter, locale]);

  // Trigger authentication when wallet connects
  useEffect(() => {
    if (connected && publicKey && !isAuthenticated && !authLoading) {
      authenticate();
    }
  }, [connected, publicKey, isAuthenticated, authLoading, authenticate]);

  // Load users when authenticated
  useEffect(() => {
    if (connected && publicKey && isAuthenticated) {
      fetchUsers();
    }
  }, [connected, publicKey, isAuthenticated, fetchUsers]);

  // Update select all state when individual selections change
  useEffect(() => {
    if (users.length === 0) {
      setIsSelectAll(false);
    } else if (selectedUsers.size === users.length) {
      setIsSelectAll(true);
    } else {
      setIsSelectAll(false);
    }
  }, [selectedUsers.size, users.length]);
  
  const handleDeleteUser = async (userId: string) => {
    if (!connected || !publicKey || !isAuthenticated) return;
    
    setDeletingUserId(userId);
    setError(null);
    
    try {
      const walletAddress = publicKey.toString();
      
      // Create authentication headers
      const { createAuthHeaders } = await import('@/lib/wallet-auth');
      const headers = {
        'Content-Type': 'application/json',
        ...createAuthHeaders(walletAddress)
      };

      const response = await fetch(`/${locale}/api/admin/users/${userId}`, {
        method: 'DELETE',
        headers
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }

      // Remove user from local state
      setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
      setShowDeleteConfirm(null);
      
    } catch (err) {
      console.error('Error deleting user:', err);
      setError((err as Error).message);
    } finally {
      setDeletingUserId(null);
    }
  };

  const confirmDelete = (userId: string) => {
    setShowDeleteConfirm(userId);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  // Batch selection functions
  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (isSelectAll) {
      setSelectedUsers(new Set());
      setIsSelectAll(false);
    } else {
      setSelectedUsers(new Set(users.map(user => user.id)));
      setIsSelectAll(true);
    }
  };

  const handleBatchDelete = async () => {
    if (!connected || !publicKey || !isAuthenticated || selectedUsers.size === 0) return;
    
    setIsBatchDeleting(true);
    setError(null);
    
    try {
      const walletAddress = publicKey.toString();
      
      // Create authentication headers
      const { createAuthHeaders } = await import('@/lib/wallet-auth');
      const headers = {
        'Content-Type': 'application/json',
        ...createAuthHeaders(walletAddress)
      };

      // Delete users one by one
      const userIds = Array.from(selectedUsers);
      const results = await Promise.allSettled(
        userIds.map(userId =>
          fetch(`/${locale}/api/admin/users/${userId}`, {
            method: 'DELETE',
            headers
          })
        )
      );

      // Check results
      const successful = results.filter(result => 
        result.status === 'fulfilled' && result.value.ok
      ).length;
      
      const failed = results.length - successful;

      // Remove successfully deleted users from local state
      const successfulUserIds = results
        .map((result, index) => result.status === 'fulfilled' && result.value.ok ? userIds[index] : null)
        .filter(id => id !== null) as string[];

      setUsers(prevUsers => prevUsers.filter(user => !successfulUserIds.includes(user.id)));
      
      // Clear selection
      setSelectedUsers(new Set());
      setIsSelectAll(false);
      setShowBatchDeleteConfirm(false);

      // Show results
      if (failed > 0) {
        setError(`Successfully deleted ${successful} users. Failed to delete ${failed} users.`);
      } else {
        // Show success message briefly
        setTimeout(() => setError(null), 3000);
      }
      
    } catch (err) {
      console.error('Error in batch deletion:', err);
      setError((err as Error).message);
    } finally {
      setIsBatchDeleting(false);
    }
  };

  const confirmBatchDelete = () => {
    setShowBatchDeleteConfirm(true);
  };

  const cancelBatchDelete = () => {
    setShowBatchDeleteConfirm(false);
  };
  
  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const colors = {
      active: 'bg-green-500/20 text-green-400 border-green-500/30',
      suspended: 'bg-red-500/20 text-red-400 border-red-500/30',
    };
    
    return (
      <span 
        className={`px-2 py-1 text-xs rounded-full border ${colors[status as keyof typeof colors] || 'bg-gray-500/20 text-gray-400 border-gray-500/30'}`}
      >
        {status}
      </span>
    );
  };
  
  // Role badge component
  const RoleBadge = ({ isAdmin, isCreator }: { isAdmin: boolean; isCreator: boolean }) => {
    if (isAdmin) {
      return (
        <span className="px-2 py-1 text-xs rounded-full bg-[#FFD700]/20 text-[#FFD700] border border-[#FFD700]/30">
          {t('admin')}
        </span>
      );
    }
    
    if (isCreator) {
      return (
        <span className="px-2 py-1 text-xs rounded-full bg-purple-500/20 text-purple-400 border border-purple-500/30">
          {t('creator')}
        </span>
      );
    }
    
    return (
      <span className="px-2 py-1 text-xs rounded-full bg-white/10 text-white/60 border border-white/20">
        {t('viewer')}
      </span>
    );
  };

  // Authentication loading state
  if (authLoading || (!isAuthenticated && connected)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="flex flex-col items-center text-center">
          <div className="w-10 h-10 border-2 border-white border-t-transparent rounded-full animate-spin mb-4"></div>
          <h2 className="text-xl mb-2">Authenticating...</h2>
          <p className="text-white/60 max-w-md">
            Please sign the message in your wallet to access admin dashboard.
          </p>
        </div>
      </div>
    );
  }

  // Authentication error state
  if (authError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center max-w-md p-6 bg-black/40 rounded-lg border border-red-500/30">
          <h2 className="text-xl mb-2">Authentication Failed</h2>
          <p className="mb-6 text-white/60">{authError}</p>
          <button 
            onClick={() => authenticate()} 
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded transition mr-2"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // No wallet connected
  if (!connected) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center max-w-md p-6 bg-black/40 rounded-lg border border-orange-500/30">
          <h1 className="text-2xl font-bold mb-4">Admin Access Required</h1>
          <p className="mb-6">Please connect your wallet to access the admin dashboard.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">{t('text')}</h1>
      
      {/* Search and filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-grow">
          <input
            type="text"
            placeholder={t('search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 bg-black/30 border border-white/20 rounded-md text-white placeholder:text-white/40 focus:outline-none focus:ring-1 focus:ring-[#FFD700]"
          />
        </div>
        
        <div className="flex gap-2">
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="px-3 py-2 bg-black/30 border border-white/20 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-[#FFD700]"
          >
            <option value="all">All Roles</option>
            <option value="admin">{t('admin')}</option>
            <option value="creator">{t('creator')}</option>
            <option value="user">{t('viewer')}</option>
          </select>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 bg-black/30 border border-white/20 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-[#FFD700]"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
      </div>
      
      {/* Error state */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold mb-2 text-red-400">Error Loading Users</h2>
          <p className="text-white/70 mb-4">{error}</p>
          <button 
            onClick={fetchUsers}
            className="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 rounded transition"
          >
            Try Again
          </button>
        </div>
      )}
      
      {/* Results count */}
      <div className="text-sm text-white/60">
        Showing {users.length} of {pagination.total} users
      </div>
      
      {/* Batch Actions Toolbar */}
      {selectedUsers.size > 0 && (
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-blue-400 font-medium">
                {selectedUsers.size} user{selectedUsers.size !== 1 ? 's' : ''} selected
              </span>
              <button
                onClick={() => setSelectedUsers(new Set())}
                className="text-blue-400 hover:text-blue-300 text-sm underline"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={confirmBatchDelete}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition flex items-center space-x-2"
                disabled={isBatchDeleting}
              >
                {isBatchDeleting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Deleting...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    <span>Delete Selected ({selectedUsers.size})</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Users table */}
      <div className="bg-black/40 rounded-lg border border-white/10 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left border-b border-white/10">
                <th className="px-6 py-3 text-white/70">
                  <input
                    type="checkbox"
                    checked={isSelectAll}
                    onChange={handleSelectAll}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                </th>
                <th className="px-6 py-3 text-white/70">User</th>
                <th className="px-6 py-3 text-white/70">{t('walletAddress')}</th>
                <th className="px-6 py-3 text-white/70">{t('role')}</th>
                <th className="px-6 py-3 text-white/70">{t('status')}</th>
                <th className="px-6 py-3 text-white/70">{t('joined')}</th>
                <th className="px-6 py-3 text-white/70">{t('actions')}</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/5">
              {isLoading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center text-white/60">
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Loading users...</span>
                    </div>
                  </td>
                </tr>
              ) : users.length > 0 ? (
                users.map((user) => (
                  <tr key={user.id} className="hover:bg-white/5 transition">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedUsers.has(user.id)}
                        onChange={() => handleSelectUser(user.id)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-800">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={user.avatar_url || `https://placehold.co/32x32/gray/white?text=${user.username?.[0] || '?'}`}
                            alt={user.username || user.wallet_address}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <span>{user.username || '(No username)'}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 font-mono text-sm">{user.wallet_address}</td>
                    <td className="px-6 py-4">
                      <RoleBadge isAdmin={user.is_admin} isCreator={user.is_creator} />
                    </td>
                    <td className="px-6 py-4">
                      <StatusBadge status={user.status} />
                    </td>
                    <td className="px-6 py-4 text-white/60 text-sm">
                      {new Date(user.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex space-x-2">
                        {user.is_creator && user.username ? (
                          <Link
                            href={`/c/${user.username}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="px-3 py-1 text-xs rounded-md bg-[#FFD700]/10 text-[#FFD700] hover:bg-[#FFD700]/20 border border-[#FFD700]/20 transition"
                          >
                            {t('viewProfile')}
                          </Link>
                        ) : (
                          <span className="px-3 py-1 text-xs rounded-md bg-gray-500/10 text-gray-400 border border-gray-500/20 cursor-not-allowed">
                            {t('viewProfile')}
                          </span>
                        )}
                        <button
                          onClick={() => confirmDelete(user.id)}
                          className="px-3 py-1 text-xs rounded-md bg-red-500/10 text-red-400 hover:bg-red-500/20 border border-red-500/20 transition"
                          disabled={deletingUserId === user.id}
                        >
                          {deletingUserId === user.id ? 'Deleting...' : 'Delete'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center text-white/60">
                    <p>No users found matching your filters.</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="border-t border-white/10 px-6 py-3 flex justify-between items-center">
            <button 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
              className="px-3 py-1 text-sm rounded hover:bg-white/10 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="text-sm text-white/60">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            <button 
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page >= pagination.totalPages}
              className="px-3 py-1 text-sm rounded hover:bg-white/10 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}
      </div>
      
      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-black/90 border border-white/10 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold mb-4">Confirm User Deletion</h3>
            <p className="text-white/70 mb-6">
              Are you sure you want to delete this user? This action cannot be undone and will permanently remove all user data including their profile, streams, tips, and NFTs.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => handleDeleteUser(showDeleteConfirm)}
                className="flex-1 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded transition"
                disabled={deletingUserId === showDeleteConfirm}
              >
                {deletingUserId === showDeleteConfirm ? 'Deleting...' : 'Delete User'}
              </button>
              <button
                onClick={cancelDelete}
                className="flex-1 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded transition"
                disabled={deletingUserId === showDeleteConfirm}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Batch Delete Confirmation Modal */}
      {showBatchDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-black/90 border border-white/10 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold mb-4">Confirm Batch User Deletion</h3>
            <p className="text-white/70 mb-6">
              Are you sure you want to delete {selectedUsers.size} selected user{selectedUsers.size !== 1 ? 's' : ''}? This action cannot be undone and will permanently remove all user data including their profiles, streams, tips, and NFTs.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={handleBatchDelete}
                className="flex-1 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded transition"
                disabled={isBatchDeleting}
              >
                {isBatchDeleting ? 'Deleting...' : `Delete ${selectedUsers.size} User${selectedUsers.size !== 1 ? 's' : ''}`}
              </button>
              <button
                onClick={cancelBatchDelete}
                className="flex-1 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded transition"
                disabled={isBatchDeleting}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 