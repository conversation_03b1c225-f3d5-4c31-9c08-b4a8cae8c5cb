'use client';

import React, { useEffect, useState } from 'react';
import { Link, useRouter } from '@/lib/i18n';
import { useTranslations } from 'next-intl';
import { useWallet } from '@solana/wallet-adapter-react';
import { useParams } from 'next/navigation';
import { useWalletAuth } from '@/lib/useWalletAuth';

// TypeScript interface for the layout props to match Next.js 15 expectations
// interface LayoutProps {
//   children: React.ReactNode;
//   params: Promise<{ locale: string }>;
// }

const Icon = ({ name }: { name: string }) => {
  switch (name) {
    case 'dashboard':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="7" height="9"></rect>
          <rect x="14" y="3" width="7" height="5"></rect>
          <rect x="14" y="12" width="7" height="9"></rect>
          <rect x="3" y="16" width="7" height="5"></rect>
        </svg>
      );
    case 'dollar':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <line x1="12" y1="1" x2="12" y2="23"></line>
          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
        </svg>
      );
    case 'users':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      );
    case 'database':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
          <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
          <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
        </svg>
      );
    case 'bar-chart':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <line x1="12" y1="20" x2="12" y2="10"></line>
          <line x1="18" y1="20" x2="18" y2="4"></line>
          <line x1="6" y1="20" x2="6" y2="16"></line>
        </svg>
      );
    case 'analytics':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <line x1="12" y1="20" x2="12" y2="10"></line>
          <line x1="18" y1="20" x2="18" y2="4"></line>
          <line x1="6" y1="20" x2="6" y2="16"></line>
        </svg>
      );
    case 'audit':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
      );
    default:
      return null;
  }
};

// Client component that contains the actual layout UI and logic
export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const { connected, publicKey } = useWallet();
  const { isAuthenticated, isLoading: authLoading, error: authError, authenticate } = useWalletAuth();
  const t = useTranslations('admin');
  const params = useParams();
  const locale = params.locale as string || 'en';
  const router = useRouter();
  
  const [adminCheckComplete, setAdminCheckComplete] = useState(false);

  // Trigger authentication when wallet connects (same as collection page)
  useEffect(() => {
    if (connected && publicKey && !isAuthenticated && !authLoading) {
      authenticate();
    }
  }, [connected, publicKey, isAuthenticated, authLoading, authenticate]);

  // Check admin status when authenticated (same pattern as collection page)
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!connected || !publicKey || !isAuthenticated) return;
      
      try {
        const walletAddress = publicKey.toString();
        
        // Create authentication headers using the same pattern as collection page
        const { createAuthHeaders } = await import('@/lib/wallet-auth');
        const headers = {
          'Content-Type': 'application/json',
          ...createAuthHeaders(walletAddress)
        };

        const response = await fetch(`/${locale}/api/profile?wallet=${walletAddress}`, {
          headers,
          cache: 'no-cache'
        });

        if (!response.ok) {
          console.error('Failed to check admin status:', response.status);
          setAdminCheckComplete(true);
          return;
        }

        const data = await response.json();
        const isAdminUser = data.profile?.is_admin || false;
        
        setAdminCheckComplete(true);
        
        // If not admin, redirect
        if (!isAdminUser) {
          router.push('/');
        }
        
      } catch (error) {
        console.error('Error checking admin status:', error);
        setAdminCheckComplete(true);
      }
    };

    if (connected && publicKey && isAuthenticated) {
      checkAdminStatus();
    }
  }, [connected, publicKey, isAuthenticated, locale, router]);

  // Nav items for the sidebar
  const navItems = [
    { href: '/admin', label: t('nav.dashboard'), icon: 'dashboard' },
    { href: '/admin/users', label: t('nav.users'), icon: 'users' },
  ];

  // Authentication loading state
  if (authLoading || (!isAuthenticated && connected)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="flex flex-col items-center text-center">
          <div className="w-10 h-10 border-2 border-white border-t-transparent rounded-full animate-spin mb-4"></div>
          <h2 className="text-xl mb-2">Authenticating...</h2>
          <p className="text-white/60 max-w-md">
            Please sign the message in your wallet to access admin dashboard.
          </p>
        </div>
      </div>
    );
  }

  // Authentication error state
  if (authError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center max-w-md p-6 bg-black/40 rounded-lg border border-red-500/30">
          <h2 className="text-xl mb-2">Authentication Failed</h2>
          <p className="mb-6 text-white/60">{authError}</p>
          <button 
            onClick={() => authenticate()} 
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded transition mr-2"
          >
            Try Again
          </button>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded transition"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  // Admin check loading state
  if (isAuthenticated && !adminCheckComplete) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span>Verifying admin access...</span>
        </div>
      </div>
    );
  }

  if (!connected) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-900 text-white">
        <div className="text-center max-w-md p-6 bg-black/40 rounded-lg border border-orange-500/30">
          <h1 className="text-2xl font-bold mb-4">Admin Access Required</h1>
          <p className="mb-6">Please connect your wallet to access the admin dashboard.</p>
          <Link href="/" className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded transition">
            Return Home
          </Link>
        </div>
      </div>
    );
  }

  // This should only render if isAdmin is true (otherwise redirected above)
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="flex h-screen">
        {/* Sidebar */}
        <aside className="w-64 bg-black/40 border-r border-white/10 flex-shrink-0">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-8 text-white">Admin Panel</h2>
            <nav className="space-y-2">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors duration-200 text-white/80 hover:text-white"
                >
                  <Icon name={item.icon} />
                  <span className="font-medium">{item.label}</span>
                </Link>
              ))}
            </nav>
          </div>
        </aside>

        {/* Main content */}
        <main className="flex-1 overflow-auto">
          <div className="p-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}