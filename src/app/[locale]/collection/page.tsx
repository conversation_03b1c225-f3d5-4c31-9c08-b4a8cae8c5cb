'use client';

import { useState, useEffect, useCallback } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import ClientWalletButton from '@/components/wallet/ClientWalletButton';
import { useWalletAuth } from '@/lib/useWalletAuth';
import { useParams } from 'next/navigation';
import { Loader2, ExternalLink, Clock, ChevronDown, Filter, SortAsc, SortDesc } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

type NFT = {
  id: string;
  mint_address: string;
  metadata_uri: string;
  timestamp: number;
  platform_fee_at_mint: number;
  created_at: string;
  stream_id: string;
  stream?: {
    title: string;
    creator: {
      username?: string;
      wallet_address: string;
    };
  };
  metadata?: {
    name: string;
    description: string;
    image: string;
    attributes: Array<{ trait_type: string; value: string }>;
  };
};

export default function CollectionPage() {
  const wallet = useWallet();
  const { connected, publicKey } = wallet;
  const { isAuthenticated, isLoading: authLoading, error: authError, authenticate } = useWalletAuth();
  const params = useParams();
  const locale = params.locale as string;
  const t = useTranslations('collection');
  const commonT = useTranslations('common');
  
  const [nfts, setNfts] = useState<NFT[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedNft, setSelectedNft] = useState<NFT | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [sortBy, setSortBy] = useState<'created_at' | 'stream_title'>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [showSortMenu, setShowSortMenu] = useState(false);
  
  // Trigger authentication when wallet connects
  useEffect(() => {
    if (connected && publicKey && !isAuthenticated && !authLoading) {
      authenticate();
    }
  }, [connected, publicKey, isAuthenticated, authLoading, authenticate]);
  
  // Fetch user's NFT collection after authentication
  const fetchCollectionData = useCallback(async (walletAddress: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // First try to get bonkstream NFTs from database
      const { createAuthHeaders } = await import('@/lib/wallet-auth');
      
      const headers = {
        'Content-Type': 'application/json',
        ...createAuthHeaders(walletAddress)
      };
      
      let bonkstreamNfts: NFT[] = [];
      try {
        const response = await fetch(`/${locale}/api/collection?owner=${walletAddress}&page=${currentPage}&sort=${sortBy}&direction=${sortDirection}`, {
          headers
        });
        
        if (response.ok) {
          const data = await response.json();
          bonkstreamNfts = data.nfts || [];
          setHasMore(data.pagination?.hasMore || false);
        }
      } catch (err) {
        console.warn('Could not fetch bonkstream NFTs:', err);
      }
      
      // Now fetch ALL NFTs from the wallet using Solana blockchain
      try {
        const { PublicKey, Connection } = await import('@solana/web3.js');
        const { TOKEN_PROGRAM_ID } = await import('@solana/spl-token');
        
        // Get connection
        const connection = new Connection(
          process.env.NEXT_PUBLIC_SOLANA_RPC || 'https://api.mainnet-beta.solana.com'
        );
        
        const walletPubkey = new PublicKey(walletAddress);
        
        // Get all token accounts owned by the wallet
        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(walletPubkey, {
          programId: TOKEN_PROGRAM_ID,
        });
        
        // Filter for NFTs (tokens with decimals = 0 and amount = 1)
        const nftAccounts = tokenAccounts.value.filter(account => {
          const info = account.account.data.parsed.info;
          return info.tokenAmount.decimals === 0 && info.tokenAmount.uiAmount === 1;
        });
        
        
        // Fetch metadata for each NFT
        const allNfts = await Promise.all(
          nftAccounts.slice(0, 50).map(async (account, index) => { // Limit to 50 NFTs for performance
            try {
              const mintAddress = account.account.data.parsed.info.mint;
              
              // Check if this is a bonkstream NFT first
              const bonkstreamNft = bonkstreamNfts.find(nft => nft.mint_address === mintAddress);
              if (bonkstreamNft) {
                return bonkstreamNft; // Use the detailed bonkstream NFT data
              }
              
              // For non-bonkstream NFTs, try to fetch metadata from Metaplex
              try {
                // Try to get metadata account
                const { Metaplex } = await import('@metaplex-foundation/js');
                const metaplex = Metaplex.make(connection);
                
                const mintPubkey = new PublicKey(mintAddress);
                const nft = await metaplex.nfts().findByMint({ mintAddress: mintPubkey });
                
                if (nft.json) {
                  return {
                    id: `wallet-nft-${index}`,
                    mint_address: mintAddress,
                    metadata_uri: nft.uri,
                    timestamp: 0,
                    platform_fee_at_mint: 0,
                    created_at: new Date().toISOString(),
                    stream_id: '',
                    metadata: {
                      name: nft.json.name || 'Unknown NFT',
                      description: nft.json.description || 'NFT from wallet',
                      image: nft.json.image || '/nft-placeholder.png',
                      attributes: nft.json.attributes || []
                    }
                  } as NFT;
                }
              } catch (metaplexError) {
                console.warn(`Could not fetch Metaplex metadata for ${mintAddress}:`, metaplexError);
              }
              
              // Fallback: create a basic NFT object
              return {
                id: `wallet-nft-${index}`,
                mint_address: mintAddress,
                metadata_uri: '',
                timestamp: 0,
                platform_fee_at_mint: 0,
                created_at: new Date().toISOString(),
                stream_id: '',
                metadata: {
                  name: 'Unknown NFT',
                  description: 'NFT from your wallet',
                  image: '/nft-placeholder.png',
                  attributes: [
                    { trait_type: 'Mint Address', value: formatWalletAddress(mintAddress) },
                    { trait_type: 'Type', value: 'Wallet NFT' }
                  ]
                }
              } as NFT;
              
            } catch (err) {
              console.error('Error processing NFT:', err);
              return null;
            }
          })
        );
        
        // Filter out null results and set NFTs
        const validNfts = allNfts.filter(nft => nft !== null) as NFT[];
        setNfts(validNfts);
        
        // Since we're fetching all NFTs from wallet directly, there's no more to load
        setHasMore(false);
        
        
      } catch (blockchainError) {
        console.error('Error fetching NFTs from blockchain:', blockchainError);
        // Fallback to bonkstream NFTs only
        setNfts(bonkstreamNfts);
        setHasMore(false);
      }
      
    } catch (err) {
      console.error('Error fetching collection:', err);
      setError('Failed to load NFT collection. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [locale, currentPage, sortBy, sortDirection]);

  // Load collection data when authenticated
  useEffect(() => {
    if (connected && publicKey && isAuthenticated) {
      fetchCollectionData(publicKey.toString());
    }
  }, [connected, publicKey, isAuthenticated, currentPage, sortBy, sortDirection, fetchCollectionData]);
  
  // Format time (seconds to MM:SS)
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Format wallet address
  const formatWalletAddress = (address: string) => {
    if (!address) return '';
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };
  
  // View NFT on Explorer
  const viewOnExplorer = (mintAddress: string) => {
    const baseUrl = process.env.NEXT_PUBLIC_SOLANA_EXPLORER_URL || 'https://explorer.solana.com/address';
    const networkParam = process.env.NEXT_PUBLIC_SOLANA_NETWORK === 'mainnet' ? '' : '?cluster=devnet';
    
    window.open(`${baseUrl}/${mintAddress}${networkParam}`, '_blank');
  };
  
  // Handle NFT selection
  const handleSelectNft = (nft: NFT) => {
    setSelectedNft(nft);
  };
  
  // Handle sort change
  const handleSortChange = (field: 'created_at' | 'stream_title') => {
    if (field === sortBy) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to descending
      setSortBy(field);
      setSortDirection('desc');
    }
    setShowSortMenu(false);
  };
  
  // Not connected state
  if (!connected) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <header className="border-b border-bonk-orange/20 p-4">
          <div className="container mx-auto flex justify-between items-center">
            <h1 className="bonk-header text-xl">{t('title')}</h1>
          </div>
        </header>
        
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="text-center max-w-md mx-auto">
            <div className="w-24 h-24 rounded-full bg-bonk-widget-dark flex items-center justify-center mx-auto mb-6">
              <svg className="w-12 h-12 text-bonk-orange" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
              </svg>
            </div>
            <h2 className="bonk-header text-2xl mb-2">{t('connectWallet')}</h2>
            <p className="bonk-body text-white/60 mb-6">
              {t('description')}
            </p>
            <ClientWalletButton />
          </div>
        </main>
      </div>
    );
  }
  
  // Authentication loading state
  if (authLoading || (!isAuthenticated && connected)) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <header className="border-b border-bonk-orange/20 p-4">
          <div className="container mx-auto flex justify-between items-center">
            <h1 className="bonk-header text-xl">{t('title')}</h1>
          </div>
        </header>
        
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="flex flex-col items-center text-center">
            <LoadingSpinner size="lg" text="Authenticating..." />
            <h2 className="bonk-header text-xl mb-2 mt-4">Authenticating...</h2>
            <p className="bonk-body text-white/60 mb-6 max-w-md">
              Please sign the message in your wallet to access your collection.
            </p>
          </div>
        </main>
      </div>
    );
  }
  
  // Authentication error state
  if (authError) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <header className="border-b border-bonk-orange/20 p-4">
          <div className="container mx-auto flex justify-between items-center">
            <h1 className="bonk-header text-xl">{t('title')}</h1>
          </div>
        </header>
        
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="text-center max-w-md mx-auto p-6 bg-bonk-widget-dark rounded-lg border border-bonk-red/30">
            <h2 className="bonk-header text-xl mb-2">Authentication Failed</h2>
            <p className="bonk-body text-white/60 mb-6">
              {authError}
            </p>
            <Button onClick={() => authenticate()} className="bonk-btn bg-bonk-orange hover:bg-bonk-orange/80 text-white mr-2">
              Try Again
            </Button>
            <Button onClick={() => window.location.reload()} className="bonk-btn bg-bonk-widget-black hover:bg-bonk-widget-dark text-white">
              Refresh Page
            </Button>
          </div>
        </main>
      </div>
    );
  }
  
  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <header className="border-b border-bonk-orange/20 p-4">
          <div className="container mx-auto flex justify-between items-center">
            <h1 className="bonk-header text-xl">{t('title')}</h1>
          </div>
        </header>
        
        <main className="flex-1 flex items-center justify-center p-4">
          <LoadingSpinner size="lg" text="Loading your NFT collection..." />
        </main>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <header className="border-b border-bonk-orange/20 p-4">
          <div className="container mx-auto flex justify-between items-center">
            <h1 className="bonk-header text-xl">{t('title')}</h1>
          </div>
        </header>
        
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="text-center max-w-md mx-auto p-6 bg-bonk-widget-dark rounded-lg border border-bonk-red/30">
            <svg className="w-12 h-12 mx-auto mb-4 text-bonk-red" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h2 className="bonk-header text-xl mb-2">{commonT('error')}</h2>
            <p className="bonk-body mb-4">{error}</p>
            <Button 
              onClick={() => window.location.reload()}
              className="bonk-btn bg-bonk-red/20 hover:bg-bonk-red/30 text-white"
            >
              {t('retry')}
            </Button>
          </div>
        </main>
      </div>
    );
  }
  
  // Empty collection state
  if (nfts.length === 0) {
    return (
      <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
        <header className="border-b border-bonk-orange/20 p-4">
          <div className="container mx-auto flex justify-between items-center">
            <h1 className="bonk-header text-xl">My Collection</h1>
          </div>
        </header>
        
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="text-center max-w-md mx-auto">
            <div className="w-24 h-24 rounded-full bg-bonk-widget-dark flex items-center justify-center mx-auto mb-6">
              <svg className="w-12 h-12 text-bonk-orange" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
              </svg>
            </div>
            <h2 className="bonk-header text-2xl mb-2">No NFTs Found</h2>
            <p className="bonk-body text-white/60 mb-6">
              You haven&apos;t minted any NFT moments yet. Start watching streams and mint your favorite moments!
            </p>
          </div>
        </main>
      </div>
    );
  }
  
  // Main collection view (rest of the component remains the same)
  return (
    <div className="min-h-screen bg-bonk-gradient-bg text-white flex flex-col">
      <header className="border-b border-bonk-orange/20 p-4">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="bonk-header text-xl">{t('title')}</h1>
        </div>
      </header>
      
      <main className="flex-1 container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="bonk-header text-2xl">{t('your_nft_moments')}</h2>
          
          <div className="relative">
            <button 
              onClick={() => setShowSortMenu(!showSortMenu)} 
              className="bonk-btn flex items-center bg-bonk-widget-dark hover:bg-bonk-widget-black text-white rounded-md px-3 py-2 text-sm transition"
            >
              <Filter className="w-4 h-4 mr-2" />
              <span>{t('sortBy')}</span>
              <ChevronDown className="w-4 h-4 ml-2" />
            </button>
            
            {showSortMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-bonk-widget-dark border border-bonk-orange/20 rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button
                    onClick={() => handleSortChange('created_at')}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-bonk-widget-black transition ${
                      sortBy === 'created_at' ? 'text-bonk-orange' : 'text-white'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span>Date Minted</span>
                      {sortBy === 'created_at' && (
                        sortDirection === 'desc' ? <SortDesc className="w-4 h-4" /> : <SortAsc className="w-4 h-4" />
                      )}
                    </div>
                  </button>
                  <button
                    onClick={() => handleSortChange('stream_title')}
                    className={`w-full text-left px-4 py-2 text-sm hover:bg-bonk-widget-black transition ${
                      sortBy === 'stream_title' ? 'text-bonk-orange' : 'text-white'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span>Stream Title</span>
                      {sortBy === 'stream_title' && (
                        sortDirection === 'desc' ? <SortDesc className="w-4 h-4" /> : <SortAsc className="w-4 h-4" />
                      )}
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* NFT Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {nfts.map((nft) => (
            <div
              key={nft.id}
              className="bg-bonk-widget-dark rounded-lg border border-bonk-orange/20 overflow-hidden hover:border-bonk-orange/40 transition-colors cursor-pointer group"
              onClick={() => handleSelectNft(nft)}
            >
              <div className="aspect-square relative">
                <Image
                  src={nft.metadata?.image || '/nft-placeholder.png'}
                  alt={nft.metadata?.name || 'NFT'}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/nft-placeholder.png';
                  }}
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
              </div>
              
              <div className="p-4">
                <h3 className="bonk-header text-sm mb-2 truncate">
                  {nft.metadata?.name || 'Unknown NFT'}
                </h3>
                
                {nft.stream && (
                  <div className="text-xs text-white/60 mb-1">
                    <span className="truncate">{nft.stream.title}</span>
                  </div>
                )}
                
                {nft.timestamp > 0 && (
                  <div className="text-xs text-white/60 mb-2">
                    <Clock className="w-3 h-3 inline mr-1" />
                    {formatTime(nft.timestamp)}
                  </div>
                )}
                
                <div className="text-xs text-white/40">
                  {formatDate(nft.created_at)}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {hasMore && (
          <div className="flex justify-center mt-8">
            <Button
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={loadingMore}
              className="bonk-btn bg-bonk-orange hover:bg-bonk-orange/80 text-white"
            >
              {loadingMore ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Loading...
                </>
              ) : (
                'Load More NFTs'
              )}
            </Button>
          </div>
        )}
      </main>

      {/* NFT Detail Modal */}
      {selectedNft && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-bonk-widget border border-bonk-orange/30 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h2 className="bonk-header text-xl">
                  {selectedNft.metadata?.name || 'NFT Details'}
                </h2>
                <button
                  onClick={() => setSelectedNft(null)}
                  className="text-white/60 hover:text-white"
                >
                  <svg className="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="relative aspect-square">
                  <Image
                    src={selectedNft.metadata?.image || '/nft-placeholder.png'}
                    alt={selectedNft.metadata?.name || 'NFT'}
                    fill
                    className="object-cover rounded-lg"
                    sizes="(max-width: 768px) 100vw, 50vw"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/nft-placeholder.png';
                    }}
                  />
                </div>
                
                <div>
                  <div className="space-y-4">
                    <div>
                      <h3 className="bonk-header text-lg mb-2">Description</h3>
                      <p className="bonk-body text-white/80">
                        {selectedNft.metadata?.description || 'No description available'}
                      </p>
                    </div>
                    
                    {selectedNft.stream && (
                      <div>
                        <h3 className="bonk-header text-lg mb-2">Stream</h3>
                        <p className="bonk-body text-white/80">{selectedNft.stream.title}</p>
                        <p className="text-sm text-white/60">
                          by {selectedNft.stream.creator.username || formatWalletAddress(selectedNft.stream.creator.wallet_address)}
                        </p>
                      </div>
                    )}
                    
                    {selectedNft.timestamp > 0 && (
                      <div>
                        <h3 className="bonk-header text-lg mb-2">Timestamp</h3>
                        <p className="bonk-body text-white/80">{formatTime(selectedNft.timestamp)}</p>
                      </div>
                    )}
                    
                    <div>
                      <h3 className="bonk-header text-lg mb-2">Mint Address</h3>
                      <p className="bonk-body text-white/80 break-all text-sm">
                        {selectedNft.mint_address}
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="bonk-header text-lg mb-2">Minted</h3>
                      <p className="bonk-body text-white/80">{formatDate(selectedNft.created_at)}</p>
                    </div>
                    
                    {selectedNft.metadata?.attributes && selectedNft.metadata.attributes.length > 0 && (
                      <div>
                        <h3 className="bonk-header text-lg mb-2">Attributes</h3>
                        <div className="grid grid-cols-2 gap-2">
                          {selectedNft.metadata.attributes.map((attr, index) => (
                            <div key={index} className="bg-bonk-widget-black rounded p-2">
                              <div className="text-xs text-white/60">{attr.trait_type}</div>
                              <div className="text-sm text-white">{attr.value}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-6 flex gap-3">
                    <Button
                      onClick={() => viewOnExplorer(selectedNft.mint_address)}
                      className="bonk-btn bg-bonk-orange hover:bg-bonk-orange/80 text-white flex items-center"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View on Explorer
                    </Button>
                    
                    {selectedNft.stream_id && (
                      <Link href={`/stream/${selectedNft.stream_id}`}>
                        <Button className="bonk-btn bg-bonk-widget-black hover:bg-bonk-widget-dark text-white">
                          View Stream
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 