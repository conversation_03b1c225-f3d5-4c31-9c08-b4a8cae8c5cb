import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';

// Create server-side Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Validation schema for viewer tracking
const viewerTrackingSchema = z.object({
  streamId: z.string().uuid(),
  action: z.enum(['join', 'leave']),
  viewerId: z.string().optional(), // Optional anonymous viewer ID
});

// In-memory store for tracking active viewers per stream
const activeViewers = new Map<string, Set<string>>();

export async function POST(request: NextRequest) {
  try {
    let body;
    
    // Check content type to handle sendBeacon requests properly
    const contentType = request.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      body = await request.json();
    } else {
      // Handle sendBeacon or other requests without proper content-type
      const text = await request.text();
      if (!text.trim()) {
        return NextResponse.json(
          { error: 'Empty request body' },
          { status: 400 }
        );
      }
      body = JSON.parse(text);
    }
    
    const result = viewerTrackingSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.errors },
        { status: 400 }
      );
    }
    
    const { streamId, action, viewerId } = result.data;
    
    // Generate a viewer ID if not provided (for anonymous viewers)
    const effectiveViewerId = viewerId || `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Check if stream exists
    const { data: stream, error: streamError } = await supabaseAdmin
      .from('streams')
      .select('id, status')
      .eq('id', streamId)
      .single();
    
    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }
    
    // Get current viewers for this stream
    if (!activeViewers.has(streamId)) {
      activeViewers.set(streamId, new Set());
    }
    
    const streamViewers = activeViewers.get(streamId)!;
    
    // Update viewer count based on action
    if (action === 'join') {
      streamViewers.add(effectiveViewerId);
    } else if (action === 'leave') {
      streamViewers.delete(effectiveViewerId);
    }
    
    const viewerCount = streamViewers.size;
    
    // Update viewer count in database
    const { error: updateError } = await supabaseAdmin
      .rpc('update_viewer_count', {
        p_stream_id: streamId,
        p_count: viewerCount
      });
    
    if (updateError) {
      console.error('Failed to update viewer count:', updateError);
    }
    
    return NextResponse.json({
      success: true,
      viewerCount,
      viewerId: effectiveViewerId
    });
    
  } catch (error) {
    console.error('Viewer tracking error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const streamId = url.searchParams.get('streamId');

    if (!streamId) {
      return NextResponse.json(
        { error: 'Stream ID required' },
        { status: 400 }
      );
    }

    if (activeViewers.has(streamId)) {
      activeViewers.delete(streamId);

      await supabaseAdmin.rpc('update_viewer_count', {
        p_stream_id: streamId,
        p_count: 0
      });
    }
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('Viewer cleanup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
