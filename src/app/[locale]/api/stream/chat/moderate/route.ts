import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import supabase from '@/lib/supabase/client';

// Validation schema for message moderation
const moderateMessageSchema = z.object({
  messageId: z.string().uuid(),
  action: z.enum(['delete']),
  walletAddress: z.string().min(32).max(44),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const result = moderateMessageSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.errors },
        { status: 400 }
      );
    }
    
    const { messageId, action, walletAddress } = result.data;
    
    // Get wallet auth headers for verification
    const authHeader = request.headers.get('authorization');
    const signature = request.headers.get('x-wallet-signature');
    const authMessage = request.headers.get('x-wallet-message');
    
    if (!authHeader || !signature || !authMessage || !authHeader.includes(walletAddress)) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, username')
      .eq('wallet_address', walletAddress)
      .single();
    
    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }
    
    // Get the message to check permissions
    const { data: message, error: messageError } = await supabase
      .from('chat_messages')
      .select(`
        id,
        sender_id,
        stream_id,
        streams!inner (
          creator_id
        )
      `)
      .eq('id', messageId)
      .eq('is_deleted', false)
      .single();
    
    if (messageError || !message) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }
    
    // Check if user has permission to moderate this message
    const streams = Array.isArray(message.streams) && message.streams.length > 0 
      ? message.streams[0] as { creator_id: string } 
      : null;
    const isCreator = streams?.creator_id === profile.id;
    const isMessageSender = message.sender_id === profile.id;
    
    if (!isCreator && !isMessageSender) {
      return NextResponse.json(
        { error: 'Permission denied. Only stream creators can moderate messages or users can delete their own messages.' },
        { status: 403 }
      );
    }
    
    // Perform the moderation action
    if (action === 'delete') {
      const { error: updateError } = await supabase
        .from('chat_messages')
        .update({
          is_deleted: true,
          deleted_by: profile.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', messageId);
      
      if (updateError) {
        console.error('Message deletion error:', updateError);
        return NextResponse.json(
          { error: 'Failed to delete message' },
          { status: 500 }
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      action: action,
      messageId: messageId
    });
    
  } catch (error) {
    console.error('Moderate chat message error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
