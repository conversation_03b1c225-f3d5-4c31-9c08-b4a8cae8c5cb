import { NextResponse } from 'next/server';
import supabase from '@/lib/supabase/client';

export async function GET() {
  try {
    // Fetch all active emotes
    const { data: emotes, error: emotesError } = await supabase
      .from('chat_emotes')
      .select('id, name, image_url')
      .eq('is_active', true)
      .order('name');
    
    if (emotesError) {
      console.error('Fetch emotes error:', emotesError);
      return NextResponse.json(
        { error: 'Failed to fetch emotes' },
        { status: 500 }
      );
    }
    
    // Add standard emoji mapping for common ones
    const standardEmojis = [
      { id: 'heart', name: '❤️', image_url: null },
      { id: 'fire', name: '🔥', image_url: null },
      { id: 'rocket', name: '🚀', image_url: null },
      { id: 'diamond', name: '💎', image_url: null },
      { id: 'moon', name: '🌙', image_url: null },
      { id: 'thumbsup', name: '👍', image_url: null },
      { id: 'clap', name: '👏', image_url: null },
      { id: 'party', name: '🎉', image_url: null },
    ];
    
    return NextResponse.json({
      success: true,
      emotes: {
        custom: emotes || [],
        standard: standardEmojis
      }
    });
    
  } catch (error) {
    console.error('Get emotes error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
