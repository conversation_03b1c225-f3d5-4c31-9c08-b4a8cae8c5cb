import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import supabase from '@/lib/supabase/client';
import { createClient } from '@supabase/supabase-js';

// Create server-side Supabase client with service role key for bypassing RLS when needed
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Validation schema for chat message
const sendMessageSchema = z.object({
  streamId: z.string().uuid(),
  message: z.string().min(1).max(500),
  walletAddress: z.string().min(32).max(44),
  emotes: z.array(z.object({
    name: z.string(),
    startIndex: z.number(),
    endIndex: z.number()
  })).optional().default([])
});

// Rate limiting in-memory store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 5; // messages per window
const RATE_WINDOW = 60 * 1000; // 1 minute

function checkRateLimit(walletAddress: string): boolean {
  const now = Date.now();
  const key = walletAddress;
  const limit = rateLimitStore.get(key);

  if (!limit || now > limit.resetTime) {
    // Reset or create new window
    rateLimitStore.set(key, { count: 1, resetTime: now + RATE_WINDOW });
    return true;
  }

  if (limit.count >= RATE_LIMIT) {
    return false;
  }

  limit.count++;
  return true;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const result = sendMessageSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.errors },
        { status: 400 }
      );
    }
    
    const { streamId, message, walletAddress, emotes } = result.data;
    
    // Get wallet auth headers for verification
    const authHeader = request.headers.get('authorization');
    const signature = request.headers.get('x-wallet-signature');
    const authMessage = request.headers.get('x-wallet-message');
    
    if (!authHeader || !signature || !authMessage || !authHeader.includes(walletAddress)) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, username')
      .eq('wallet_address', walletAddress)
      .single();
    
    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }
    
    // Check if stream exists and is live
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select('id, status, creator_id')
      .eq('id', streamId)
      .single();
    
    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }
    
    if (stream.status !== 'live') {
      return NextResponse.json(
        { error: 'Chat is only available for live streams' },
        { status: 400 }
      );
    }
    
    // Check rate limit
    if (!checkRateLimit(walletAddress)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before sending another message.' },
        { status: 429 }
      );
    }
    
    // Validate emotes exist
    if (emotes && emotes.length > 0) {
      const emoteNames = emotes.map(e => e.name);
      const { data: validEmotes, error: emotesError } = await supabase
        .from('chat_emotes')
        .select('name')
        .in('name', emoteNames)
        .eq('is_active', true);
      
      if (emotesError) {
        console.error('Emotes validation error:', emotesError);
        return NextResponse.json(
          { error: 'Failed to validate emotes' },
          { status: 500 }
        );
      }
      
      const validEmoteNames = validEmotes?.map(e => e.name) || [];
      const invalidEmotes = emoteNames.filter(name => !validEmoteNames.includes(name));
      
      if (invalidEmotes.length > 0) {
        return NextResponse.json(
          { error: `Invalid emotes: ${invalidEmotes.join(', ')}` },
          { status: 400 }
        );
      }
    }
    
    // Insert the chat message using admin client to bypass RLS
    const { data: chatMessage, error: insertError } = await supabaseAdmin
      .from('chat_messages')
      .insert({
        stream_id: streamId,
        sender_id: profile.id,
        sender_wallet_address: walletAddress,
        message_content: message,
        emotes: emotes || []
      })
      .select(`
        id,
        message_content,
        emotes,
        created_at,
        sender_id,
        sender_wallet_address,
        profiles:sender_id (
          username,
          avatar_url
        )
      `)
      .single();
    
    if (insertError) {
      console.error('Chat message insert error:', insertError);
      return NextResponse.json(
        { error: 'Failed to send message' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: chatMessage
    });
    
  } catch (error) {
    console.error('Send chat message error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
