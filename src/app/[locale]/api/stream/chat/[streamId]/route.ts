import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import supabase from '@/lib/supabase/client';
import { createClient } from '@supabase/supabase-js';

// Create server-side Supabase client with service role key for reading messages
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Validation schema for query parameters
const getChatSchema = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(50),
  before: z.string().uuid().nullable().optional(), // For pagination - can be null, undefined, or a UUID string
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ streamId: string }> }
) {
  try {
    const { streamId } = await params;
    
    // Validate stream ID
    const streamIdValidation = z.string().uuid().safeParse(streamId);
    if (!streamId || !streamIdValidation.success) {
      console.error('Chat route - Invalid stream ID:', streamId, streamIdValidation.error);
      return NextResponse.json(
        { error: 'Invalid stream ID', receivedId: streamId, validationError: streamIdValidation.error },
        { status: 400 }
      );
    }
    
    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      limit: url.searchParams.get('limit'),
      before: url.searchParams.get('before'),
    };
    
    const result = getChatSchema.safeParse(queryParams);
    
    if (!result.success) {
      console.error('Chat route - Invalid query parameters:', queryParams, result.error);
      return NextResponse.json(
        { error: 'Invalid query parameters', details: result.error.errors, receivedParams: queryParams },
        { status: 400 }
      );
    }
    
    const { limit, before } = result.data;
    
    // Check if stream exists
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select('id, status')
      .eq('id', streamId)
      .single();
    
    
    if (streamError || !stream) {
      console.error('Chat route - Stream not found:', streamId, streamError);
      return NextResponse.json(
        { error: 'Stream not found', streamId, streamError },
        { status: 404 }
      );
    }
    
    let query = supabaseAdmin
      .from('chat_messages')
      .select(`
        id,
        message_content,
        emotes,
        created_at,
        sender_id,
        sender_wallet_address,
        profiles:sender_id (
          username,
          avatar_url
        )
      `)
      .eq('stream_id', streamId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (before && typeof before === 'string') {
      query = query.lt('id', before);
    }
    
    const { data: messages, error: messagesError } = await query;
    
    if (messagesError) {
      console.error('Fetch chat messages error:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch chat messages' },
        { status: 500 }
      );
    }
    
    // Reverse the array to show oldest first (since we ordered by desc for pagination)
    const sortedMessages = messages?.reverse() || [];
    
    return NextResponse.json({
      success: true,
      messages: sortedMessages,
      pagination: {
        hasMore: messages?.length === limit,
        before: messages?.[0]?.id || null
      }
    });
    
  } catch (error) {
    console.error('Get chat messages error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
