import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import supabase from '@/lib/supabase/client';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const deleteMessageSchema = z.object({
  messageId: z.string().uuid(),
  streamId: z.string().uuid()
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const result = deleteMessageSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.errors },
        { status: 400 }
      );
    }
    
    const { messageId, streamId } = result.data;
    
    // Get wallet auth headers for verification
    const authHeader = request.headers.get('authorization');
    const signature = request.headers.get('x-wallet-signature');
    const authMessage = request.headers.get('x-wallet-message');
    
    if (!authHeader || !signature || !authMessage) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Extract wallet address from auth header
    const walletMatch = authHeader.match(/Wallet (.+)/);
    if (!walletMatch) {
      return NextResponse.json(
        { error: 'Invalid authorization header' },
        { status: 401 }
      );
    }
    
    const walletAddress = walletMatch[1];
    
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, username')
      .eq('wallet_address', walletAddress)
      .single();
    
    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }
    
    // Get the message to check ownership and stream association
    const { data: message, error: messageError } = await supabase
      .from('chat_messages')
      .select(`
        *,
        streams!inner(
          creator_id
        )
      `)
      .eq('id', messageId)
      .eq('stream_id', streamId)
      .single();
    
    if (messageError || !message) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }
    
    // Type assertion for the joined streams data
    interface MessageWithStream {
      streams: {
        creator_id: string;
      };
      sender_id: string;
      [key: string]: unknown;
    }
    
    const typedMessage = message as MessageWithStream;
    
    // Check if user can delete this message
    // Users can delete their own messages, creators can delete any message in their stream
    const canDelete = typedMessage.sender_id === profile.id || 
                     typedMessage.streams.creator_id === profile.id;
    
    if (!canDelete) {
      return NextResponse.json(
        { error: 'Not authorized to delete this message' },
        { status: 403 }
      );
    }
    
    // Mark message as deleted (soft delete) using admin client to bypass RLS
    const { error: updateError } = await supabaseAdmin
      .from('chat_messages')
      .update({
        is_deleted: true,
        deleted_by: profile.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', messageId);
    
    if (updateError) {
      console.error('Error deleting message:', updateError);
      return NextResponse.json(
        { error: 'Failed to delete message' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      metadata: {
        timestamp: Date.now(),
        deletedBy: profile.id,
        messageId
      }
    });
    
  } catch (error) {
    console.error('Delete chat message error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
