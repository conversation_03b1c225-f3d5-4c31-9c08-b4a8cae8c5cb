import { NextRequest, NextResponse } from 'next/server';
import { createBonkTipTransaction } from '@/lib/bonk';
import { PublicKey } from '@solana/web3.js';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import { getPlatformFeePercentage } from '@/lib/solana';

// Validate the incoming request data for creating a tip transaction
const createTipSchema = z.object({
  streamId: z.string().uuid().optional().or(z.literal('')), // Allow empty string for profile tips
  amount: z.number().positive(),
  creatorWalletAddress: z.string().min(32).max(44),
  fromWalletAddress: z.string().min(32).max(44),
  tipperName: z.string().max(50).optional(),
  message: z.string().max(280).optional(),
});

// Validate the incoming request data for recording a completed tip
const recordTipSchema = z.object({
  streamId: z.string().uuid().optional().or(z.literal('')), // Allow empty string for profile tips
  amount: z.number().positive(),
  transactionSignature: z.string().min(32),
  creatorWalletAddress: z.string().min(32).max(44),
  fromWalletAddress: z.string().min(32).max(44),
  platformFeePercentage: z.number(),
  platformFeeAmount: z.number(),
  creatorAmount: z.number(),
  tipperName: z.string().max(50).optional(),
  message: z.string().max(280).optional(),
});

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { streamId, amount, creatorWalletAddress, fromWalletAddress, transactionSignature, tipperName, message } = data;

    
    // Check if this is a request to create a transaction or record a completed one
    if (!transactionSignature) {
      // Create transaction request
      try {
        // Validate request data
        createTipSchema.parse(data);
        
       
        // Get wallet public keys
        const creatorPubkey = new PublicKey(creatorWalletAddress);
        const fromWalletPubkey = new PublicKey(fromWalletAddress);
        
        // Create the transaction
        const tipData = await createBonkTipTransaction(
          amount,
          fromWalletPubkey,
          creatorPubkey
        );
        
        
        // Return the transaction for client-side signing
        return NextResponse.json({
          transaction: tipData.transaction.serialize({ requireAllSignatures: false }),
          platformFeePercentage: tipData.platformFeePercentage,
          platformFeeAmount: tipData.platformFeeAmount,
          creatorAmount: tipData.creatorAmount,
        });
      } catch (error) {

        
        return NextResponse.json(
          { error: 'Failed to create tip transaction', details: (error as Error).message },
          { status: 400 }
        );
      }
    } else {
      // Record completed transaction request
      try {
        // Validate request data
        recordTipSchema.parse(data);
        

        
        // Get supabase client
        const supabase = await createClient();
        
        // Get user ID from wallet address (web3 authentication)
        const { data: initialUserData, error } = await supabase
          .from('profiles')
          .select('id, wallet_address, username')
          .eq('wallet_address', fromWalletAddress)
          .single();

        let userData = initialUserData;
        
        if (error || !userData) {
          
          // Try to create the user profile automatically
          const { data: newUserData, error: createError } = await supabase
            .from('profiles')
            .insert({
              wallet_address: fromWalletAddress,
              is_creator: false,
              is_admin: false
            })
            .select('id, wallet_address, username')
            .single();

          if (createError || !newUserData) {
            return NextResponse.json(
              { error: 'Failed to create user profile. Please try connecting your wallet again.' },
              { status: 500 }
            );
          }
          
          // Use the newly created user data
          userData = newUserData;
        }
          

        
        // Get creator ID from wallet address
        const { data: creatorData, error: creatorError } = await supabase
          .from('profiles')
          .select('id, username')
          .eq('wallet_address', creatorWalletAddress)
          .single();
        
        if (creatorError || !creatorData) {

          return NextResponse.json(
            { error: 'Creator not found', details: creatorError?.message },
            { status: 404 }
          );
        }
        
        // Record the tip in the database
        const tipRecord = {
          stream_id: streamId && streamId.trim() !== '' ? streamId : null, // Use null for profile tips
          tipper_id: userData.id,
          recipient_id: creatorData.id,
          amount: amount * Math.pow(10, 5), // Convert to lamports (BONK has 5 decimals)
          platform_fee_percentage: data.platformFeePercentage,
          platform_fee_amount: data.platformFeeAmount,
          creator_amount: data.creatorAmount,
          tx_signature: transactionSignature,
          tipper_name: tipperName?.trim() || null,
          message: message?.trim() || null,
        };
        
        const { data: tipData, error: tipError } = await supabase
          .from('tips')
          .insert(tipRecord)
          .select()
          .single();
        
        if (tipError) {

          throw tipError;
        }
        
        // Update tip count for the stream (only if this is a stream-specific tip)
        if (streamId && streamId.trim() !== '') {
          const { data: streamData, error: streamError } = await supabase
            .from('streams')
            .select('tip_count')
            .eq('id', streamId)
            .single();
            
          if (!streamError && streamData) {
            const newTipCount = (streamData.tip_count || 0) + 1;
            const { error: updateError } = await supabase
              .from('streams')
              .update({ tip_count: newTipCount })
              .eq('id', streamId);
              
            if (updateError) {
              console.warn('Failed to update stream tip count:', updateError);
            }
          }
        } else {

        }
        
        // Generate Solana explorer link
        const isMainnet = process.env.NEXT_PUBLIC_SOLANA_NETWORK !== 'devnet';
        const explorerUrl = isMainnet 
          ? `https://explorer.solana.com/tx/${transactionSignature}`
          : `https://explorer.solana.com/tx/${transactionSignature}?cluster=devnet`;
        

        
        return NextResponse.json({
          success: true,
          tip: tipData,
          explorerUrl, // Include Solana explorer link
          message: `Tip sent successfully! 🎉 View transaction: ${explorerUrl}`
        });
      } catch (error) {

        
        return NextResponse.json(
          { error: 'Failed to record tip transaction', details: (error as Error).message },
          { status: 500 }
        );
      }
    }
  } catch (error) {

    
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}

// Get tip statistics for a stream
export async function GET(request: NextRequest) {
  try {
    // Get stream ID from query parameter
    const searchParams = request.nextUrl.searchParams;
    const streamId = searchParams.get('streamId');
    
    if (!streamId) {
      return NextResponse.json(
        { error: 'Stream ID is required' },
        { status: 400 }
      );
    }
    
    
    // Get supabase client
    const supabase = await createClient();
    
    // Get tip statistics with enhanced data
    const { data, error } = await supabase
      .from('tips')
      .select(`
        id,
        amount,
        platform_fee_percentage,
        platform_fee_amount,
        creator_amount,
        created_at,
        tipper_name,
        message,
        tipper:tipper_id(id, username, wallet_address)
      `)
      .eq('stream_id', streamId)
      .order('created_at', { ascending: false });
    
    if (error) {

      throw error;
    }
    
    // Calculate total tips
    const totalAmount = data.reduce((sum: number, tip: { amount: number }) => sum + Number(tip.amount), 0);
    const totalCreatorAmount = data.reduce((sum: number, tip: { creator_amount: number }) => sum + Number(tip.creator_amount), 0);
    const totalFeeAmount = data.reduce((sum: number, tip: { platform_fee_amount: number }) => sum + Number(tip.platform_fee_amount), 0);
    
    const statistics = {
      tipCount: data.length,
      totalAmount,
      totalCreatorAmount,
      totalFeeAmount,
      currentFeePercentage: getPlatformFeePercentage(),
      averageTipAmount: data.length > 0 ? totalAmount / data.length : 0,
      uniqueTippers: new Set(data.map(tip => tip.tipper?.[0]?.id).filter(Boolean)).size,
      tipsWithMessages: data.filter(tip => tip.message).length,
      tipsWithNames: data.filter(tip => tip.tipper_name).length,
    };

    return NextResponse.json({
      tips: data,
      statistics
    });
  } catch (error) {

    
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 