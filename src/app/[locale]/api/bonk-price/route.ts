import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Fetch BONK price from CoinGecko API
    const response = await fetch(
      'https://api.coingecko.com/api/v3/simple/price?ids=bonk&vs_currencies=usd&include_24hr_change=true',
      {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'bonkstream/1.0'
        },
        // Cache for 30 seconds to avoid rate limiting
        next: { revalidate: 30 }
      }
    );
    
    if (!response.ok) {
      throw new Error(`CoinGecko API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!data.bonk) {
      throw new Error('BONK data not found in response');
    }
    
    // Return formatted data
    return NextResponse.json({
      success: true,
      data: {
        price: data.bonk.usd,
        change24h: data.bonk.usd_24h_change || 0,
        change24hPercent: data.bonk.usd_24h_change || 0
      }
    });
    
  } catch (error) {
    console.error('Error fetching BONK price:', error);
    
    // Return fallback data on error (graceful degradation)
    return NextResponse.json({
      success: false,
      data: {
        price: 0.000023,
        change24h: 5.67,
        change24hPercent: 5.67
      },
      error: 'Failed to fetch live price, showing fallback data'
    });
  }
} 