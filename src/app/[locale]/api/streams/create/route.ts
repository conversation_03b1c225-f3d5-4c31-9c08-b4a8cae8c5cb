import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { livepeerProvider } from '@/lib/video-providers/livepeer';
import { verifyWalletSignature } from '@/lib/auth';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create service role client for operations that bypass RLS
function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  
  return createSupabaseClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Get wallet address from headers (simplified auth for logged in creators)
    const walletAddress = request.headers.get('Authorization')?.replace('Wallet ', '') || 
                         request.headers.get('x-wallet-address');
    const signature = request.headers.get('x-wallet-signature');
    const message = request.headers.get('x-wallet-message');

    if (!walletAddress) {
      return NextResponse.json(
        { error: 'Wallet address required' },
        { status: 401 }
      );
    }

    // For stream creation by authenticated users, signature verification is optional
    // This allows users who are already authenticated to create streams without re-signing
    if (signature && message) {
      const isValidSignature = await verifyWalletSignature(walletAddress, signature, message);
      if (!isValidSignature) {
        console.warn('Invalid signature provided, but allowing stream creation for authenticated user');
      }
    }

    // Get user profile to verify creator status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', walletAddress)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Type assertion for profile
    const profileData = profile as { id: string; is_creator: boolean };

    if (!profileData.is_creator) {
      // Auto-upgrade user to creator status
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_creator: true })
        .eq('id', profileData.id);

      if (updateError) {
        console.error('Error upgrading to creator:', updateError);
        return NextResponse.json(
          { error: 'Failed to upgrade to creator status' },
          { status: 500 }
        );
      }
      
      // Update local profile data
      profileData.is_creator = true;
    }

    // Parse request body
    const { name, title, description, record = false } = await request.json();

    // Use name or title (some flexibility for client implementations)
    const streamName = name || title;

    if (!streamName) {
      return NextResponse.json(
        { error: 'Stream name/title is required' },
        { status: 400 }
      );
    }

    // Create stream on Livepeer
    const livepeerStream = await livepeerProvider.createStream({
      name: streamName,
      record,
      profiles: [
        {
          name: '1080p',
          bitrate: 4000000,
          fps: 30,
          width: 1920,
          height: 1080
        },
        {
          name: '720p',
          bitrate: 2000000,
          fps: 30,
          width: 1280,
          height: 720
        },
        {
          name: '480p',
          bitrate: 1000000,
          fps: 30,
          width: 854,
          height: 480
        }
      ]
    });

    // Store stream in database using service role client to bypass RLS
    const serviceRoleClient = createServiceRoleClient();
    const { data: streamRecord, error: streamError } = await serviceRoleClient
      .from('streams')
      .insert({
        creator_id: profileData.id,
        title: streamName,
        description: description || '',
        status: 'live',
        is_active: true,  // Mark stream as actively broadcasting
        storage_provider: 'livepeer',
        livepeer_stream_id: livepeerStream.id,
        livepeer_playback_id: livepeerStream.playbackId,
        livepeer_stream_key: livepeerStream.streamKey,
        playback_url: `https://playback.livepeer.studio/hls/${livepeerStream.playbackId}/index.m3u8`,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (streamError) {
      console.error('Error storing stream:', streamError);
      return NextResponse.json(
        { error: 'Failed to store stream' },
        { status: 500 }
      );
    }

    // Type assertion for stream record
    const streamData = streamRecord as { 
      id: string; 
      title: string; 
      description?: string;
      status: string;
      livepeer_stream_id: string; 
      livepeer_playback_id: string;
      rtmp_ingest_url: string;
      stream_key: string;
      playback_url: string;
    };

    // Return stream data with RTMP credentials
    return NextResponse.json({
      success: true,
      // Include both detailed stream object and direct access properties for backward compatibility
      streamKey: livepeerStream.streamKey,
      playbackId: livepeerStream.playbackId,
      stream: {
        id: streamData.id,
        title: streamData.title,
        description: streamData.description,
        status: streamData.status,
        playbackUrl: streamData.playback_url,
        // RTMP streaming details for the creator
        rtmp: {
          url: 'rtmp://rtmp.livepeer.com/live',
          streamKey: livepeerStream.streamKey
        },
        // For now, use RTMP for WebRTC fallback as Livepeer's WHIP endpoint may not be available
        whip: {
          url: 'rtmp://rtmp.livepeer.com/live',  // Use RTMP as fallback
          streamKey: livepeerStream.streamKey
        },
        livepeer: {
          streamId: livepeerStream.id,
          playbackId: livepeerStream.playbackId,
          streamKey: livepeerStream.streamKey
        }
      }
    });

  } catch (error) {
    console.error('Error creating stream:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
