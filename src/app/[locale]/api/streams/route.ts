import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { livepeerProvider } from '@/lib/video-providers/livepeer';

// Add caching headers for better performance
const CACHE_DURATION = 30; // 30 seconds cache for streams

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20); // Limit max to 20
    const sortBy = searchParams.get('sort') || 'latest';
    const creatorId = searchParams.get('creator');
    const status = searchParams.get('status');  // 'live', 'ended', 'all'
    
    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Base query
    if (!supabase) {
      throw new Error('Failed to create Supabase client');
    }
    
    let query = supabase
      .from('streams')
      .select(`
        *,
        creator:creator_id(id, username, wallet_address, avatar_url)
      `);
    
    // Add filters
    if (creatorId) {
      query = query.eq('creator_id', creatorId);
    }
    
    if (status) {
      if (status === 'live') {
        query = query.eq('status', 'live');
      } else if (status === 'ended') {
        query = query.eq('status', 'ended');
      }
      // If 'all', don't add a filter
    }
    
    // Add sorting
    switch (sortBy) {
      case 'popular':
        query = query.order('view_count', { ascending: false });
        break;
      case 'trending':
        // For trending, we could look at recent tips or views
        query = query.order('created_at', { ascending: false }).order('view_count', { ascending: false });
        break;
      case 'live':
        // For live streams, sort by most recently started
        query = query.eq('status', 'live').order('created_at', { ascending: false });
        break;
      case 'latest':
      default:
        query = query.order('created_at', { ascending: false });
        break;
    }
    
    // Add pagination
    query = query.range(offset, offset + limit - 1);
    
    // Execute query
    const { data, error } = await query;
    
    if (error) {
      throw error;
    }
    
    // OPTIMIZED: Batch all database queries to avoid N+1 problem
    const streamIds = data.map(stream => stream.id);
    
    // Get all tip counts in one query
    const { data: tipCounts, error: tipCountError } = await supabase
      .from('tips')
      .select('stream_id')
      .in('stream_id', streamIds);
    
    if (tipCountError) {
      console.warn('Failed to get tip counts:', tipCountError);
    }
    
    // Get all NFT counts in one query  
    const { data: nftCounts, error: nftCountError } = await supabase
      .from('nfts')
      .select('stream_id')
      .in('stream_id', streamIds);
    
    if (nftCountError) {
      console.warn('Failed to get NFT counts:', nftCountError);
    }
    
    // Create count maps for efficient lookups
    const tipCountMap = tipCounts?.reduce((acc, tip) => {
      acc[tip.stream_id] = (acc[tip.stream_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};
    
    const nftCountMap = nftCounts?.reduce((acc, nft) => {
      acc[nft.stream_id] = (acc[nft.stream_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};
    
    // Process the data with cached counts
    const streams = data.map((stream: { 
      id: string; 
      status: string;
      livepeer_stream_id?: string;
      like_count?: number;
      [key: string]: unknown;
    }) => ({
      ...stream,
      tip_count: tipCountMap[stream.id] || 0,
      nft_count: nftCountMap[stream.id] || 0,
      like_count: stream.like_count || 0
    }));
    
    // OPTIMIZED: Only check live status for discovery page requests or when specifically needed
    const shouldCheckLiveStatus = !searchParams.get('skip_live_check');
    
    if (shouldCheckLiveStatus) {
      // Check live status for streams marked as live in a more efficient way
      const liveStreamIds = streams
        .filter(stream => stream.status === 'live' && stream.livepeer_stream_id)
        .map(stream => stream.livepeer_stream_id!)
        .filter(id => id); // Remove any undefined values
        
      const liveStatusMap: Record<string, boolean> = {};
      
      if (liveStreamIds.length > 0) {
        try {
          // Batch request live status checks with timeout and error handling
          const liveStatusChecks = await Promise.allSettled(
            liveStreamIds.map(async (streamId) => {
              try {
                // Add timeout to prevent hanging requests
                const timeoutPromise = new Promise<{ streamId: string; isLive: boolean }>((_, reject) => {
                  setTimeout(() => reject(new Error('Timeout')), 3000); // 3 second timeout
                });
                
                const statusPromise = livepeerProvider.isStreamLive(streamId).then(isLive => ({
                  streamId,
                  isLive
                }));
                
                return await Promise.race([statusPromise, timeoutPromise]);
              } catch (error) {
                // If check fails, assume stream is not live
                console.warn(`Failed to check live status for stream ${streamId}:`, error);
                return { streamId, isLive: false };
              }
            })
          );
          
          // Process results
          liveStatusChecks.forEach((result) => {
            if (result.status === 'fulfilled') {
              const { streamId, isLive } = result.value;
              liveStatusMap[streamId] = isLive;
            } else {
              console.warn('Live status check failed:', result.reason);
            }
          });
        } catch (liveCheckError) {
          console.error('Error checking live status:', liveCheckError);
          // Continue without live status checks rather than failing the entire request
        }
      }
      
      // Update streams with actual live status
      const updatedStreams = streams.map(stream => {
        if (stream.status === 'live' && stream.livepeer_stream_id) {
          return {
            ...stream,
            is_actually_live: liveStatusMap[stream.livepeer_stream_id] || false
          };
        }
        return stream;
      });
      
      // Get total count for pagination (only when needed)
      const { count, error: countError } = await supabase
        .from('streams')
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        throw countError;
      }
      
      // Return streams with pagination info and caching headers
      const response = NextResponse.json({
        streams: updatedStreams,
        pagination: {
          page,
          limit,
          total: count,
          totalPages: Math.ceil((count || 0) / limit)
        },
        hasMore: offset + streams.length < (count || 0)
      });
      
      // Set cache headers for better performance
      response.headers.set('Cache-Control', `public, s-maxage=${CACHE_DURATION}, stale-while-revalidate=60`);
      
      return response;
    } else {
      // Skip live status check for better performance
      const { count, error: countError } = await supabase
        .from('streams')
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        throw countError;
      }
      
      const response = NextResponse.json({
        streams,
        pagination: {
          page,
          limit,
          total: count,
          totalPages: Math.ceil((count || 0) / limit)
        },
        hasMore: offset + streams.length < (count || 0)
      });
      
      // Set longer cache for requests without live status
      response.headers.set('Cache-Control', `public, s-maxage=${CACHE_DURATION * 2}, stale-while-revalidate=120`);
      
      return response;
    }
  } catch (error) {
    console.error('Error fetching streams:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    );
  }
}