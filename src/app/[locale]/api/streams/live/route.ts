import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '12');
    const page = parseInt(searchParams.get('page') || '1');
    
    // Create Supabase client
    const supabase = await createClient();
    if (!supabase) {
      throw new Error('Failed to create Supabase client');
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    
    // Fetch live streams with creator info
    const { data: liveStreams, error: liveStreamsError } = await supabase
      .from('streams')
      .select('*, creator:creator_id(username, avatar_url, wallet_address)')
      .eq('status', 'live')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (liveStreamsError) {
      console.error('Error fetching live streams:', liveStreamsError);
      return NextResponse.json(
        { error: 'Failed to fetch live streams' },
        { status: 500 }
      );
    }
    
    // Get live stream count in single query (reusing filter)
    const { count, error: countError } = await supabase
      .from('streams')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'live');

    if (countError) {
      console.error('Error counting live streams:', countError);
    }

    return NextResponse.json({
      streams: liveStreams,
      pagination: {
        total: count || 0,
        page,
        limit,
        pages: count ? Math.ceil(count / limit) : 0
      }
    });
  } catch (error) {
    console.error('Error fetching live streams:', error);
    return NextResponse.json(
      { error: 'Server error' },
      { status: 500 }
    );
  }
}
