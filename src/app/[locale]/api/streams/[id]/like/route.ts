import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create service role client for operations that bypass RLS
function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  
  return createSupabaseClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id: streamId } = await params;
    const body = await request.json();
    const { walletAddress } = body;

    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Validate wallet address
    if (!walletAddress) {
      return NextResponse.json(
        { error: 'Wallet address required' },
        { status: 400 }
      );
    }

    // Get user profile by wallet address
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', walletAddress)
      .single();

    let userProfile = profile;

    if (error || !profile) {
      // Try to create the user profile automatically if not found
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert({
          wallet_address: walletAddress,
          is_creator: false,
          is_admin: false
        })
        .select('id')
        .single();
      
      if (createError || !newProfile) {
        return NextResponse.json(
          { error: 'Failed to create user profile' },
          { status: 500 }
        );
      }
      
      // Use the newly created profile
      userProfile = newProfile;
    }

    // At this point userProfile should never be null
    if (!userProfile) {
      return NextResponse.json(
        { error: 'Failed to get user profile' },
        { status: 500 }
      );
    }

    // Check if stream exists
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select('id, like_count')
      .eq('id', streamId)
      .single();

    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }

    // Use service role client for like operations to bypass RLS
    const serviceRoleClient = createServiceRoleClient();
    
    // Check if user has already liked this stream
    const { data: existingLike } = await serviceRoleClient
      .from('stream_likes')
      .select('id')
      .eq('stream_id', streamId)
      .eq('user_id', userProfile.id)
      .single();

    let isLiked = false;

    if (existingLike) {
      // Unlike: Remove the like
      const { error: deleteError } = await serviceRoleClient
        .from('stream_likes')
        .delete()
        .eq('stream_id', streamId)
        .eq('user_id', userProfile.id);

      if (deleteError) {
        throw deleteError;
      }

      isLiked = false;
    } else {
      // Like: Add the like
      const { error: insertError } = await serviceRoleClient
        .from('stream_likes')
        .insert({
          stream_id: streamId,
          user_id: userProfile.id
        });

      if (insertError) {
        throw insertError;
      }

      isLiked = true;
    }

    // Get updated like count from database after the triggers have run
    const { data: updatedStream } = await serviceRoleClient
      .from('streams')
      .select('like_count')
      .eq('id', streamId)
      .single();

    return NextResponse.json({
      success: true,
      isLiked,
      likeCount: updatedStream?.like_count || 0
    });

  } catch (error) {
    console.error('Error toggling stream like:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id: streamId } = await params;
    const url = new URL(request.url);
    const walletAddress = url.searchParams.get('walletAddress');
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    let isLiked = false;

    if (walletAddress) {
      // Get user profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', walletAddress)
        .single();

      if (profile) {
        // Use service role client to check likes
        const serviceRoleClient = createServiceRoleClient();
        const { data: existingLike } = await serviceRoleClient
          .from('stream_likes')
          .select('id')
          .eq('stream_id', streamId)
          .eq('user_id', profile.id)
          .single();

        isLiked = !!existingLike;
      }
    }

    // Get stream like count
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select('like_count')
      .eq('id', streamId)
      .single();

    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      isLiked,
      likeCount: stream.like_count || 0
    });

  } catch (error) {
    console.error('Error fetching stream like status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
