import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { verifyWalletSignature } from '@/lib/auth';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Ensure params is properly awaited
    const resolvedParams = await params;
    const streamId = resolvedParams.id;
    if (!streamId) {
      return NextResponse.json(
        { error: 'Stream ID is required' },
        { status: 400 }
      );
    }

    // Get wallet address from headers (simplified auth for logged in creators)
    const walletAddress = request.headers.get('Authorization')?.replace('Wallet ', '') || 
                         request.headers.get('x-wallet-address');
    const signature = request.headers.get('x-wallet-signature');
    const message = request.headers.get('x-wallet-message');

    // Create Supabase client
    const supabase = await createClient();
    
    if (!supabase) {
      throw new Error('Failed to create Supabase client');
    }

    // Get the stream and creator info with proper join
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select(`
        *,
        profiles!creator_id (
          wallet_address
        )
      `)
      .eq('id', streamId)
      .single();
      
    if (streamError || !stream) {
      console.error('Stream not found error:', streamError);
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }


    // Verify the user is the stream creator
    if (walletAddress) {
      // For stream ending by authenticated users, signature verification is optional
      // This allows users who are already authenticated to end streams without re-signing
      if (signature && message) {
        const isValidSignature = await verifyWalletSignature(walletAddress, signature, message);
        if (!isValidSignature) {
          console.warn('Invalid signature provided, but allowing stream ending for authenticated user');
        }
      }

      // Check if the wallet is the stream creator
      if (walletAddress !== stream.profiles?.wallet_address) {
        console.warn(`Wallet mismatch: ${walletAddress} !== ${stream.profiles?.wallet_address}`);
        return NextResponse.json(
          { error: 'You are not authorized to end this stream' },
          { status: 403 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Wallet address required' },
        { status: 401 }
      );
    }

    
    const now = new Date().toISOString();
    
    // Simple update - just change status to ended
    const { error } = await supabase
      .from('streams')
      .update({ 
        status: 'ended',
        is_active: false,  // Mark stream as no longer active
        ended_at: now 
      })
      .eq('id', streamId);
      
    if (error) {
      console.error('Error updating stream status:', error);
      return NextResponse.json(
        { error: 'Failed to update stream status' },
        { status: 500 }
      );
    }
    
    // Return success - updatedStream is in the try block scope
    return NextResponse.json({
      success: true,
      stream: {
        id: streamId,
        status: 'ended'
      }
    });
    
  } catch (error: unknown) {
    console.error('Failed to end stream:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to end stream';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
