import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { livepeerProvider } from '@/lib/video-providers/livepeer';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; locale: string }> }
) {
  try {
    const { id } = await params;
    const searchParams = request.nextUrl.searchParams;
    const statsType = searchParams.get('stats'); // 'live' for go-live page stats
    
    if (!id) {
      return NextResponse.json(
        { error: 'Stream ID is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createClient();
    
    if (!supabase) {
      throw new Error('Failed to create Supabase client');
    }

    // Get stream data with creator information
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select(`
        *,
        creator:creator_id(*)
      `)
      .eq('id', id)
      .single();

    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }

    // If requesting live stats (for go-live page), provide comprehensive stats
    if (statsType === 'live') {
      try {
        // Get viewer count from stream_viewer_count table (real-time tracking)
        let viewerCount = 0;
        if (stream.livepeer_stream_id && stream.status === 'live') {
          try {
            // Check if stream is actually live first
            const livepeerStream = await livepeerProvider.getStream(stream.livepeer_stream_id);
            const isActuallyLive = await livepeerProvider.isStreamLive(stream.livepeer_stream_id);
            
            if (isActuallyLive && livepeerStream) {
              // Get current viewer count from real-time tracking table
              try {
                const { data: viewerData, error: viewerError } = await supabase
                  .from('stream_viewer_count')
                  .select('viewer_count')
                  .eq('stream_id', id)
                  .single();
                
                if (!viewerError && viewerData) {
                  viewerCount = viewerData.viewer_count || 0;
                } else {
                  viewerCount = 0;
                }
              } catch {
                viewerCount = 0;
              }
            } else {
              viewerCount = 0;
            }
          } catch (error) {
            console.warn('Could not get Livepeer stream data:', error);
            viewerCount = 0;
          }
        }

        // Get tip statistics for this stream
        const { data: tipsData, error: tipsError } = await supabase
          .from('tips')
          .select('amount, creator_amount, platform_fee_amount, platform_fee_percentage')
          .eq('stream_id', id);

        if (tipsError) {
          console.error('Error fetching tip data:', tipsError);
          // Continue with empty tip data rather than failing
        }

        // Calculate statistics
        const tips = tipsData || [];
        const tipCount = tips.length;
        const totalCreatorAmount = tips.reduce((sum, tip) => sum + Number(tip.creator_amount || 0), 0);
        
        // Convert from lamports to BONK (5 decimals)
        const totalEarnings = Math.round(totalCreatorAmount / Math.pow(10, 5));

        return NextResponse.json({
          success: true,
          liveStats: {
            viewerCount,
            tipCount,
            totalEarnings,
            isLive: stream.status === 'live'
          }
        });
      } catch (error) {
        console.error('Error fetching live stats:', error);
        return NextResponse.json(
          { error: 'Failed to fetch live stats' },
          { status: 500 }
        );
      }
    }

    // Regular stream data response (existing functionality)
    // Record view if not requesting stats
    if (!statsType) {
      const { error: viewError } = await supabase.rpc('increment_view_count', {
        stream_id: id
      });

      if (viewError) {
        console.error('Error incrementing view count:', viewError);
        // Don't fail the request if view count increment fails
      }
    }

    // Check actual live status for live streams
    let isActuallyLive = false;
    if (stream.status === 'live' && stream.livepeer_stream_id) {
      try {
        // Add timeout to prevent hanging requests
        const timeoutPromise = new Promise<boolean>((_, reject) => {
          setTimeout(() => reject(new Error('Timeout')), 3000); // 3 second timeout
        });
        
        const statusPromise = livepeerProvider.isStreamLive(stream.livepeer_stream_id);
        
        isActuallyLive = await Promise.race([statusPromise, timeoutPromise]);
      } catch (error) {
        console.warn(`Failed to check live status for stream ${id}:`, error);
        isActuallyLive = false; // Default to not live if check fails
      }
    }

    // Process the stream data - use database column values for counts
    const processedStream = {
      ...stream,
      tip_count: stream.tip_count || 0,
      nft_count: stream.nft_count || 0,
      is_actually_live: isActuallyLive // Add actual live status
    };

    return NextResponse.json({
      success: true,
      stream: processedStream
    });
  } catch (error) {
    console.error('Error fetching stream:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH endpoint for updating stream metadata (e.g., incrementing view count)
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string; locale: string }> }
) {
  try {
    const { id } = await context.params;
    const body = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'Stream ID is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createClient();
    
    if (!supabase) {
      throw new Error('Failed to create Supabase client');
    }

    // Handle different update types
    if (body.action === 'increment_view') {
      // First get current view count
      const { data: currentStream, error: fetchError } = await supabase
        .from('streams')
        .select('view_count')
        .eq('id', id)
        .single();

      if (fetchError) {
        console.error('Error fetching current stream:', fetchError);
        throw fetchError;
      }

      // Increment view count
      const newViewCount = (currentStream?.view_count || 0) + 1;
      
      const { data, error } = await supabase
        .from('streams')
        .update({ 
          view_count: newViewCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error incrementing view count:', error);
        throw error;
      }

      return NextResponse.json({
        success: true,
        message: 'View count incremented',
        stream: data
      });
    }

    return NextResponse.json(
      { error: 'Invalid action specified' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error updating stream:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update stream',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string; locale: string }> }
) {
  try {
    const { id } = await context.params;
    const supabase = await createClient();

    // Get wallet address from request body for wallet-based auth
    const body = await request.json();
    const walletAddress = body.walletAddress;
    
    if (!walletAddress) {
      return NextResponse.json(
        { error: 'Wallet address required for authentication' },
        { status: 401 }
      );
    }

    // Get the stream to verify ownership
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select('creator_id, status, livepeer_stream_id')
      .eq('id', id)
      .single();

    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }

    // Get user profile by wallet address to verify they are the creator
    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('wallet_address', walletAddress)
      .single();

    if (!profile || profile.id !== stream.creator_id) {
      return NextResponse.json(
        { error: 'Unauthorized - You can only delete your own streams' },
        { status: 403 }
      );
    }

    // Check if stream is actually live before preventing deletion
    let isActuallyLive = false;
    if (stream.status === 'live' && stream.livepeer_stream_id) {
      try {
        isActuallyLive = await livepeerProvider.isStreamLive(stream.livepeer_stream_id);
      } catch (liveCheckError) {
        console.warn(`Failed to check live status for stream ${id}:`, liveCheckError);
        isActuallyLive = false; // Default to not live if check fails
      }
    }

    // Don't allow deletion of actually live streams
    if (isActuallyLive) {
      return NextResponse.json(
        { error: 'Cannot delete a stream that is currently live' },
        { status: 400 }
      );
    }

    // Delete the stream (this will cascade to delete related tips and NFTs due to foreign key constraints)
    const { error: deleteError } = await supabase
      .from('streams')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting stream:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete stream' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error in DELETE /api/streams/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
