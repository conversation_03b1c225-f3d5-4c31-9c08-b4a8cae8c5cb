import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create service role client for operations that bypass RLS
function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  
  return createSupabaseClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string; locale: string }> }
) {
  try {
    const { id: streamId } = await context.params;
    
    if (!streamId) {
      return NextResponse.json(
        { error: 'Stream ID is required' },
        { status: 400 }
      );
    }
    
    // Get user information from request body or headers
    let body: { userId?: string; user_id?: string } = {};
    try {
      const text = await request.text();
      if (text.trim()) {
        body = JSON.parse(text);
      }
    } catch {
      // If JSON parsing fails, continue with empty body
    }
    const userId = body.userId || body.user_id || null;
    
    // Get user information (if authenticated)
    const supabase = await createClient();
    const { data: sessionData } = await supabase.auth.getSession();
    const sessionUserId = sessionData.session?.user?.id;
    
    // Use either session user or provided user ID
    const finalUserId = sessionUserId || userId;
    
    // First, verify the stream exists and get current view count
    const { data: streamData, error: streamError } = await supabase
      .from('streams')
      .select('id, view_count')
      .eq('id', streamId)
      .single();

    if (streamError) {
      console.error('Error checking stream:', streamError);
      if (streamError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Stream not found' },
          { status: 404 }
        );
      }
      throw streamError;
    }

    // Increment view count in the streams table using service role to bypass RLS
    const serviceSupabase = createServiceRoleClient();
    const newViewCount = (streamData.view_count || 0) + 1;
    
    const { data: updateData, error: updateError } = await serviceSupabase
      .from('streams')
      .update({ 
        view_count: newViewCount
      })
      .eq('id', streamId)
      .select('view_count');

    if (updateError) {
      console.error('Error incrementing view count:', updateError);
      return NextResponse.json({
        success: false,
        error: 'Failed to increment view count',
        details: updateError.message
      }, { status: 500 });
    }

    const updatedViewCount = updateData?.[0]?.view_count || newViewCount;

    // Try to record view in the database for analytics (optional)
    const viewData = {
      stream_id: streamId,
      viewer_id: finalUserId || null, // Can be anonymous view
      view_timestamp: new Date().toISOString(),
      ip_hash: hashIp(request), // For analytics
      user_agent: request.headers.get('user-agent') || null,
    };
    
    try {
      await serviceSupabase
        .from('stream_views')
        .insert(viewData);
    } catch (viewError) {
      console.error('Error recording view detail (table may not exist):', viewError);
      // Continue execution - this is not critical
    }

    return NextResponse.json({ 
      success: true,
      view_count: updatedViewCount 
    });
  } catch (error) {
    console.error('Error recording view:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}

// Helper function to hash IP for privacy
function hashIp(request: NextRequest): string {
  // Get IP from request
  const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '';
  
  // Create a simple hash of the IP
  let hash = 0;
  for (let i = 0; i < ip.length; i++) {
    const char = ip.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  // Return as string
  return hash.toString(16);
} 