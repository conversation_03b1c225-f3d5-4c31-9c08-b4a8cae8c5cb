import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { livepeerProvider } from '@/lib/video-providers/livepeer';
import { verifyWalletSignature } from '@/lib/auth';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Create service role client for operations that bypass RLS
function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  
  return createSupabaseClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Use service role client for public read access to stream status
    const supabase = createServiceRoleClient();
    
    const { id } = await params;

    // Get stream from database - try by database ID first
    let { data: stream, error: streamError } = await supabase
      .from('streams')
      .select('*')
      .eq('id', id)
      .single();

    // If not found by database ID, try by livepeer_stream_id as fallback
    if (streamError || !stream) {
      const { data: streamByLivepeer, error: livepeerError } = await supabase
        .from('streams')
        .select('*')
        .eq('livepeer_stream_id', id)
        .single();
      
      if (!livepeerError && streamByLivepeer) {
        stream = streamByLivepeer;
        streamError = null;
      }
    }

    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }

    // Type assertion with proper interface
    const streamData = stream as { 
      livepeer_stream_id?: string;
      livepeer_playback_id?: string;
      status?: string;
    };

    if (!streamData.livepeer_stream_id) {
      return NextResponse.json(
        { error: 'Not a Livepeer stream' },
        { status: 400 }
      );
    }

    // Get live status from Livepeer
    try {
      const livepeerStream = await livepeerProvider.getStream(streamData.livepeer_stream_id);
      
      if (!livepeerStream) {
        const { error: updateError } = await supabase
          .from('streams')
          .update({ 
            status: 'ended',
            ended_at: new Date().toISOString()
          })
          .eq('id', id);
          
        if (updateError) {
          console.error('Error updating stream status:', updateError);
        }
        
        return NextResponse.json({
          isActive: false,
          lastSeen: 0,
          viewerCount: 0,
          sourceSegments: 0,
          transcodedSegments: 0,
          streamId: streamData.livepeer_stream_id,
          playbackId: streamData.livepeer_playback_id
        });
      }
      
      return NextResponse.json({
        isActive: livepeerStream.isActive,
        lastSeen: livepeerStream.lastSeen,
        viewerCount: 0, // Livepeer doesn't provide real-time viewer count in basic plan
        sourceSegments: livepeerStream.sourceSegments,
        transcodedSegments: livepeerStream.transcodedSegments,
        streamId: livepeerStream.id,
        playbackId: livepeerStream.playbackId
      });
    } catch (livepeerError) {
      console.error('Error fetching stream status from Livepeer:', livepeerError);
      
      // Fallback to database status
      return NextResponse.json({
        isActive: streamData.status === 'live',
        lastSeen: 0,
        viewerCount: 0,
        sourceSegments: 0,
        transcodedSegments: 0,
        streamId: streamData.livepeer_stream_id,
        playbackId: streamData.livepeer_playback_id
      });
    }

  } catch (error) {
    console.error('Error checking stream status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    const { id } = await params;
    
    // Get authenticated user from headers (wallet auth)
    const walletAddress = request.headers.get('Authorization')?.replace('Wallet ', '');
    const signature = request.headers.get('x-wallet-signature');
    const message = request.headers.get('x-wallet-message');

    if (!walletAddress || !signature || !message) {
      return NextResponse.json(
        { error: 'Wallet authentication required' },
        { status: 401 }
      );
    }

    // Verify the wallet signature
    const isValidSignature = await verifyWalletSignature(walletAddress, signature, message);
    if (!isValidSignature) {
      return NextResponse.json(
        { error: 'Invalid wallet signature' },
        { status: 401 }
      );
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', walletAddress)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Parse request body
    const { status, isActive } = await request.json();

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Use service role client to update stream status
    const serviceRoleClient = createServiceRoleClient();
    const { data: streamRecord, error: streamError } = await serviceRoleClient
      .from('streams')
      .update({
        status: status,
        is_active: isActive || false,
        started_at: status === 'live' ? new Date().toISOString() : null,
        ended_at: status === 'ended' ? new Date().toISOString() : null
      })
      .eq('id', id)
      .eq('creator_id', (profile as { id: string }).id) // Ensure user owns the stream
      .select()
      .single();

    if (streamError) {
      console.error('Error updating stream status:', streamError);
      return NextResponse.json(
        { error: 'Failed to update stream status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      stream: streamRecord
    });

  } catch (error) {
    console.error('Error updating stream status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
