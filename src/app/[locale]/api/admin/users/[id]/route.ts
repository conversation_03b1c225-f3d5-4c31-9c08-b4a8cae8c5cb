import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; locale: string }> }
) {
  try {
    const { id } = await params;
    
    // Check admin authentication using wallet headers
    const authHeader = request.headers.get('authorization');
    const signatureHeader = request.headers.get('x-wallet-signature');
    const messageHeader = request.headers.get('x-wallet-message');
    
    if (!authHeader || !authHeader.startsWith('Wallet ') || !signatureHeader || !messageHeader) {
      return NextResponse.json(
        { error: 'Unauthorized - Missing wallet authentication' },
        { status: 401 }
      );
    }
    
    const walletAddress = authHeader.substring(7);
    
    // Verify signature
    const { verifyWalletSignature } = await import('@/lib/auth');
    const isSignatureValid = await verifyWalletSignature(walletAddress, signatureHeader, messageHeader);
    
    if (!isSignatureValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid signature' },
        { status: 401 }
      );
    }
    
    // Check admin status
    const { checkAdminStatus } = await import('@/lib/auth');
    const isAdmin = await checkAdminStatus(walletAddress);
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }
    
    const supabase = await createClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // First, check if the user exists
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single();
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Prevent deletion of other admins (optional security measure)
    if (user.is_admin && user.wallet_address !== walletAddress) {
      return NextResponse.json(
        { error: 'Cannot delete other admin users' },
        { status: 403 }
      );
    }
    
    // Delete user data in the correct order to handle foreign key constraints
    // Start with dependent data first
    
    // Delete tips where user is tipper or recipient
    const { error: tipsError } = await supabase
      .from('tips')
      .delete()
      .or(`tipper_id.eq.${id},recipient_id.eq.${id}`);
    
    if (tipsError) {
      console.error('Error deleting tips:', tipsError);
      return NextResponse.json(
        { error: 'Failed to delete user tips' },
        { status: 500 }
      );
    }
    
    // Delete NFTs owned by user
    const { error: nftsError } = await supabase
      .from('nfts')
      .delete()
      .eq('owner_id', id);
    
    if (nftsError) {
      console.error('Error deleting NFTs:', nftsError);
      return NextResponse.json(
        { error: 'Failed to delete user NFTs' },
        { status: 500 }
      );
    }
    
    // Delete streams created by user
    const { error: streamsError } = await supabase
      .from('streams')
      .delete()
      .eq('creator_id', id);
    
    if (streamsError) {
      console.error('Error deleting streams:', streamsError);
      return NextResponse.json(
        { error: 'Failed to delete user streams' },
        { status: 500 }
      );
    }
    
    // Delete video archives for user's streams
    const { error: archivesError } = await supabase
      .from('video_archives')
      .delete()
      .eq('stream_id', id);
    
    if (archivesError) {
      console.error('Error deleting video archives:', archivesError);
      // Continue anyway as this might not be critical
    }
    
    // Finally, delete the user profile
    const { error: profileError } = await supabase
      .from('profiles')
      .delete()
      .eq('id', id);
    
    if (profileError) {
      console.error('Error deleting profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to delete user profile' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });
    
  } catch (error) {
    console.error('Error in user deletion API:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 