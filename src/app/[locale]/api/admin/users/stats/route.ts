import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Use shared admin authentication
    const { verifyAdminAuth } = await import('@/lib/admin-auth');
    const authResult = await verifyAdminAuth(request);
    
    if (!authResult.isValid) {
      return authResult.error!;
    }
    
    const supabase = await createClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Get total active users count
    const { count: activeUsers, error: countError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Error counting active users:', countError);
      return NextResponse.json(
        { error: 'Failed to count users' },
        { status: 500 }
      );
    }
    
    // Calculate timestamp for 24h ago
    const last24h = new Date();
    last24h.setHours(last24h.getHours() - 24);
    
    // Calculate timestamp for 7d ago
    const last7d = new Date();
    last7d.setDate(last7d.getDate() - 7);
    
    // Calculate timestamp for 30d ago
    const last30d = new Date();
    last30d.setDate(last30d.getDate() - 30);
    
    // Get new users in last 24 hours
    const { count: newUsers24h, error: new24hError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', last24h.toISOString());
    
    if (new24hError) {
      console.error('Error counting new users 24h:', new24hError);
      return NextResponse.json(
        { error: 'Failed to count new users' },
        { status: 500 }
      );
    }
    
    // Get new users in last 7 days
    const { count: newUsers7d, error: new7dError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', last7d.toISOString());
    
    if (new7dError) {
      console.error('Error counting new users 7d:', new7dError);
      return NextResponse.json(
        { error: 'Failed to count new users' },
        { status: 500 }
      );
    }
    
    // Get new users in last 30 days
    const { count: newUsers30d, error: new30dError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', last30d.toISOString());
    
    if (new30dError) {
      console.error('Error counting new users 30d:', new30dError);
      return NextResponse.json(
        { error: 'Failed to count new users' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: {
        activeUsers: activeUsers || 0,
        newUsers: {
          last24h: newUsers24h || 0,
          last7d: newUsers7d || 0,
          last30d: newUsers30d || 0
        }
      }
    });
  } catch (error) {
    console.error('Error in user stats API:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 