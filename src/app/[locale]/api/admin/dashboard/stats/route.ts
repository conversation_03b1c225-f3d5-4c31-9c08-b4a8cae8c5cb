import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Use shared admin authentication
    const { verifyAdminAuth } = await import('@/lib/admin-auth');
    const authResult = await verifyAdminAuth(request);
    
    if (!authResult.isValid) {
      return authResult.error!;
    }
    
    const supabase = await createClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Calculate timestamps for different time periods
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last30d = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // Get all stats in parallel
    const [
      { count: totalUsers },
      { count: newUsers24h },
      { count: newUsers7d },
      { count: newUsers30d },
      { count: totalCreators },
      { count: totalStreams },
      { count: liveStreams },
      { count: processingStreams },
      { count: totalNFTs },
      { count: nfts24h },
      { data: allTips },
      { data: tips24h },
      { data: tips7d },
      { data: tips30d },
      { data: recentStreams },
      { data: recentUsers },
      { data: systemSettings }
    ] = await Promise.all([
      // Total users
      supabase.from('profiles').select('*', { count: 'exact', head: true }),
      
      // New users in last 24h
      supabase.from('profiles').select('*', { count: 'exact', head: true })
        .gte('created_at', last24h.toISOString()),
      
      // New users in last 7d
      supabase.from('profiles').select('*', { count: 'exact', head: true })
        .gte('created_at', last7d.toISOString()),
      
      // New users in last 30d
      supabase.from('profiles').select('*', { count: 'exact', head: true })
        .gte('created_at', last30d.toISOString()),
      
      // Total creators
      supabase.from('profiles').select('*', { count: 'exact', head: true })
        .eq('is_creator', true),
      
      // Total streams
      supabase.from('streams').select('*', { count: 'exact', head: true }),
      
      // Live streams
      supabase.from('streams').select('*', { count: 'exact', head: true })
        .eq('status', 'live'),
      
      // Processing streams
      supabase.from('streams').select('*', { count: 'exact', head: true })
        .eq('status', 'processing'),
      
      // Total NFTs
      supabase.from('nfts').select('*', { count: 'exact', head: true }),
      
      // NFTs minted in last 24h
      supabase.from('nfts').select('*', { count: 'exact', head: true })
        .gte('created_at', last24h.toISOString()),
      
      // All tips for calculations
      supabase.from('tips').select('amount, platform_fee_amount, created_at'),
      
      // Tips in last 24h
      supabase.from('tips').select('platform_fee_amount')
        .gte('created_at', last24h.toISOString()),
      
      // Tips in last 7d
      supabase.from('tips').select('platform_fee_amount')
        .gte('created_at', last7d.toISOString()),
      
      // Tips in last 30d
      supabase.from('tips').select('platform_fee_amount')
        .gte('created_at', last30d.toISOString()),
      
      // Recent streams for activity
      supabase.from('streams').select('created_at')
        .gte('created_at', last24h.toISOString())
        .order('created_at', { ascending: false }),
      
      // Recent user activity
      supabase.from('profiles').select('updated_at')
        .gte('updated_at', last24h.toISOString())
        .order('updated_at', { ascending: false }),
      
      // System settings
      supabase.from('system_settings').select('*')
    ]);
    
    // Calculate revenue from platform fees
    const revenue24h = tips24h?.reduce((sum, tip) => sum + Number(tip.platform_fee_amount || 0), 0) || 0;
    const revenue7d = tips7d?.reduce((sum, tip) => sum + Number(tip.platform_fee_amount || 0), 0) || 0;
    const revenue30d = tips30d?.reduce((sum, tip) => sum + Number(tip.platform_fee_amount || 0), 0) || 0;
    
    // Calculate tip statistics
    const totalTipAmount = allTips?.reduce((sum, tip) => sum + Number(tip.amount || 0), 0) || 0;
    const averageTipAmount = allTips && allTips.length > 0 ? totalTipAmount / allTips.length : 0;
    
    // Calculate transaction volume (total amount of all tips)
    const transactionVolume = totalTipAmount;
    const transactionCount = allTips?.length || 0;
    
    return NextResponse.json({
      success: true,
      data: {
        activeUsers: totalUsers || 0,
        revenue: {
          last24h: revenue24h,
          last7d: revenue7d,
          last30d: revenue30d,
        },
        transactions: {
          count: transactionCount,
          volume: transactionVolume,
        },
        newUsers: {
          last24h: newUsers24h || 0,
          last7d: newUsers7d || 0,
          last30d: newUsers30d || 0,
        },
        streams: {
          total: totalStreams || 0,
          live: liveStreams || 0,
          processing: processingStreams || 0,
        },
        creators: {
          total: totalCreators || 0,
          active: totalCreators || 0, // For now, all creators are considered active
        },
        nfts: {
          total: totalNFTs || 0,
          minted24h: nfts24h || 0,
        },
        tips: {
          averageAmount: averageTipAmount,
          totalAmount: totalTipAmount,
        },
        recentStreams: recentStreams?.map(stream => ({
          createdAt: stream.created_at,
        })) || [],
        recentUsers: recentUsers?.map(user => ({
          updatedAt: user.updated_at,
        })) || [],
        systemSettings: systemSettings?.map(setting => ({
          ...setting,
        })) || [],
      }
    });
  } catch (error) {
    console.error('Error in dashboard stats API:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 