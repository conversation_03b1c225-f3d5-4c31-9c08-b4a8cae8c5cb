import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Use shared admin authentication
    const { verifyAdminAuth } = await import('@/lib/admin-auth');
    const authResult = await verifyAdminAuth(request);
    
    if (!authResult.isValid) {
      return authResult.error!;
    }
    
    // Create Supabase client
    const supabase = await createClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Get URL params
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20');
    
    // Fetch recent tips
    const { data: recentTips, error: tipsError } = await supabase
      .from('tips')
      .select(`
        id,
        amount,
        platform_fee_amount,
        created_at,
        tipper_id (id, username, wallet_address),
        recipient_id (id, username, wallet_address),
        stream_id
      `)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (tipsError) {
      console.error('Error fetching recent tips:', tipsError);
      return NextResponse.json(
        { error: 'Failed to fetch recent tips' },
        { status: 500 }
      );
    }
    
    // Fetch recent NFT mints
    const { data: recentNfts, error: nftsError } = await supabase
      .from('nfts')
      .select(`
        id,
        mint_address,
        created_at,
        owner_id (id, username, wallet_address),
        stream_id
      `)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (nftsError) {
      console.error('Error fetching recent NFTs:', nftsError);
      return NextResponse.json(
        { error: 'Failed to fetch recent NFTs' },
        { status: 500 }
      );
    }
    
    // Format the activities
    const tipActivities = recentTips?.map((tip: Record<string, unknown>) => ({
      id: `tip-${tip.id}`,
      type: 'transaction',
      message: `${(tip.tipper_id as Record<string, unknown>)?.username || String((tip.tipper_id as Record<string, unknown>)?.wallet_address).substring(0, 6) + '...'} tipped ${tip.amount} BONK`,
      timestamp: tip.created_at,
      data: tip
    })) || [];
    
    const nftActivities = recentNfts?.map((nft: Record<string, unknown>) => ({
      id: `nft-${nft.id}`,
      type: 'nft',
      message: `${(nft.owner_id as Record<string, unknown>)?.username || String((nft.owner_id as Record<string, unknown>)?.wallet_address).substring(0, 6) + '...'} minted an NFT moment`,
      timestamp: nft.created_at,
      data: nft
    })) || [];
    
    // Combine all activities and sort by timestamp
    const allActivities = [...tipActivities, ...nftActivities]
      .sort((a, b) => new Date(String(b.timestamp)).getTime() - new Date(String(a.timestamp)).getTime())
      .slice(0, limit);
    
    return NextResponse.json({
      success: true,
      data: allActivities
    });
  } catch (error) {
    console.error('Error in activity API:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 