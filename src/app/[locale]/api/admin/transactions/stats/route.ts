import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication using wallet headers
    const authHeader = request.headers.get('authorization');
    const signatureHeader = request.headers.get('x-wallet-signature');
    const messageHeader = request.headers.get('x-wallet-message');
    
    if (!authHeader || !authHeader.startsWith('Wallet ') || !signatureHeader || !messageHeader) {
      return NextResponse.json(
        { error: 'Unauthorized - Missing wallet authentication' },
        { status: 401 }
      );
    }
    
    const walletAddress = authHeader.substring(7);
    
    // Verify signature
    const { verifyWalletSignature } = await import('@/lib/auth');
    const isSignatureValid = await verifyWalletSignature(walletAddress, signatureHeader, messageHeader);
    
    if (!isSignatureValid) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid signature' },
        { status: 401 }
      );
    }
    
    // Check admin status
    const { checkAdminStatus } = await import('@/lib/auth');
    const isAdmin = await checkAdminStatus(walletAddress);
    
    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }
    
    const supabase = await createClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Get total transaction count and volume
    const { data: allTips, error: tipsError } = await supabase
      .from('tips')
      .select('amount, platform_fee_amount');
    
    if (tipsError) {
      console.error('Error fetching tips:', tipsError);
      return NextResponse.json(
        { error: 'Failed to fetch transactions' },
        { status: 500 }
      );
    }
    
    // Calculate total transaction count and volume
    const transactionCount = allTips?.length || 0;
    const transactionVolume = allTips?.reduce((sum, tip) => sum + tip.amount, 0) || 0;
    
    // Calculate timestamp for 24h ago
    const last24h = new Date();
    last24h.setHours(last24h.getHours() - 24);
    
    // Calculate timestamp for 7d ago
    const last7d = new Date();
    last7d.setDate(last7d.getDate() - 7);
    
    // Calculate timestamp for 30d ago
    const last30d = new Date();
    last30d.setDate(last30d.getDate() - 30);
    
    // Get revenue (platform fee) in last 24 hours
    const { data: tips24h, error: tips24hError } = await supabase
      .from('tips')
      .select('platform_fee_amount')
      .gte('created_at', last24h.toISOString());
    
    if (tips24hError) {
      console.error('Error fetching 24h tips:', tips24hError);
      return NextResponse.json(
        { error: 'Failed to fetch recent transactions' },
        { status: 500 }
      );
    }
    
    // Calculate revenue for last 24 hours
    const revenue24h = tips24h?.reduce((sum, tip) => sum + tip.platform_fee_amount, 0) || 0;
    
    // Get revenue (platform fee) in last 7 days
    const { data: tips7d, error: tips7dError } = await supabase
      .from('tips')
      .select('platform_fee_amount')
      .gte('created_at', last7d.toISOString());
    
    if (tips7dError) {
      console.error('Error fetching 7d tips:', tips7dError);
      return NextResponse.json(
        { error: 'Failed to fetch recent transactions' },
        { status: 500 }
      );
    }
    
    // Calculate revenue for last 7 days
    const revenue7d = tips7d?.reduce((sum, tip) => sum + tip.platform_fee_amount, 0) || 0;
    
    // Get revenue (platform fee) in last 30 days
    const { data: tips30d, error: tips30dError } = await supabase
      .from('tips')
      .select('platform_fee_amount')
      .gte('created_at', last30d.toISOString());
    
    if (tips30dError) {
      console.error('Error fetching 30d tips:', tips30dError);
      return NextResponse.json(
        { error: 'Failed to fetch recent transactions' },
        { status: 500 }
      );
    }
    
    // Calculate revenue for last 30 days
    const revenue30d = tips30d?.reduce((sum, tip) => sum + tip.platform_fee_amount, 0) || 0;
    
    return NextResponse.json({
      success: true,
      data: {
        transactions: {
          count: transactionCount,
          volume: transactionVolume
        },
        revenue: {
          last24h: revenue24h,
          last7d: revenue7d,
          last30d: revenue30d
        }
      }
    });
  } catch (error) {
    console.error('Error in transaction stats API:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 