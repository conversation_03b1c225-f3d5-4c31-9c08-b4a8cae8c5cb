import { NextRequest, NextResponse } from 'next/server';
import { getSrc } from '@livepeer/react/external';
import { Livepeer } from 'livepeer';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const playbackId = searchParams.get('playbackId');
    
    if (!playbackId) {
      return NextResponse.json(
        { error: 'Playback ID is required' },
        { status: 400 }
      );
    }
    
    // Initialize Livepeer client
    const livepeerApiKey = process.env.LIVEPEER_API_KEY;
    
    if (!livepeerApiKey) {
      return NextResponse.json(
        { error: 'Livepeer API key is not configured' },
        { status: 500 }
      );
    }
    
    const livepeer = new Livepeer({ apiKey: livepeerApiKey });
    
    // Get playback info from Livepeer
    try {
      const playbackInfo = await livepeer.playback.get(playbackId);
      
      if (!playbackInfo || !playbackInfo.playbackInfo) {
        throw new Error('Failed to get playback information');
      }
      
      // Convert to src format for the Livepeer player
      const src = getSrc(playbackInfo.playbackInfo);
      
      
      // Provide a fallback if src is empty
      if (!src || src.length === 0) {
        // Create a fallback HLS source
        const fallbackSrc = [{
          type: 'hls',
          mime: 'application/vnd.apple.mpegurl',
          url: `https://livepeercdn.studio/hls/${playbackId}/index.m3u8`
        }];
        
        return NextResponse.json({
          src: fallbackSrc,
          meta: {
            type: 'video',
            live: true,
            source: 'fallback-direct-hls'
          }
        });
      }
      
      // Check if the playback is for a live stream
      const playbackMeta = playbackInfo.playbackInfo as { meta?: { live?: boolean } };
      const isLive = playbackInfo.playbackInfo.type === 'live' || 
                    playbackMeta.meta?.live === true;
      
      return NextResponse.json({ 
        src,
        meta: {
          type: 'video',
          live: isLive,
          source: 'livepeer-api'
        }
      });
    } catch (livepeerError) {
      console.error('Error fetching playback info:', livepeerError);
      
      // Check if the stream exists before providing fallback
      try {
        // Try a direct HEAD request to the HLS URL to see if it exists
        const response = await fetch(
          `https://livepeercdn.studio/hls/${playbackId}/index.m3u8`,
          { method: 'HEAD' }
        );
        
        if (response.ok) {
          // Stream exists, provide the direct URL
          return NextResponse.json({
            src: [{
              type: 'hls',
              mime: 'application/vnd.apple.mpegurl',
              url: `https://livepeercdn.studio/hls/${playbackId}/index.m3u8`
            }],
            meta: {
              type: 'video',
              live: true,
              source: 'fallback-hls'
            }
          });
        } else {
          throw new Error('Stream not available');
        }
      } catch {
        // Both API and direct check failed, return a clear error
        return NextResponse.json(
          { error: 'Stream not available', details: 'The requested stream could not be found' },
          { status: 404 }
        );
      }
    }
  } catch (error) {
    console.error('Error handling stream playback request:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}
