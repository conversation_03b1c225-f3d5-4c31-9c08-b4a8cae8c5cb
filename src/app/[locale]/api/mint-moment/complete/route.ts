import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createServiceClient } from '@/lib/supabase/server';
import { createNftMintTransaction, uploadMetadataToIPFS } from '@/lib/nft';

// Validate request data for completing moment capture after BONK payment
const completeMomentCaptureSchema = z.object({
  streamId: z.string().uuid(),
  timestamp: z.number().min(0),
  imageUrl: z.string().min(1), // Allow data URLs and regular URLs
  creatorWalletAddress: z.string().min(32).max(44),
  userWalletAddress: z.string().min(32).max(44),
  bonkTransactionSignature: z.string().min(32),
  nftTransactionSignature: z.string().min(32).optional(), // Optional for backward compatibility
  metadataUri: z.string().optional(), // IPFS URI if NFT was minted
  mintAddress: z.string().optional(), // The actual NFT mint address
  metadata: z.object({
    name: z.string(),
    description: z.string(),
    image: z.string(),
    attributes: z.array(z.object({
      trait_type: z.string(),
      value: z.union([z.string(), z.number()])
    }))
  }),
  mintCostBonk: z.number().positive(),
  platformFeePercentage: z.number(),
  creatorAmountBonk: z.number(),
  platformFeeAmountBonk: z.number()
});

export async function POST(request: NextRequest) {

  try {
    const data = await request.json();

    // Validate request data
    try {
      completeMomentCaptureSchema.parse(data);
    } catch (error) {
      console.error('Complete validation error:', error);
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error instanceof Error ? error.message : 'Validation failed'
        },
        { status: 400 }
      );
    }

    const { 
      streamId, 
      timestamp, 
      metadata, 
      creatorWalletAddress, 
      userWalletAddress,
      bonkTransactionSignature,
      nftTransactionSignature,
      metadataUri: providedMetadataUri,
      mintAddress,
      mintCostBonk,
      platformFeePercentage,
      creatorAmountBonk,
      platformFeeAmountBonk
    } = data;
    
    // Get supabase service client (bypasses RLS)
    let supabase;
    try {

      supabase = createServiceClient();
    } catch (error) {
      return NextResponse.json(
        { error: 'Database connection failed', details: (error as Error).message },
        { status: 500 }
      );
    }
    
    // SECURITY: Rate limiting - check for recent NFT mints from this wallet
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const { count: recentMints } = await supabase
      .from('nfts')
      .select('*', { count: 'exact', head: true })
      .eq('owner_wallet_address', userWalletAddress)
      .gte('created_at', oneMinuteAgo);

    if (recentMints && recentMints >= 3) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before minting another NFT.' },
        { status: 429 }
      );
    }

    // SECURITY: Check for duplicate transaction signatures
    const { data: existingNft } = await supabase
      .from('nfts')
      .select('id')
      .eq('bonk_transaction_signature', bonkTransactionSignature)
      .single();

    if (existingNft) {
      return NextResponse.json(
        { error: 'Transaction signature already used' },
        { status: 409 }
      );
    }

    // Get user ID from wallet address
    let userId: string;
    try {
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', userWalletAddress)
        .single();

      if (userError || !userData) {
        // Try to create user profile if it doesn't exist
        const { data: newUserData, error: createError } = await supabase
          .from('profiles')
          .insert({
            wallet_address: userWalletAddress,
            is_creator: false,
            is_admin: false
          })
          .select('id')
          .single();
        
        if (createError || !newUserData) {
          console.error('Failed to create user profile:', createError);
          return NextResponse.json(
            { error: 'Failed to create user profile', details: createError?.message || 'Unknown error' },
            { status: 500 }
          );
        }
        
        userId = newUserData.id;
      } else {
        userId = userData.id;
      }
    } catch (error) {
      console.error('Error during user lookup/creation:', error);
      return NextResponse.json(
        { error: 'User lookup failed', details: (error as Error).message },
        { status: 500 }
      );
    }
    
    // Get creator ID from wallet address
    let creatorId: string;
    try {
      const { data: creatorData, error: creatorError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', creatorWalletAddress)
        .single();
      
      if (creatorError || !creatorData) {
        console.error('Creator not found:', creatorError);
        return NextResponse.json(
          { error: 'Creator not found', details: creatorError?.message || 'Creator profile does not exist' },
          { status: 404 }
        );
      }
      
      creatorId = creatorData.id;
    } catch (error) {
      return NextResponse.json(
        { error: 'Creator lookup failed', details: (error as Error).message },
        { status: 500 }
      );
    }
    
    // Record moment capture in database (using the updated nfts table structure)
    const momentRecord = {
      mint_address: mintAddress || `bonk-moment-${bonkTransactionSignature.substring(0, 8)}`, // Use real mint address or fallback
      owner_id: userId,
      minter_id: userId,
      creator_id: creatorId,
      stream_id: streamId,
      metadata_uri: providedMetadataUri || JSON.stringify(metadata), // Use provided IPFS URI or fallback to JSON
      timestamp: timestamp,
      platform_fee_at_mint: platformFeePercentage,
      cost_bonk: mintCostBonk,
      creator_amount_bonk: creatorAmountBonk,
      platform_fee_amount_bonk: platformFeeAmountBonk,
      bonk_transaction_signature: bonkTransactionSignature,
      nft_transaction_signature: nftTransactionSignature, // Store NFT transaction signature if available
      tx_signature: bonkTransactionSignature, // For compatibility with old schema
    };
    
    // Record moment capture in database
    let momentData;
    try {
      const { data: initialData, error: momentError } = await supabase
        .from('nfts')
        .insert(momentRecord)
        .select()
        .single();
      
      if (momentError) {
        console.error('Failed to record moment capture:', momentError);
        
        // Fallback: try with minimal record if new columns don't exist
        if (momentError.code === '42703') { // Column doesn't exist
          const fallbackRecord = {
            mint_address: 'bonk-moment-' + bonkTransactionSignature.substring(0, 8),
            owner_id: userId,
            stream_id: streamId,
            metadata_uri: JSON.stringify(metadata),
            timestamp: timestamp,
            platform_fee_at_mint: platformFeePercentage,
            tx_signature: bonkTransactionSignature,
          };
          
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('nfts')
            .insert(fallbackRecord)
            .select()
            .single();
            
          if (fallbackError) {
            console.error('Fallback insert also failed:', fallbackError);
            return NextResponse.json(
              { error: 'Failed to record moment capture', details: fallbackError.message },
              { status: 500 }
            );
          }
          
          momentData = fallbackData;
        } else {
          return NextResponse.json(
            { error: 'Failed to record moment capture', details: momentError.message },
            { status: 500 }
          );
        }
      } else {
        momentData = initialData;
      }
    } catch (error) {
      console.error('Error during database insert:', error);
      return NextResponse.json(
        { error: 'Database insert failed', details: (error as Error).message },
        { status: 500 }
      );
    }
    
    // Step 2: Handle NFT minting based on whether we have a client-signed NFT transaction
    let metadataUri = providedMetadataUri; // Use provided metadata URI if available

    
    if (nftTransactionSignature) {
      // Client already signed and sent NFT transaction - skip server-side minting
      
      // Use the provided metadata URI or generate one if not provided
      if (!metadataUri) {
        try {
          metadataUri = await uploadMetadataToIPFS(metadata);
        } catch (metadataError) {
          console.warn('Could not generate metadata URI, using fallback:', metadataError);
          metadataUri = JSON.stringify(metadata);
        }
      }
    } else {
      // Legacy flow: Server-side NFT minting (fallback for older clients)
      metadataUri = null;
    
    try {      
      // Upload metadata to IPFS first
      metadataUri = await uploadMetadataToIPFS(metadata);
      
      // Create NFT mint transaction
      await createNftMintTransaction(
        metadata,
        userWalletAddress
      );
            
      // Since this is a server operation, we need to handle the NFT minting
      // For now, we'll store the transaction for manual processing
      // In production, you'd want to use a service wallet to mint the NFT
      
      // Update the database record with metadata URI
      const { error: updateError } = await supabase
        .from('nfts')
        .update({ 
          metadata_uri: metadataUri,
          mint_address: 'pending-nft-mint' // Mark as pending actual mint
        })
        .eq('id', momentData.id);
        
      if (updateError) {
        console.error('Failed to update NFT record with metadata:', updateError);
      } else {
      }
      
    } catch (nftError) {
      console.error('❌ NFT minting failed:', nftError);
      
      // Update database to reflect NFT minting failure
      await supabase
        .from('nfts')
        .update({ 
          mint_address: 'nft-mint-failed',
          metadata_uri: JSON.stringify(metadata) // Store as JSON fallback
        })
        .eq('id', momentData.id);
    }
    } // Close the else block for legacy flow
    
    // Generate explorer link
    const isMainnet = process.env.NEXT_PUBLIC_SOLANA_NETWORK !== 'devnet';
    const bonkExplorerUrl = isMainnet
      ? `https://explorer.solana.com/tx/${bonkTransactionSignature}`
      : `https://explorer.solana.com/tx/${bonkTransactionSignature}?cluster=devnet`;
        
    return NextResponse.json({
      success: true,
      nft: {
        id: momentData.id,
        mintAddress: mintAddress || null, // Real mint address if NFT was created
        metadataUri: metadataUri || JSON.stringify(metadata),
        bonkTransactionSignature,
        nftTransactionSignature: nftTransactionSignature || null,
        bonkExplorerUrl
      },
      message: mintAddress 
        ? `Real NFT minted! 🎉 Your video moment is now a collectible NFT in your wallet. Mint address: ${mintAddress.substring(0, 8)}... View payment: ${bonkExplorerUrl}`
        : metadataUri 
          ? `Moment captured! 🎉 BONK payment sent and NFT metadata uploaded to IPFS. View payment: ${bonkExplorerUrl}`
          : `Moment captured! 🎉 BONK payment sent (NFT minting failed). View payment: ${bonkExplorerUrl}`
    });
    
  } catch (error) {
    console.error('Error recording moment capture:', error);
    
    // Provide more specific error information
    let errorMessage = 'Failed to record moment capture';
    const errorDetails = (error as Error).message;
    
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        errorMessage = 'Database connection error';
      } else if (error.message.includes('validation')) {
        errorMessage = 'Data validation error';
      } else if (error.message.includes('auth')) {
        errorMessage = 'Authentication error';
      }
    }
    
    return NextResponse.json(
      { error: errorMessage, details: errorDetails },
      { status: 500 }
    );
  }
}
