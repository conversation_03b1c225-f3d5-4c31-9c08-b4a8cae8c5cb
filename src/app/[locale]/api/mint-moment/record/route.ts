import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

const recordNftSchema = z.object({
  streamId: z.string().uuid(),
  userWalletAddress: z.string().min(32).max(44),
  signature: z.string(),
  timestamp: z.number().nonnegative(),
  imageUrl: z.string().url(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { streamId, userWalletAddress, signature, timestamp, imageUrl } = recordNftSchema.parse(body);

    const supabase = await createClient();

    // Record the NFT mint in the database
    const { data: nft, error } = await supabase
      .from('nfts')
      .insert({
        stream_id: streamId,
        owner_wallet_address: userWalletAddress,
        transaction_signature: signature,
        timestamp: timestamp,
        image_url: imageUrl,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { error: 'Failed to record NFT in database' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      nft: nft,
    });

  } catch (error) {
    console.error('Record NFT error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to record NFT' },
      { status: 500 }
    );
  }
}
