import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createNFTMetadata, createSimpleCollectibleNft } from '@/lib/nft';
import { createClient } from '@/lib/supabase/server';
import { createBonkNftMintTransaction } from '@/lib/bonk';

// BONK cost for NFT minting (2 BONK for testing)
const NFT_MINT_COST_BONK = 2;

// Validate request data with security checks
const createNftRequestSchema = z.object({
  streamId: z.string().uuid(),
  timestamp: z.number().min(0).max(86400), // Max 24 hours
  imageUrl: z.string().min(1).max(200000), // Limit image URL size
  creatorWalletAddress: z.string().min(32).max(44),
  creatorName: z.string().optional(),
  userWalletAddress: z.string().min(32).max(44),
  // Wallet signature for security
  walletSignature: z.string().min(32),
  walletMessage: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate request data
    try {
      createNftRequestSchema.parse(data);
    } catch (error) {
      console.error('Validation error:', error);
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: error instanceof Error ? error.message : 'Validation failed'
        },
        { status: 400 }
      );
    }
    
    const { 
      streamId, 
      timestamp, 
      imageUrl, 
      creatorWalletAddress, 
      creatorName, 
      userWalletAddress,
      walletSignature,
      walletMessage 
    } = data;
    
    // Verify wallet signature for security
    if (!walletSignature || !walletMessage) {
      return NextResponse.json(
        { error: 'Wallet signature required for security' },
        { status: 401 }
      );
    }
    
    // Basic signature validation (timestamp check to prevent replay attacks)
    const messageTimestamp = walletMessage.match(/(\d{13})$/);
    if (!messageTimestamp) {
      return NextResponse.json(
        { error: 'Invalid message format' },
        { status: 401 }
      );
    }
    
    const messageTime = parseInt(messageTimestamp[1]);
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;
    
    if (now - messageTime > fiveMinutes) {
      return NextResponse.json(
        { error: 'Message expired. Please try again.' },
        { status: 401 }
      );
    }
    
    
    // Get stream details from database
    const supabase = await createClient();
    const { data: stream, error: streamError } = await supabase
      .from('streams')
      .select(`
        title,
        profiles!creator_id (
          username
        )
      `)
      .eq('id', streamId)
      .single();
      
    if (streamError || !stream) {
      return NextResponse.json(
        { error: 'Stream not found' },
        { status: 404 }
      );
    }
    
    // Validate image URL to prevent manipulation
    if (imageUrl.startsWith('data:')) {
      // Allow data URLs but limit size
      if (imageUrl.length > 200000) { // ~150KB base64 limit
        return NextResponse.json(
          { error: 'Image too large' },
          { status: 400 }
        );
      }
    } else if (!imageUrl.startsWith('https://')) {
      return NextResponse.json(
        { error: 'Invalid image URL' },
        { status: 400 }
      );
    }
    
    // Create NFT metadata
    const metadata = createNFTMetadata(
      stream.title,
      timestamp,
      imageUrl,
      creatorName || (stream.profiles as { username?: string })?.username || 'Unknown Creator',
      streamId
    );

    try {
      
      // Step 1: Create BONK payment transaction
      const bonkTransactionResult = await createBonkNftMintTransaction(
        userWalletAddress,
        creatorWalletAddress,
        NFT_MINT_COST_BONK
      );
      
      // Step 2: Always use client-side NFT creation (bypasses transaction size limits)
      const nftMintResult = await createSimpleCollectibleNft(
        metadata,
        userWalletAddress
      );
      
      // Serialize BONK transaction
      const serializedBonkTransaction = bonkTransactionResult.transaction.serialize({ 
        requireAllSignatures: false 
      });
      
      return NextResponse.json({
        bonkTransaction: Buffer.from(serializedBonkTransaction).toString('base64'),
        nftInstructions: nftMintResult.instructions,
        metadata,
        metadataUri: nftMintResult.metadataUri,
        mintCostBonk: NFT_MINT_COST_BONK,
        platformFeePercentage: bonkTransactionResult.platformFeePercentage,
        creatorAmountBonk: bonkTransactionResult.creatorAmount,
        platformFeeAmountBonk: bonkTransactionResult.platformFeeAmount,
        clientSideNft: true,
        requiresTwoTransactions: false, // BONK payment + client-side NFT
        securityVerified: true,
        message: 'BONK payment ready, NFT will be created client-side in your wallet'
      });
    } catch (error) {
      console.error('Error creating transactions:', error);
      return NextResponse.json(
        { error: 'Failed to create transactions', details: (error as Error).message },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}