import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';

// Validate creator registration data
const creatorRegistrationSchema = z.object({
  username: z.string().min(3).max(50),
  displayName: z.string().min(2).max(100),
  bio: z.string().min(20).max(500),
  avatarUrl: z.string().url().optional().or(z.string().length(0)),
  coverImageUrl: z.string().url().optional().or(z.string().length(0)),
  wallet_address: z.string().min(32).max(44),
  socialLinks: z.object({
    twitter: z.string().optional(),
    discord: z.string().optional(),
    instagram: z.string().optional(),
    website: z.string().optional(),
  }).optional(),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms to continue',
  }),
  acceptFeesDisclosure: z.boolean().refine(val => val === true, {
    message: 'You must accept the fee disclosure to continue',
  }),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validation = creatorRegistrationSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid creator data', details: validation.error.errors },
        { status: 400 }
      );
    }
    
    const { 
      username, 
      displayName, 
      bio, 
      avatarUrl, 
      coverImageUrl,
      wallet_address,
      socialLinks,
    } = validation.data;
    
    // Get supabase client
    const supabase = await createClient();
    
    // Check if username is already taken
    const { data: existingUsername } = await supabase
      .from('profiles')
      .select('id')
      .eq('username', username)
      .maybeSingle();
    
    if (existingUsername) {
      return NextResponse.json(
        { error: 'Username is already taken' },
        { status: 409 }
      );
    }
    
    // Get user profile by wallet address
    const { data: existingProfile, error: profileError } = await supabase
      .from('profiles')
      .select('id, is_creator')
      .eq('wallet_address', wallet_address)
      .maybeSingle();
    
    if (profileError && profileError.code !== 'PGRST116') {
      return NextResponse.json(
        { error: 'Failed to check profile', details: profileError.message },
        { status: 500 }
      );
    }
    
    // If already a creator, return error
    if (existingProfile?.is_creator) {
      return NextResponse.json(
        { error: 'This wallet is already registered as a creator' },
        { status: 409 }
      );
    }
    
    // If profile exists, update it to make them a creator
    if (existingProfile) {
      const { data: updatedProfile, error: updateError } = await supabase
        .from('profiles')
        .update({
          username,
          display_name: displayName,
          bio,
          avatar_url: avatarUrl || null,
          cover_image_url: coverImageUrl || null,
          is_creator: true,
          social_links: socialLinks || {},
        })
        .eq('id', existingProfile.id)
        .select()
        .single();
      
      if (updateError) {
        return NextResponse.json(
          { error: 'Failed to update profile', details: updateError.message },
          { status: 500 }
        );
      }
      
      // Create a creator record with default settings
      const { error: creatorError } = await supabase
        .from('creator_settings')
        .insert({
          profile_id: existingProfile.id,
          tip_message: `Thanks for supporting me with BONK!`,
          about_me: bio,
        });
      
      if (creatorError) {
        console.error('Error creating creator settings:', creatorError);
      }
      
      // Revalidate profile page
      revalidatePath('/profile');
      revalidatePath('/creator/dashboard');
      
      return NextResponse.json({ 
        success: true, 
        message: 'Successfully registered as creator',
        profile: updatedProfile 
      });
    } else {
      // Create a new profile with creator status
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert({
          wallet_address,
          username,
          display_name: displayName,
          bio,
          avatar_url: avatarUrl || null,
          cover_image_url: coverImageUrl || null,
          is_creator: true,
          social_links: socialLinks || {},
        })
        .select()
        .single();
      
      if (createError) {
        return NextResponse.json(
          { error: 'Failed to create profile', details: createError.message },
          { status: 500 }
        );
      }
      
      // Create a creator record with default settings
      const { error: creatorError } = await supabase
        .from('creator_settings')
        .insert({
          profile_id: newProfile.id,
          tip_message: `Thanks for supporting me with BONK!`,
          about_me: bio,
        });
      
      if (creatorError) {
        console.error('Error creating creator settings:', creatorError);
      }
      
      // Revalidate profile page
      revalidatePath('/profile');
      revalidatePath('/creator/dashboard');
      
      return NextResponse.json({ 
        success: true, 
        message: 'Successfully registered as creator',
        profile: newProfile 
      });
    }
  } catch (error) {
    console.error('Error registering creator:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 