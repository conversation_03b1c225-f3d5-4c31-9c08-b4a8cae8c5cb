import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { livepeerProvider } from '@/lib/video-providers/livepeer';

// Helper function to handle common errors
function handleError(error: unknown, message: string, status = 500) {
  console.error(`${message}:`, error);
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  return NextResponse.json(
    { error: message, details: errorMessage },
    { status }
  );
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ username: string; locale: string }> }
) {
  try {
    const { username } = await params;

    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    if (!supabase) {
      return handleError(new Error("Database connection failed"), 'Failed to create database client');
    }

    // 1. Fetch public profile by username
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, username, avatar_url, bio, wallet_address, created_at')
      .eq('username', username)
      .eq('is_creator', true) // Ensure the user is actually a creator
      .single();

    if (profileError) {
      if (profileError.code === 'PGRST116') { // PGRST116: "single() on a query with 0 rows"
        return NextResponse.json(
          { error: 'Creator not found' },
          { status: 404 }
        );
      }
      return handleError(profileError, 'Failed to fetch creator profile');
    }

    if (!profile) {
        // Should be caught by PGRST116, but as a safeguard
        return NextResponse.json(
            { error: 'Creator not found' }, 
            { status: 404 }
        );
    }

    // 2. Fetch public streams for the creator
    const { data: streams, error: streamsError } = await supabase
      .from('streams')
      .select('id, title, description, thumbnail_url, view_count, tip_count, nft_count, like_count, duration, status, is_active, created_at, ended_at, livepeer_stream_id')
      .eq('creator_id', profile.id)
      .order('created_at', { ascending: false });

    // 2.1. Calculate actual tip counts for each stream
    const streamsWithActualTipCounts = await Promise.all(
      (streams || []).map(async (stream) => {
        const { count: actualTipCount, error: tipCountError } = await supabase
          .from('tips')
          .select('*', { count: 'exact', head: true })
          .eq('stream_id', stream.id);

        if (tipCountError) {
          console.warn(`Failed to get tip count for stream ${stream.id}:`, tipCountError);
        }

        // Calculate actual NFT count as well
        const { count: actualNftCount, error: nftCountError } = await supabase
          .from('nfts')
          .select('*', { count: 'exact', head: true })
          .eq('stream_id', stream.id);

        if (nftCountError) {
          console.warn(`Failed to get NFT count for stream ${stream.id}:`, nftCountError);
        }

        // Determine if stream is actually live by checking with Livepeer
        let actuallyLive = false;
        if (stream.status === 'live' && stream.livepeer_stream_id && !stream.ended_at) {
          try {
            actuallyLive = await livepeerProvider.isStreamLive(stream.livepeer_stream_id);
          } catch (liveCheckError) {
            console.warn(`Failed to check live status for stream ${stream.id}:`, liveCheckError);
            actuallyLive = false; // Default to not live if check fails
          }
        }

        return {
          ...stream,
          tip_count: actualTipCount || 0,
          nft_count: actualNftCount || 0,
          is_actually_live: actuallyLive,
          is_active: actuallyLive // Keep this for backward compatibility
        };
      })
    );

    if (streamsError) {
      return handleError(streamsError, 'Failed to fetch creator streams');
    }

    // 3. Calculate creator statistics
    const totalStreams = streamsWithActualTipCounts?.length || 0;
    const totalViews = streamsWithActualTipCounts?.reduce((sum, stream) => sum + (stream.view_count || 0), 0) || 0;
    const totalLikes = streamsWithActualTipCounts?.reduce((sum, stream) => sum + (stream.like_count || 0), 0) || 0;
    const avgStreamDuration = totalStreams > 0 
      ? Math.round(streamsWithActualTipCounts.reduce((sum, stream) => sum + (stream.duration || 0), 0) / totalStreams)
      : 0;

    // 4. Fetch tip statistics
    const { data: tipStats, error: tipStatsError } = await supabase
      .from('tips')
      .select('amount, creator_amount, tipper_name, message, created_at, stream_id, tx_signature')
      .eq('recipient_id', profile.id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (tipStatsError) {
      console.warn('Failed to fetch tip stats:', tipStatsError);
    }

    const totalTips = tipStats?.length || 0;
    // Keep amounts in lamports - let frontend handle conversion consistently
    const totalBonkEarned = tipStats?.reduce((sum, tip) => sum + (tip.creator_amount || 0), 0) || 0;

    // Get stream titles for recent tips
    const recentTipsWithStreams = await Promise.all(
      (tipStats || []).slice(0, 5).map(async (tip) => {
        const { data: stream } = await supabase
          .from('streams')
          .select('title')
          .eq('id', tip.stream_id)
          .single();
        
        return {
          id: crypto.randomUUID(),
          amount: tip.amount || 0, // Keep in lamports
          creator_amount: tip.creator_amount || 0, // Keep in lamports
          tipper_name: tip.tipper_name,
          message: tip.message,
          created_at: tip.created_at,
          stream_title: stream?.title || 'Unknown Stream',
          tx_signature: tip.tx_signature
        };
      })
    );

    // 5. Fetch NFT statistics
    // Get all stream IDs for the creator
    const streamIds = streamsWithActualTipCounts.map(stream => stream.id);

    // Count the total number of NFTs for the creator's streams
    const { count: totalNfts, error: nftCountError } = await supabase
      .from('nfts')
      .select('*', { count: 'exact', head: true })
      .in('stream_id', streamIds);

    if (nftCountError) {
      console.warn('Failed to fetch NFT count:', nftCountError);
    }

    // Fetch recent NFT mints for the creator's streams
    const { data: nftStats, error: nftStatsError } = await supabase
      .from('nfts')
      .select('mint_address, timestamp, created_at, stream_id')
      .in('stream_id', streamIds)
      .order('created_at', { ascending: false })
      .limit(6);

    if (nftStatsError) {
      console.warn('Failed to fetch NFT stats:', nftStatsError);
    }

    // Get stream titles for recent NFT mints
    const recentNftsWithStreams = await Promise.all(
      (nftStats || []).slice(0, 4).map(async (nft) => {
        const { data: stream } = await supabase
          .from('streams')
          .select('title')
          .eq('id', nft.stream_id)
          .single();
        
        return {
          id: crypto.randomUUID(),
          mint_address: nft.mint_address,
          timestamp: nft.timestamp,
          created_at: nft.created_at,
          stream_title: stream?.title || 'Unknown Stream'
        };
      })
    );

    // 6. Compile comprehensive response
    const responseData = {
      profile,
      streams: streamsWithActualTipCounts || [],
      stats: {
        total_streams: totalStreams,
        total_views: totalViews,
        total_likes: totalLikes,
        total_tips_received: totalTips,
        total_bonk_earned: totalBonkEarned,
        total_nfts_minted: totalNfts,
        avg_stream_duration: avgStreamDuration,
        follower_count: 0 // Placeholder for future feature
      },
      tips: {
        total_tips: totalTips,
        total_amount: totalBonkEarned,
        recent_tips: recentTipsWithStreams
      },
      nfts: {
        total_nfts: totalNfts,
        recent_mints: recentNftsWithStreams
      }
    };

    return NextResponse.json(responseData);

  } catch (error) {
    return handleError(error, 'Unhandled error in fetching creator public data');
  }
} 