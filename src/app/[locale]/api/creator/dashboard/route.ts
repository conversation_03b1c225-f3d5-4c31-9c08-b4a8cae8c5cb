import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { livepeerProvider } from '@/lib/video-providers/livepeer';

export async function GET(request: NextRequest) {
  try {
    // Get wallet address from query parameter (same as profile API)
    const searchParams = request.nextUrl.searchParams;
    const walletAddress = searchParams.get('wallet');
    
    if (!walletAddress) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createClient();
    
    // Get user profile by wallet address
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', walletAddress)
      .single();

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Check if user is a creator
    if (!profile.is_creator) {
      return NextResponse.json(
        { error: 'You are not registered as a creator' },
        { status: 403 }
      );
    }

    // Get time filtering parameter
    const timeRange = searchParams.get('timeRange') || 'week'; // day, week, month, year, all
    
    // Calculate date range based on timeRange
    const now = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case 'day':
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(now.getDate() - 7); // Default to week
    }
    
    const startDateStr = startDate.toISOString();
    
    // Get earnings summary for the selected time period
    const { data: tipsData, error: tipsError } = await supabase
      .from('tips')
      .select('amount, creator_amount, platform_fee_amount, platform_fee_percentage')
      .eq('recipient_id', profile.id)
      .gte('created_at', startDateStr);
      
    if (tipsError) {
      throw tipsError;
    }
    
    // Calculate earnings for previous period (for percentage change)
    const prevStartDate = new Date(startDate);
    let prevEndDate = new Date(now);
    
    switch (timeRange) {
      case 'day':
        prevStartDate.setDate(prevStartDate.getDate() - 1);
        prevEndDate = new Date(startDate);
        break;
      case 'week':
        prevStartDate.setDate(prevStartDate.getDate() - 7);
        prevEndDate = new Date(startDate);
        break;
      case 'month':
        prevStartDate.setMonth(prevStartDate.getMonth() - 1);
        prevEndDate = new Date(startDate);
        break;
      case 'year':
        prevStartDate.setFullYear(prevStartDate.getFullYear() - 1);
        prevEndDate = new Date(startDate);
        break;
      case 'all':
        // No previous period for 'all' time range
        break;
    }
    
    let percentChange = 0;
    
    if (timeRange !== 'all') {
      const { data: prevTipsData, error: prevTipsError } = await supabase
        .from('tips')
        .select('amount')
        .eq('recipient_id', profile.id)
        .gte('created_at', prevStartDate.toISOString())
        .lt('created_at', prevEndDate.toISOString());
        
      if (!prevTipsError && prevTipsData) {
        const currentSum = tipsData.reduce((sum: number, tip: Record<string, unknown>) => sum + Number(tip.amount || 0), 0);
        const prevSum = prevTipsData.reduce((sum: number, tip: Record<string, unknown>) => sum + Number(tip.amount || 0), 0);
        
        if (prevSum > 0) {
          percentChange = Math.round(((currentSum - prevSum) / prevSum) * 100);
        } else if (currentSum > 0) {
          percentChange = 100; // If prev period had 0, but current has something, that's a 100% increase
        }
      }
    }
    
    // Calculate totals (keep in lamports - let frontend handle conversion)
    const totalAmount = tipsData.reduce((sum: number, tip: Record<string, unknown>) => sum + Number(tip.amount || 0), 0);
    const creatorAmount = tipsData.reduce((sum: number, tip: Record<string, unknown>) => sum + Number(tip.creator_amount || 0), 0);
    
    // Get current platform fee percentage
    let feePercentage = 5; // Default value
    const { data: feeData, error: feeError } = await supabase
      .from('platform_fees')
      .select('percentage')
      .order('effective_from', { ascending: false })
      .limit(1);
      
    if (!feeError && feeData && feeData.length > 0) {
      feePercentage = feeData[0].percentage;
    }
    
    // Get tips with additional info
    let tipsQuery = supabase
      .from('tips')
      .select(`
        *,
        stream:stream_id(*),
        tipper:tipper_id(id, username, wallet_address, avatar_url)
      `)
      .eq('recipient_id', profile.id);
      
    if (timeRange !== 'all') {
      tipsQuery = tipsQuery.gte('created_at', startDateStr);
    }
    
    const { data: tips, error: tipsDetailError } = await tipsQuery
      .order('created_at', { ascending: false })
      .limit(50); // Limit to avoid very large response
      
    if (tipsDetailError) {
      throw tipsDetailError;
    }
    
    // Return raw lamport amounts - let frontend handle conversion consistently
    const processedTips = (tips || []).map(tip => ({
      ...tip,
      amount: Number(tip.amount || 0),
      creator_amount: Number(tip.creator_amount || 0),
      platform_fee_amount: Number(tip.platform_fee_amount || 0),
      tipper_username: tip.tipper?.username || null
    }));
    
    // Get streams with analytics
    const { data: streams, error: streamsError } = await supabase
      .from('streams')
      .select(`
        *,
        tips:tips(amount, created_at)
      `)
      .eq('creator_id', profile.id)
      .order('created_at', { ascending: false });
      
    if (streamsError) {
      throw streamsError;
    }
    
    // Check live status for streams marked as live
    const liveStreamIds = streams
      .filter(stream => stream.status === 'live' && stream.livepeer_stream_id)
      .map(stream => stream.livepeer_stream_id);
      
    const liveStatusMap: Record<string, boolean> = {};
    
    if (liveStreamIds.length > 0) {
      try {
        const liveStatusChecks = await Promise.allSettled(
          liveStreamIds.map(async (streamId) => {
            try {
              const isLive = await livepeerProvider.isStreamLive(streamId);
              return { streamId, isLive };
            } catch (error) {
              console.error(`Error checking live status for stream ${streamId}:`, error);
              return { streamId, isLive: false };
            }
          })
        );
        
        liveStatusChecks.forEach((result) => {
          if (result.status === 'fulfilled') {
            const { streamId, isLive } = result.value;
            liveStatusMap[streamId] = isLive;
          }
        });
      } catch (liveCheckError) {
        console.error('Error checking live status:', liveCheckError);
      }
    }

    // Process stream data to add analytics and live status
    const processedStreams = streams.map(stream => {
      const filteredTips = (stream.tips as Record<string, unknown>[]).filter((tip: Record<string, unknown>) => 
        timeRange === 'all' || new Date(String(tip.created_at)) >= startDate
      );
      
      // Determine if stream is actually live
      const isActuallyLive = stream.status === 'live' && 
                           stream.livepeer_stream_id && 
                           liveStatusMap[stream.livepeer_stream_id];
      
      return {
        ...stream,
        views: stream.view_count || 0, // Map view_count to views for component compatibility
        tip_count: filteredTips.length,
        total_tips: filteredTips.reduce((sum: number, tip: Record<string, unknown>) => sum + Number(tip.amount || 0), 0),
        is_actually_live: isActuallyLive,
        tips: undefined // Remove raw tips data to reduce response size
      };
    });
    
    // Get NFT minting activity
    let nftsQuery = supabase
      .from('nfts')
      .select(`
        *,
        stream:stream_id(*),
        owner:owner_id(id, username, wallet_address, avatar_url)
      `)
      .eq('stream.creator_id', profile.id);
      
    if (timeRange !== 'all') {
      nftsQuery = nftsQuery.gte('created_at', startDateStr);
    }
    
    const { data: nfts, error: nftsError } = await nftsQuery
      .order('created_at', { ascending: false })
      .limit(20); // Limit to avoid very large response
      
    if (nftsError) {
      throw nftsError;
    }
    
    // Process NFT data to add image URLs
    const processedNfts = nfts.map(nft => {
      // NFTs use IPFS for metadata storage, not Arweave
      const imageUrl = nft.metadata_uri 
        ? nft.metadata_uri.startsWith('http') 
          ? nft.metadata_uri 
          : `https://ipfs.io/ipfs/${nft.metadata_uri.replace('ipfs://', '')}`
        : `https://placehold.co/640x360/000000/FFFFFF?text=NFT+Moment+${nft.stream.title}`;
      
      return {
        ...nft,
        image_url: imageUrl
      };
    });
    
    // Count streams, views, and NFTs
    const streamCount = streams.length;
    const viewCount = streams.reduce((sum, stream) => sum + (stream.view_count || 0), 0);
    const nftCount = nfts.length;
    
    // Return dashboard data
    return NextResponse.json({
      earnings: {
        totalAmount,
        creatorAmount,
        feePercentage,
        tipCount: tipsData.length,
        percentChange,
        streamCount,
        viewCount,
        nftCount
      },
      tips: processedTips,
      streams: processedStreams || [],
      nfts: processedNfts || []
    });
  } catch (error) {
    console.error('Error fetching creator dashboard data:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 