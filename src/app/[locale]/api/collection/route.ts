import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const ownerWalletAddress = searchParams.get('owner');
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const sort = searchParams.get('sort') || 'created_at';
    const direction = searchParams.get('direction') || 'desc';
    
    if (!ownerWalletAddress) {
      return NextResponse.json(
        { error: 'Owner wallet address is required' },
        { status: 400 }
      );
    }
    
    
    // Validate sort field to prevent SQL injection
    const validSortFields = ['created_at', 'stream_title', 'platform_fee_at_mint', 'timestamp'];
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    
    // Validate sort direction
    const sortDirection = direction === 'asc' ? 'asc' : 'desc';
    
    try {
      // Get supabase client
      const supabase = await createClient();
      
      // Get user ID for the wallet address
      try {
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('id')
          .eq('wallet_address', ownerWalletAddress as string)
          .single();
        
        
        if (userError) {
          console.error('Error fetching user profile:', userError);
          return NextResponse.json(
            { error: 'User not found', details: userError.message },
            { status: 404 }
          );
        }
        
        if (!userData) {
          return NextResponse.json(
            { error: 'User not found', details: 'No profile exists for this wallet address' },
            { status: 404 }
          );
        }
        
        // Create query
        try {
          let query = supabase
            .from('nfts')
            .select(`
              id,
              mint_address,
              metadata_uri,
              timestamp,
              platform_fee_at_mint,
              created_at,
              stream_id,
              stream:stream_id (
                title,
                creator:creator_id (
                  username,
                  wallet_address
                )
              )
            `, { count: 'exact' })
            .eq('owner_id', userData.id);
          
          // Apply sorting logic
          if (sortField === 'stream_title') {
            // Special case for sorting by stream title since it's in a joined table
            query = query.order('stream(title)', { ascending: sortDirection === 'asc' });
          } else {
            query = query.order(sortField, { ascending: sortDirection === 'asc' });
          }
          
          // Apply pagination
          const { data: nfts, error: nftsError, count } = await query
            .range((page - 1) * limit, page * limit - 1);
          
          
          if (nftsError) {
            console.error('Error fetching NFTs:', nftsError);
            return NextResponse.json(
              { error: 'Failed to fetch NFTs', details: nftsError.message },
              { status: 500 }
            );
          }
          
          // Calculate pagination info
          const totalPages = count ? Math.ceil(count / limit) : 0;
          const hasMore = page < totalPages;
          
          
          return NextResponse.json({
            nfts: nfts || [],
            pagination: {
              page,
              limit,
              total: count,
              totalPages,
              hasMore
            }
          });
        } catch (nftQueryError) {
          console.error('Error during NFT query:', nftQueryError);
          return NextResponse.json(
            { error: 'Error querying NFTs', details: (nftQueryError as Error).message },
            { status: 500 }
          );
        }
      } catch (profileQueryError) {
        console.error('Error during profile query:', profileQueryError);
        return NextResponse.json(
          { error: 'Error querying user profile', details: (profileQueryError as Error).message },
          { status: 500 }
        );
      }
    } catch (clientError) {
      console.error('Error creating Supabase client:', clientError);
      return NextResponse.json(
        { error: 'Failed to create database client', details: (clientError as Error).message },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unhandled error in collection API:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 