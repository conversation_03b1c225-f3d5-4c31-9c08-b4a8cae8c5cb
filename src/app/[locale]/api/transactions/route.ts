import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Helper function to handle errors consistently
function handleError(error: unknown, message: string, status = 500) {
  console.error(`${message}:`, error);
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  return NextResponse.json(
    { error: message, details: errorMessage },
    { status }
  );
}

export async function GET(request: NextRequest) {
  let supabase = null;
  
  try {
    const searchParams = request.nextUrl.searchParams;
    const wallet = searchParams.get('wallet');
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    
    if (!wallet) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }
    
    try {
      // Get supabase client - properly await it
      supabase = await createClient();
    } catch (clientError) {
      return handleError(clientError, 'Failed to create database client');
    }
    
    try {
      // Add a unique request ID to help with debugging
      const requestId = `tx-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
      
      // Get user ID for the wallet address
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', wallet as string)
        .single();
      
      if (userError || !userData) {
        console.error(`[${requestId}] User not found: ${userError?.message || 'No data returned'}`);
        return NextResponse.json(
          { error: 'User not found', details: userError?.message },
          { status: 404 }
        );
      }
      
      let tips: unknown[] = [];
      try {
        const { data, error } = await supabase
          .from('tips')
          .select(`
            id,
            stream_id,
            amount,
            platform_fee_percentage,
            platform_fee_amount,
            tx_signature,
            created_at,
            stream:stream_id (
              title
            )
          `)
          .eq('tipper_id', userData.id)
          .order('created_at', { ascending: false })
          .limit(limit);
          
        if (error) {
          console.error(`[${requestId}] Error fetching tips:`, error);
        } else {
          tips = data as typeof tips || [];
        }
      } catch (tipsError) {
        console.error(`[${requestId}] Exception fetching tips:`, tipsError);
      }
      
      // Get NFT mints with proper error handling
      let nfts: unknown[] = [];
      try {
        const { data, error } = await supabase
          .from('nfts')
          .select(`
            id,
            stream_id,
            tx_signature,
            created_at,
            stream:stream_id (
              title
            )
          `)
          .eq('owner_id', userData.id)
          .order('created_at', { ascending: false })
          .limit(limit);
          
        if (error) {
          console.error(`[${requestId}] Error fetching NFTs:`, error);
        } else {
          nfts = data || [];
        }
      } catch (nftsError) {
        console.error(`[${requestId}] Exception fetching NFTs:`, nftsError);
      }
      
      // Format transactions
      const tipTransactions = (tips || []).map((tip: unknown) => {
        const t = tip as { 
          id: string; 
          stream_id: string; 
          amount: number;
          platform_fee_amount: number;
          tx_signature: string; 
          created_at: string;
          stream?: Array<{ title: string }>;
        };
        return {
          id: `tip_${t.id}`,
          type: 'tip',
          stream_id: t.stream_id,
          stream_title: t.stream?.[0]?.title,
          amount: t.amount,
          platform_fee_amount: t.platform_fee_amount,
          tx_signature: t.tx_signature,
          created_at: t.created_at
        };
      });
      
      const nftTransactions = (nfts || []).map((nft: unknown) => {
        const n = nft as { 
          id: string; 
          stream_id: string;
          tx_signature: string;
          created_at: string;
          stream?: Array<{ title: string }>;
        };
        return {
          id: `nft_${n.id}`,
          type: 'nft',
          stream_id: n.stream_id,
          stream_title: n.stream?.[0]?.title,
          tx_signature: n.tx_signature,
          created_at: n.created_at
        };
      });
      
      // Combine and sort by date
      const allTransactions = [...tipTransactions, ...nftTransactions]
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, limit);
      
      
      return NextResponse.json({
        transactions: allTransactions
      });
    } catch (dbError) {
      return handleError(dbError, 'Database operation failed');
    }
  } catch (error) {
    return handleError(error, 'Server error in transactions API');
  }
} 