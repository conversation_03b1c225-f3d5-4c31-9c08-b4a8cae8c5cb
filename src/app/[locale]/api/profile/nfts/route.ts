import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getSession } from '@/lib/auth';

// GET: Fetch the current user's NFT collection
export async function GET(request: NextRequest) {
  try {
    // Get current session
    const session = await getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized, please connect your wallet' },
        { status: 401 }
      );
    }

    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Fetch NFTs from database
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('nfts')
      .select(`
        *,
        stream:stream_id(id, title, creator_id)
      `)
      .eq('owner_id', session.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
      
    if (error) {
      throw error;
    }
    
    // Get total count
    const { count, error: countError } = await supabase
      .from('nfts')
      .select('*', { count: 'exact', head: true })
      .eq('owner_id', session.id);
      
    if (countError) {
      throw countError;
    }
    
    // Process data to add generated image URLs
    const processedNfts = data?.map(nft => {
      // NFTs use IPFS for metadata storage, not Arweave
      const imageUrl = nft.metadata_uri 
        ? nft.metadata_uri.startsWith('http') 
          ? nft.metadata_uri 
          : `https://ipfs.io/ipfs/${nft.metadata_uri.replace('ipfs://', '')}`
        : `https://placehold.co/640x360/000000/FFFFFF?text=NFT+Moment+${nft.stream.title}`;
      
      return {
        ...nft,
        image_url: imageUrl
      };
    });
    
    return NextResponse.json({
      nfts: processedNfts || [],
      total: count || 0,
      limit,
      offset
    });
  } catch (error) {
    console.error('Error fetching NFT collection:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 