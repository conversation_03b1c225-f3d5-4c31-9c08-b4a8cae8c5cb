import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { auth } from '@/lib/auth';

// Validate profile update data
const profileUpdateSchema = z.object({
  wallet_address: z.string(), 
  username: z.string().min(3).max(50).optional(),
  avatar_url: z.string().url().optional().or(z.string().length(0)),
  bio: z.string().max(1000).optional().or(z.string().length(0)), 
  is_creator: z.boolean().optional(),
});

// Define interfaces for TypeScript
interface ProfileUpdate {
  username?: string;
  avatar_url?: string;
  bio?: string;
  is_creator?: boolean;
}

interface NewProfileData {
  wallet_address: string;
  username?: string;
  avatar_url?: string;
  bio?: string;
  is_creator?: boolean;
  created_at: string;
}

// Helper function to handle common errors
function handleError(error: Error | unknown, message: string, status = 500) {
  console.error(`${message}:`, error);
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  // Include error code if available for easier debugging
  const details = (error as { code?: string }).code ? `${(error as { code: string }).code}: ${errorMessage}` : errorMessage;
  
  return NextResponse.json(
    { error: message, details },
    { status }
  );
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const wallet = searchParams.get('wallet');
    
    if (!wallet) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }
    
    
    // Create Supabase client with enhanced error handling
    let supabase;
    try {
      // Check environment variables first
      if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
        console.error('Missing Supabase environment variables');
        return NextResponse.json(
          { error: 'Database configuration error' },
          { status: 500 }
        );
      }
      
      supabase = await createClient();
      
      if (!supabase) {
        console.error('Supabase client is null');
        return NextResponse.json(
          { error: 'Failed to create database client' },
          { status: 500 }
        );
      }
      
      
      
    } catch (clientError) {
      console.error('Failed to create Supabase client:', clientError);
      return NextResponse.json(
        { error: 'Failed to create database client', details: (clientError as Error).message },
        { status: 500 }
      );
    }
    
    // Get profile for the wallet address
    try {
      // Cast the supabase client to any to bypass TypeScript errors
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('wallet_address', wallet)
        .single();
      
      if (error) {
        
        // If profile doesn't exist, create one
        if (error.code === 'PGRST116') {
          try {
            // Check if this wallet should be an admin (add your wallet addresses here)
            const adminWallets = [
              process.env.PLATFORM_WALLET_ADDRESS,
              // Add other admin wallet addresses here
            ].filter(Boolean); // Remove null/undefined entries
            

            
            const isAdmin = adminWallets.includes(wallet);
            
            // Insert with type assertion to bypass TypeScript errors
            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert({
                wallet_address: wallet,
                is_creator: false,
                is_admin: isAdmin,
                created_at: new Date().toISOString()
              })
              .select()
              .single();
            
            if (createError) {
              return handleError(createError, 'Failed to create profile');
            }
            
            return NextResponse.json({ profile: newProfile });
          } catch (insertError) {
            return handleError(insertError, 'Exception during profile creation');
          }
        }
        
        return handleError(error, 'Failed to fetch profile');
      }

      return NextResponse.json({ profile });
    } catch (queryError) {
      return handleError(queryError, 'Exception during profile query');
    }
  } catch (error) {
    return handleError(error, 'Unhandled error in profile API');
  }
}

export async function PUT(request: NextRequest) {
  
  try {
    const body = await request.json();
    
    // Validate request body
    const validation = profileUpdateSchema.safeParse(body);
    if (!validation.success) {
      console.error("Validation error:", validation.error.errors);
      return NextResponse.json(
        { error: 'Invalid profile data', details: validation.error.errors },
        { status: 400 }
      );
    }
    
    // Extract the validated data
    const { wallet_address, username, avatar_url, bio, is_creator } = validation.data;
    
    if (!wallet_address) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }
    
    
    // Create Supabase client
    const supabase = await createClient();
    if (!supabase) {
      return handleError(new Error("Database connection failed"), 'Failed to create database client');
    }
    
    // For security in production, this is where we would validate the request
    // against the session to ensure the user can only update their own profile
    
    try {
      // Prepare profile data for upsert
      const profileData: NewProfileData = {
        wallet_address,
        created_at: new Date().toISOString()
      };
      
      // Only include defined fields
      if (username !== undefined) profileData.username = username;
      if (avatar_url !== undefined) profileData.avatar_url = avatar_url;
      if (bio !== undefined) profileData.bio = bio;
      if (is_creator !== undefined) profileData.is_creator = is_creator;
      
      
      // Use upsert pattern which handles both creation and updating
      let profileResult = null;
      const { data, error } = await supabase
        .from('profiles')
        .upsert(profileData, { 
          onConflict: 'wallet_address',
          ignoreDuplicates: false
        })
        .select()
        .maybeSingle(); // Use maybeSingle() instead of single() to handle empty result gracefully
      
      if (error) {
        console.error("Profile upsert error:", error);
        
        // If upsert fails, try fetching the current profile first
        if (error.code === 'PGRST116' || error.code === '42501') {
          
          // First check if the profile exists
          const { data: existingProfiles, error: fetchError } = await supabase
            .from('profiles')
            .select('*')
            .eq('wallet_address', wallet_address);
          
          if (fetchError) {
            return handleError(fetchError, 'Failed to check profile existence');
          }
          
          if (existingProfiles && existingProfiles.length > 0) {
            // Profile exists, perform an update
            const updateData: ProfileUpdate = {};
            if (username !== undefined) updateData.username = username;
            if (avatar_url !== undefined) updateData.avatar_url = avatar_url;
            if (bio !== undefined) updateData.bio = bio;
            if (is_creator !== undefined) updateData.is_creator = is_creator;
            
            const { data: updatedProfile, error: updateError } = await supabase
              .from('profiles')
              .update(updateData)
              .eq('wallet_address', wallet_address)
              .select()
              .maybeSingle();
            
            if (updateError) {
              return handleError(updateError, 'Failed to update profile');
            }
            
            profileResult = updatedProfile;
          } else {
            // Profile doesn't exist, create new one
            const { data: newProfile, error: insertError } = await supabase
              .from('profiles')
              .insert(profileData)
              .select()
              .maybeSingle();
            
            if (insertError) {
              return handleError(insertError, 'Failed to create profile');
            }
            
            profileResult = newProfile;
          }
        } else {
          return handleError(error, 'Failed to update profile');
        }
      } else {
        profileResult = data;
      }
      
      // Revalidate paths
      const locale = request.nextUrl.pathname.split('/')[1];
      revalidatePath(`/${locale}/profile`);
      if (username) {
        revalidatePath(`/${locale}/c/${username}`);
      }
      if (is_creator !== undefined) {
        revalidatePath(`/${locale}/creator/dashboard`);
      }
      
      return NextResponse.json({ profile: profileResult });
      
    } catch (error) {
      console.error("Exception during profile operation:", error);
      return handleError(error, 'Failed to update profile');
    }
  } catch (error) {
    console.error("Unhandled error in profile update API:", error);
    return handleError(error, 'Server error in profile API');
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { wallet_address, is_admin } = body;
    
    if (!wallet_address) {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }
    
    // Use auth utility to verify the request
    const session = await auth(request.headers);
    
    // Only admin can update admin status
    if (!session?.user || !session.user.isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized: Only admins can update admin status' },
        { status: 401 }
      );
    }
    
    // Create Supabase client
    let supabase;
    try {
      supabase = await createClient();
      if (!supabase) {
        return handleError(new Error("Database connection failed"), 'Failed to create database client');
      }
    } catch (clientError) {
      return handleError(clientError, 'Failed to create database client');
    }
    
    // Update admin status
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .update({ is_admin: is_admin === true })
        .eq('wallet_address', wallet_address)
        .select()
        .single();
      
      if (error) {
        return handleError(error, 'Failed to update admin status');
      }
      
      return NextResponse.json({ success: true, profile });
    } catch (updateError) {
      return handleError(updateError, 'Error updating admin status');
    }
  } catch (error) {
    return handleError(error, 'Server error in admin status update');
  }
} 