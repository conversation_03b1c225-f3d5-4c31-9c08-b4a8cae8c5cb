import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getSession } from '@/lib/auth';

// GET: Fetch the current user's tip history
export async function GET(request: NextRequest) {
  try {
    // Get current session
    const session = await getSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized, please connect your wallet' },
        { status: 401 }
      );
    }

    // Get search params
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Fetch tips from database
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('tips')
      .select(`
        *,
        stream:stream_id(id, title),
        recipient:recipient_id(id, username, wallet_address)
      `)
      .eq('tipper_id', session.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
      
    if (error) {
      throw error;
    }
    
    // Get total count
    const { count, error: countError } = await supabase
      .from('tips')
      .select('*', { count: 'exact', head: true })
      .eq('tipper_id', session.id);
      
    if (countError) {
      throw countError;
    }
    
    return NextResponse.json({
      tips: data || [],
      total: count || 0,
      limit,
      offset
    });
  } catch (error) {
    console.error('Error fetching tip history:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
} 