'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export default function CreatorRegisterRedirect() {
  const router = useRouter();
  
  useEffect(() => {
    // Since everyone can stream now, redirect to go-live
    router.replace('/go-live');
  }, [router]);
  
  return (
    <div className="min-h-screen bg-bonk-gradient-bg text-white flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" text="Ready to go live? Redirecting..." />
      </div>
    </div>
  );
} 