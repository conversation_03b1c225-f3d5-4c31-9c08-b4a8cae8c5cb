import { getTranslations } from 'next-intl/server';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'creator.dashboard' });
  return {
    title: `${t('title')} | bonkstream`,
    description: 'Manage your bonkstream creator settings and view your stream analytics',
  };
} 