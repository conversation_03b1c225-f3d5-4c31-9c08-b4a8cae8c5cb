'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { Link } from '@/lib/i18n';
import { useTranslations } from 'next-intl';
import { LiveStreamCard } from '@/components/streaming/LiveStreamCard';
import { Search, Loader2 } from 'lucide-react';
import { LoadingScreen } from '@/components/ui/LoadingSpinner';

type Stream = {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  status: 'live' | 'ended' | 'scheduled';
  livepeer_playback_id: string;
  livepeer_stream_id: string;
  is_actually_live?: boolean;
  creator: {
    id: string;
    username: string;
    wallet_address: string;
    avatar_url: string;
  };
  created_at: string;
  view_count: number;
  tip_count: number;
  nft_count: number;
  like_count: number;
};

function StreamsContent() {
  const t = useTranslations('stream');
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const filterParam = searchParams.get('filter');
  const locale = params.locale as string || 'en';
  
  const [loading, setLoading] = useState(true);
  const [streams, setStreams] = useState<Stream[]>([]);
  const [selectedCategory] = useState('all');
  const [filter, setFilter] = useState(filterParam || 'all');
  const [searchTerm, setSearchTerm] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [, setError] = useState<string | null>(null);
  
  // Fetch streams with delay to prevent navigation blocking
  useEffect(() => {
    const fetchStreams = async () => {
      try {
        setLoading(true);
        
        let url = `/${locale}/api/streams?page=${page}&limit=12`;
        
        if (filter === 'live') {
          url += '&status=live';
        } else if (filter === 'ended') {
          url += '&status=ended';
        }
        
        if (selectedCategory !== 'all') {
          url += `&category=${selectedCategory}`;
        }

        // Add a small delay to ensure navigation is complete
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error('Failed to fetch streams');
        }
        
        const data = await response.json();
        
        if (page === 1) {
          // If filter is live, only show actually live streams
          if (filter === 'live') {
            setStreams(data.streams.filter((s: Stream) => s.is_actually_live));
          } else {
            setStreams(data.streams);
          }
        } else {
          // Append new streams to the existing list
          if (filter === 'live') {
            setStreams(prev => [...prev, ...data.streams.filter((s: Stream) => s.is_actually_live)]);
          } else {
            setStreams(prev => [...prev, ...data.streams]);
          }
        }
        
        setHasMore(data.hasMore);
      } catch (err) {
        console.error('Error fetching streams:', err);
        setError('Failed to load streams. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchStreams();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, filter, selectedCategory]); // locale intentionally omitted
  
  // Handle filter change
  const handleFilterChange = (newFilter: string) => {
    setFilter(newFilter);
    setPage(1);
    setStreams([]);
    router.push(`/streams?filter=${newFilter}`, { scroll: false });
  };
  
  // Handle load more
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  };

  // Get filtered streams based on API data and search term
  let filteredStreams = streams;
  
  // Apply search filter
  if (searchTerm.trim()) {
    filteredStreams = streams.filter(stream => 
      stream.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      stream.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      stream.creator.username.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }



  if (loading) {
    return (
      <LoadingScreen text="Loading streams..." />
    );
  }

  return (
    <div className="min-h-screen bg-bonk-gradient-bg text-white">
      {/* Header with BONK gradient */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-bonk-gradient-sunset"></div>
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-bonk-gradient-radial opacity-30 rounded-full filter blur-3xl animate-pulse"></div>
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-bonk-orange/20 rounded-full filter blur-3xl animate-bonk-float"></div>
        
        <div className="container mx-auto px-4 py-12 relative z-10">
          <div className="animate-slide-up">
            <h1 className="bonk-header text-3xl md:text-4xl mb-2">{t('trending')}</h1>
            <p className="bonk-body text-white/80 mb-8">Discover the latest and most popular streams</p>
            
            {/* Filter tabs */}
            <div className="flex space-x-2 mb-6 overflow-x-auto no-scrollbar">
              <button
                className={`bonk-btn px-4 py-2 rounded-full text-sm transition-all ${filter === 'all' ? 'bg-bonk-orange-red-2 text-white' : 'bg-bonk-widget-dark text-white hover:bg-bonk-widget-black'}`}
                onClick={() => handleFilterChange('all')}
              >
                All Streams
              </button>
              <button
                className={`bonk-btn px-4 py-2 rounded-full text-sm transition-all ${filter === 'live' ? 'bg-bonk-orange-red-2 text-white' : 'bg-bonk-widget-dark text-white hover:bg-bonk-widget-black'}`}
                onClick={() => handleFilterChange('live')}
              >
                Live Now
              </button>
            </div>
            
            {/* Search and filter */}
            <div className="flex flex-col md:flex-row gap-4 justify-between items-start md:items-center mb-8">
              <div className="relative w-full md:w-1/3">
                <input 
                  type="text" 
                  placeholder="Search streams..." 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-bonk-widget-dark border border-bonk-orange/30 rounded-full px-5 py-3 pl-12 bonk-body focus:outline-none focus:ring-2 focus:ring-bonk-orange/50 focus:border-transparent"
                />
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-bonk-orange" size={18} />
              </div>
            </div>
          </div>
          
          {/* Live streams section - only show if filter is all or live */}
          {(filter === 'all' || filter === 'live') && filteredStreams.filter(stream => stream.is_actually_live).length > 0 && (
            <div className="mb-12">
              <h2 
                className="bonk-header text-2xl mb-4 flex items-center animate-slide-up"
                style={{ animationDelay: '0.2s' }}
              >
                <span className="w-2 h-2 bg-bonk-red rounded-full animate-pulse mr-2"></span>
                Live Now
              </h2>
              <div 
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
              >
                {filteredStreams
                  .filter(stream => stream.is_actually_live)
                  .map((stream, index) => (
                    <div 
                      key={stream.id} 
                      className="animate-slide-up"
                      style={{ animationDelay: `${index * 80}ms` }}
                    >
                      <LiveStreamCard stream={stream} />
                    </div>
                  ))}
              </div>
            </div>
          )}
          
          {/* Other streams section */}
          <div className="mb-6">
            <h2 
              className="bonk-header text-2xl mb-4 animate-slide-up"
              style={{ animationDelay: '0.3s' }}
            >
              {filter === 'live' ? 'Live Streams' : 'All Streams'}
            </h2>
            {(filter !== 'live' || filteredStreams.some(s => s.is_actually_live)) ? (
              <div 
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
              >
                {(filter === 'live' 
                  ? filteredStreams.filter(stream => stream.is_actually_live)
                  : filter === 'ended'
                    ? filteredStreams.filter(stream => !stream.is_actually_live)
                    : filteredStreams.filter(stream => !stream.is_actually_live))
                  .map((stream, index) => (
                    <div 
                      key={stream.id} 
                      className="animate-slide-up"
                      style={{ animationDelay: `${index * 80}ms` }}
                    >
                      <LiveStreamCard stream={stream} />
                    </div>
                  ))}
              </div>
            ) : (
              <div 
                className="flex flex-col items-center justify-center py-16 text-center animate-fade-in"
              >
                <div className="w-20 h-20 bg-bonk-widget-dark rounded-full flex items-center justify-center mb-4">
                  <Search size={32} className="text-bonk-orange" />
                </div>
                <h3 className="bonk-header text-xl mb-2">No streams found</h3>
                <p className="bonk-body text-white/60 max-w-md">We couldn&apos;t find any streams matching your search. Try adjusting your filters or check back later.</p>
              </div>
            )}
          </div>
          
          {/* Load More button */}
          {hasMore && (
            <div 
              className="mt-8 text-center animate-slide-up"
              style={{ animationDelay: '0.4s' }}
            >
              <button 
                className="bonk-btn px-6 py-3 bg-bonk-widget-dark hover:bg-bonk-widget-black rounded-full text-white flex items-center gap-2 mx-auto"
                onClick={handleLoadMore}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>Load More</>
                )}
              </button>
            </div>
          )}
          
          {/* Discover More Button */}
          <div 
            className="mt-12 text-center animate-slide-up"
            style={{ animationDelay: '0.5s' }}
          >
            <Link 
              href="/discover" 
              className="bonk-btn inline-flex items-center gap-2 px-8 py-3 bg-bonk-gradient-orange text-white rounded-full hover:shadow-bonk-lg transition-all"
            >
              Discover More
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function StreamsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-bonk-black text-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Loading streams...</p>
        </div>
      </div>
    }>
      <StreamsContent />
    </Suspense>
  );
}
