import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: streamId } = await params;
    
    if (!streamId) {
      return NextResponse.json(
        { error: 'Stream ID is required' },
        { status: 400 }
      );
    }
    
    // Get user information (if authenticated)
    const supabase = await createClient();
    const { data: sessionData } = await supabase.auth.getSession();
    const userId = sessionData.session?.user?.id;
    
    // Create a view record
    const viewData = {
      stream_id: streamId,
      viewer_id: userId || null, // Can be anonymous view
      view_timestamp: new Date().toISOString(),
      ip_hash: hashIp(request), // For anonymous view tracking
      user_agent: request.headers.get('user-agent') || null,
    };
    
    // Record view in the database
    const { error: viewError } = await supabase
      .from('stream_views')
      .insert(viewData);
    
    if (viewError) {
      console.error('Error recording view:', viewError);
      // Don't return error to client, just log it
    }
    
    // Increment view count in the streams table
    const { error: updateError } = await supabase.rpc('increment_view_count', {
      stream_id: streamId
    });
    
    if (updateError) {
      console.error('Error incrementing view count:', updateError);
      // Don't return error to client, just log it
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error recording view:', error);
    return NextResponse.json(
      { error: 'Server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}

// Helper function to hash IP for privacy
function hashIp(request: NextRequest): string {
  // Get IP from request
  const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '';
  
  // Create a simple hash of the IP
  let hash = 0;
  for (let i = 0; i < ip.length; i++) {
    const char = ip.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  // Return as string
  return hash.toString(16);
} 
