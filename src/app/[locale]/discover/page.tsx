'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/wallet/WalletButton';
import { ChevronUp, ChevronDown, Play, Volume2, VolumeX, Share, Gift, Wallet, Coins } from 'lucide-react';
import Image from 'next/image';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { LivePlayer } from '@/components/streaming/LivePlayer';
import { TipButton } from '@/components/stream/TipButton';
import { MintNFTButton } from '@/components/stream/MintNFTButton';
import { LikeButton } from '@/components/stream/LikeButton';
import { VideoFloatingHearts, VideoFloatingHeartsRef } from '@/components/stream/VideoFloatingHearts';

type Stream = {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  playback_url: string;
  livepeer_stream_id?: string;
  livepeer_playback_id?: string;
  status: 'live' | 'ended' | 'scheduled';
  is_actually_live?: boolean;
  creator: {
    id: string;
    username: string;
    wallet_address: string;
    avatar_url: string;
  };
  created_at: string;
  view_count: number;
  tip_count: number;
  nft_count: number;
  like_count: number;
};

export default function DiscoverPage() {
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string || 'en';
  const { connected } = useWallet();
  
  // Core state
  const [streams, setStreams] = useState<Stream[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeStreamIndex, setActiveStreamIndex] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  
  // UI state - only active video plays
  const [isMuted, setIsMuted] = useState(true);
  const [activelyPlaying, setActivelyPlaying] = useState<number | null>(null);
  const [videoElement, setVideoElement] = useState<HTMLVideoElement | null>(null);
  
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const streamRefs = useRef<(HTMLDivElement | null)[]>([]);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const isNavigatingRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const videoHeartsRef = useRef<VideoFloatingHeartsRef>(null);

  // Clean navigation handler
  const navigateAway = useCallback((path: string) => {
    isNavigatingRef.current = true;
    
    // Abort any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Clear video state
    setActivelyPlaying(null);
    setVideoElement(null);
    
    // Disconnect observer
    if (observerRef.current) {
      observerRef.current.disconnect();
      observerRef.current = null;
    }
    
    router.push(path);
  }, [router]);

  // Fetch only live streams
  const fetchStreams = useCallback(async (pageNum: number = 1) => {
    if (isNavigatingRef.current) return;
    
    const controller = new AbortController();
    abortControllerRef.current = controller;
    
    try {
      setLoading(true);
      
      const response = await fetch(`/${locale}/api/streams?page=${pageNum}&limit=20`, {
        signal: controller.signal,
        cache: 'no-store'
      });
      
      if (controller.signal.aborted || isNavigatingRef.current) return;
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (controller.signal.aborted || isNavigatingRef.current) return;
      
      // Only include actually live streams - no ended streams at all
      const liveStreams = (data.streams || []).filter((stream: Stream) => 
        stream.is_actually_live === true && stream.status === 'live'
      );
      
      if (pageNum === 1) {
        setStreams(liveStreams);
        // Start playing first video immediately
        if (liveStreams.length > 0) {
          setActiveStreamIndex(0);
          setActivelyPlaying(0);
        }
      } else {
        setStreams(prev => [...prev, ...liveStreams]);
      }
      
      setHasMore(liveStreams.length === 20);
      setError(null);
    } catch (err) {
      if (!isNavigatingRef.current && !(err instanceof Error && err.name === 'AbortError')) {
        console.error('Error fetching streams:', err);
        setError('Failed to load streams. Please check your connection.');
      }
    } finally {
      if (!isNavigatingRef.current) {
        setLoading(false);
      }
    }
  }, [locale]);

  // Initial load
  useEffect(() => {
    isNavigatingRef.current = false;
    fetchStreams(1);
  }, [fetchStreams]);

  // View tracking
  const recordView = useCallback(async (streamId: string) => {
    if (isNavigatingRef.current) return;
    
    try {
      fetch(`/${locale}/api/streams/${streamId}/view`, {
        method: 'POST',
        signal: AbortSignal.timeout(3000),
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      }).catch(() => {
        // Silently ignore analytics failures
      });
    } catch {
      // Silently ignore
    }
  }, [locale]);

  // Intersection Observer for scroll-based video switching
  useEffect(() => {
    if (streams.length === 0 || isNavigatingRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (isNavigatingRef.current) return;
        
        entries.forEach((entry) => {
          const index = streamRefs.current.indexOf(entry.target as HTMLDivElement);
          
          if (entry.isIntersecting && index >= 0 && entry.intersectionRatio > 0.7) {
            if (index !== activeStreamIndex) {
              setActiveStreamIndex(index);
              setActivelyPlaying(index);
              recordView(streams[index].id);
              
              // Load more if needed
              if (index >= streams.length - 3 && hasMore && !loading) {
                setPage(prev => prev + 1);
                fetchStreams(page + 1);
              }
            }
          }
        });
      },
      { threshold: [0.7], rootMargin: '0px' }
    );

    observerRef.current = observer;
    
    streamRefs.current.forEach((ref) => {
      if (ref && !isNavigatingRef.current) observer.observe(ref);
    });

    return () => {
      observer.disconnect();
      observerRef.current = null;
    };
  }, [streams, hasMore, loading, page, fetchStreams, recordView, activeStreamIndex]);

  // Navigation controls
  const navigateToStream = useCallback((direction: 'up' | 'down') => {
    if (streams.length === 0 || isNavigatingRef.current) return;
    
    const newIndex = direction === 'up' 
      ? Math.max(0, activeStreamIndex - 1)
      : Math.min(streams.length - 1, activeStreamIndex + 1);
    
    const targetRef = streamRefs.current[newIndex];
    if (targetRef && !isNavigatingRef.current) {
      targetRef.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [activeStreamIndex, streams.length]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isNavigatingRef.current) return;
      
      // Don't interfere with inputs or buttons
      const activeElement = document.activeElement;
      if (activeElement && (
        activeElement.tagName === 'BUTTON' || 
        activeElement.tagName === 'A' || 
        activeElement.tagName === 'INPUT' ||
        activeElement.closest('header') ||
        activeElement.closest('[role="dialog"]')
      )) return;
      
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          navigateToStream('up');
          break;
        case 'ArrowDown':
          e.preventDefault();
          navigateToStream('down');
          break;
        case ' ':
        case 'm':
        case 'M':
          e.preventDefault();
          setIsMuted(prev => !prev);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [navigateToStream]);

  // Share functionality
  const handleShare = useCallback(async (stream: Stream) => {
    try {
      const shareData = {
        title: stream.title,
        text: `Check out ${stream.creator.username}'s live stream on bonkstream`,
        url: `${window.location.origin}/stream/${stream.id}`
      };
      
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        await navigator.clipboard.writeText(shareData.url);
        // You could add a toast notification here
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isNavigatingRef.current = true;
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Loading state
  if (loading && streams.length === 0) {
    return (
      <div className="min-h-[calc(100vh-160px)] bg-bonk-gradient-bg text-white">
        <div className="h-[calc(100vh-160px)] flex items-center justify-center">
          <LoadingSpinner size="md" variant="simple" />
        </div>
      </div>
    );
  }

  // Error state
  if (error && streams.length === 0) {
    return (
      <div className="min-h-[calc(100vh-160px)] bg-bonk-gradient-bg text-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6 bg-bonk-widget-dark rounded-lg border border-bonk-red/30">
          <div className="w-12 h-12 mx-auto mb-4 text-bonk-red">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="bonk-header text-xl mb-2">Error Loading Streams</h2>
          <p className="bonk-body mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="bonk-btn px-4 py-2 bg-bonk-red/20 hover:bg-bonk-red/30 rounded-md transition"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // No live streams
  if (!loading && streams.length === 0) {
    return (
      <div className="min-h-[calc(100vh-160px)] bg-bonk-gradient-bg text-white flex items-center justify-center p-6">
        <div className="text-center max-w-md mx-auto">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-bonk-widget-dark flex items-center justify-center">
            <Play size={32} className="text-bonk-orange" />
          </div>
          <h2 className="bonk-header text-2xl mb-4">No Live Streams</h2>
          <p className="bonk-body text-white/60 mb-6">
            No one is streaming live right now. Be the first to go live and earn BONK tokens!
          </p>
          {connected ? (
            <button
              onClick={() => navigateAway('/go-live')}
              className="bonk-btn px-6 py-3 bg-bonk-orange-red-2 text-white rounded-md hover:bg-bonk-orange transition"
            >
              Start Streaming
            </button>
          ) : (
            <div className="space-y-4">
              <p className="bonk-body text-sm text-white/60">Connect your wallet to start streaming</p>
              <WalletButton className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 text-white hover:from-blue-500 hover:to-blue-600 flex items-center justify-center p-0">
                <Wallet className="w-5 h-5" />
              </WalletButton>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-bonk-gradient-bg text-white">
      {/* Floating Hearts Component */}
      <VideoFloatingHearts ref={videoHeartsRef} className="fixed inset-0 pointer-events-none z-30" />
      
      {/* Main container with proper scroll handling */}
      <div 
        ref={containerRef}
        className="h-[calc(100vh-160px)] snap-y snap-mandatory overflow-y-auto scroll-smooth relative"
        style={{ scrollBehavior: 'smooth' }}
      >
        {streams.map((stream, index) => (
          <div 
            key={stream.id}
            ref={(el) => { streamRefs.current[index] = el; }}
            className="h-[calc(100vh-160px)] snap-start flex items-center justify-center relative overflow-hidden bg-black"
          >
            {/* Video/Stream Display */}
            <div className="absolute inset-0 w-full h-full">
              {/* Only load video player for the currently active stream */}
              {activelyPlaying === index && stream.livepeer_playback_id && !isNavigatingRef.current ? (
                <LivePlayer 
                  key={`${stream.id}-${index}-${activelyPlaying}`}
                  playbackId={stream.livepeer_playback_id}
                  streamId={stream.livepeer_stream_id}
                  isLive={true}
                  title={stream.title}
                  autoPlay={true}
                  controls={false}
                  aspectRatio={9/16}
                  onVideoRef={setVideoElement}
                />
              ) : (
                // Show thumbnail for all other videos
                <div className="w-full h-full">
                  {stream.thumbnail_url ? (
                    <Image
                      src={stream.thumbnail_url}
                      alt={stream.title}
                      fill
                      className="object-cover"
                      priority={index < 3}
                      sizes="100vw"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-bonk-orange/20 to-purple-900/20 flex items-center justify-center">
                      <Play size={64} className="text-white/50" />
                    </div>
                  )}
                  
                  {/* Video overlay */}
                  <div className="absolute inset-0 bg-black/10 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-black/60 rounded-full p-4">
                      <Play size={48} className="text-white" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Content overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20 pointer-events-none">
              {/* Live indicator */}
              <div className="absolute top-4 left-4 pointer-events-auto">
                <div className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-bold flex items-center gap-2">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  LIVE
                </div>
              </div>

              {/* Bottom section - Stream info and actions */}
              <div className="absolute bottom-0 left-0 right-0 p-4 pointer-events-auto">
                <div className="flex items-end justify-between">
                  {/* Stream info */}
                  <div className="flex-1 max-w-[70%]">
                    {/* Creator info */}
                    <div className="flex items-center gap-3 mb-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (isNavigatingRef.current) return;
                          navigateAway(`/c/${stream.creator.username || stream.creator.wallet_address}`);
                        }}
                        className="w-12 h-12 rounded-full overflow-hidden bg-gray-800 flex-shrink-0 transition-transform hover:scale-105"
                      >
                        {stream.creator.avatar_url ? (
                          <Image 
                            src={stream.creator.avatar_url} 
                            alt={stream.creator.username || "Creator"} 
                            width={48}
                            height={48}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-white/70 text-lg font-bold">
                            {stream.creator.username ? stream.creator.username[0].toUpperCase() : '?'}
                          </div>
                        )}
                      </button>
                      <div>
                        <p className="text-white font-bold text-lg">
                          @{stream.creator.username || stream.creator.wallet_address.slice(0, 8)}
                        </p>
                        <p className="text-white/70 text-sm">
                          {stream.view_count} views • {stream.like_count} likes
                        </p>
                      </div>
                    </div>
                    
                    {/* Stream title and description */}
                    <h2 className="text-white text-xl font-bold mb-2 line-clamp-2">
                      {stream.title}
                    </h2>
                    {stream.description && (
                      <p className="text-white/80 text-sm line-clamp-3 mb-4">
                        {stream.description}
                      </p>
                    )}
                  </div>
                  
                  {/* Action buttons - Match stream page exactly */}
                  <div className="absolute bottom-4 right-4 flex flex-col items-center gap-1 pointer-events-auto sm:bottom-6 sm:right-6">
                    {/* Creator Profile */}
                    <div className="flex flex-col items-center">
                      <div className="relative">
                        <div className="w-10 h-10 rounded-full bg-gray-800 overflow-hidden border-2 border-white sm:w-12 sm:h-12 relative">
                          {stream.creator.avatar_url ? (
                            <Image
                              src={stream.creator.avatar_url}
                              alt={stream.creator.username || 'Creator'}
                              fill
                              className="object-cover"
                              sizes="(max-width: 640px) 40px, 48px"
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center">
                              <span className="text-black font-bold text-sm">
                                {stream.creator.username?.charAt(0).toUpperCase() || 'C'}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Tip Button - Main CTA */}
                    {connected ? (
                      <div className="flex flex-col items-center">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (isNavigatingRef.current) return;
                            // Find the actual TipButton by looking for the button with "Send BONK" text
                            const tipButtons = Array.from(document.querySelectorAll('button')).filter(btn =>
                              btn.textContent?.includes('Send BONK')
                            );
                            if (tipButtons.length > 0) {
                              tipButtons[0].click();
                            }
                          }}
                          className="w-10 h-10 rounded-full text-white hover:shadow-lg flex items-center justify-center p-0 text-xl sm:w-12 sm:h-12 transition-all duration-200"
                          style={{ backgroundColor: '#FF5C01' }}
                          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e5530a'}
                          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#FF5C01'}
                        >
                          <Gift className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center">
                        <WalletButton className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 text-white hover:from-blue-500 hover:to-blue-600 flex items-center justify-center p-0 sm:w-12 sm:h-12">
                          <Wallet className="w-4 h-4" />
                        </WalletButton>
                      </div>
                    )}

                    {/* Mint NFT Button */}
                    {connected && (
                      <div className="flex flex-col items-center">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (isNavigatingRef.current) return;
                            // Find the actual MintNFTButton by looking for the button with "MINT NFT" text
                            const mintButtons = Array.from(document.querySelectorAll('button')).filter(btn =>
                              btn.textContent?.includes('MINT NFT')
                            );
                            if (mintButtons.length > 0) {
                              mintButtons[0].click();
                            }
                          }}
                          className="w-10 h-10 rounded-full text-white hover:shadow-lg flex items-center justify-center p-0 sm:w-12 sm:h-12 transition-all duration-200"
                          style={{ backgroundColor: '#FF5C01' }}
                          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e5530a'}
                          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#FF5C01'}
                        >
                          <Coins className="w-4 h-4" />
                        </button>
                      </div>
                    )}

                    {/* Like Button - Match desktop functionality */}
                    <div className="flex flex-col items-center">
                      <LikeButton
                        streamId={stream.id}
                        variant="mobile"
                        showCount={false}
                        onLikeChange={() => {}}
                        onHeartAnimation={() => videoHeartsRef.current?.triggerHearts()}
                      />
                    </div>

                    {/* Share Button */}
                    <div className="flex flex-col items-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (isNavigatingRef.current) return;
                          handleShare(stream);
                        }}
                        className="w-10 h-10 rounded-full bg-gray-700 hover:bg-gray-600 text-white flex items-center justify-center sm:w-12 sm:h-12"
                      >
                        <Share className="w-4 h-4" />
                      </button>
                    </div>

                    {/* Mute/Unmute button */}
                    <div className="flex flex-col items-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (isNavigatingRef.current) return;
                          setIsMuted(!isMuted);
                        }}
                        className="w-10 h-10 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center hover:bg-white/20 transition-colors sm:w-12 sm:h-12"
                      >
                        {isMuted ? (
                          <VolumeX className="w-4 h-4 text-white" />
                        ) : (
                          <Volume2 className="w-4 h-4 text-white" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Hidden TipButton and MintNFTButton for functionality */}
                  <div className="hidden">
                    <div id={`tip-button-${stream.id}`}>
                      <TipButton
                        streamId={stream.id}
                        creatorWalletAddress={stream.creator.wallet_address}
                        creatorName={stream.creator.username}
                        className="hidden"
                      />
                    </div>
                    <div id={`mint-button-${stream.id}`}>
                      <MintNFTButton
                        streamId={stream.id}
                        currentTime={0}
                        creatorWalletAddress={stream.creator.wallet_address}
                        creatorName={stream.creator.username}
                        {...(videoElement && { videoElement })}
                        className="hidden"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {/* Loading indicator */}
        {loading && streams.length > 0 && (
          <div className="h-32 flex items-center justify-center bg-black">
            <LoadingSpinner size="sm" variant="simple" />
          </div>
        )}
        
        {/* End indicator */}
        {!hasMore && streams.length > 0 && (
          <div className="h-24 flex items-center justify-center bg-black text-white/60">
            <p className="bonk-body">You&apos;ve reached the end!</p>
          </div>
        )}
      </div>

      {/* Navigation controls - Hidden on mobile */}
      <div className="hidden md:block fixed right-4 top-1/2 transform -translate-y-1/2 z-40 space-y-3">
        <button
          onClick={() => navigateToStream('up')}
          disabled={activeStreamIndex === 0}
          className="bg-black/50 backdrop-blur-sm rounded-full p-3 hover:bg-black/70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronUp size={20} className="text-white" />
        </button>
        <button
          onClick={() => navigateToStream('down')}
          disabled={activeStreamIndex >= streams.length - 1}
          className="bg-black/50 backdrop-blur-sm rounded-full p-3 hover:bg-black/70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronDown size={20} className="text-white" />
        </button>
      </div>

      {/* Stream counter */}
      <div className="fixed top-4 right-4 z-40 bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
        {activeStreamIndex + 1} / {streams.length}
      </div>
    </div>
  );
}