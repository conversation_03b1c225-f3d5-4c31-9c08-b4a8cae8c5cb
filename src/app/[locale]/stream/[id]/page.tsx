'use client';

import { useState, useEffect, useRef } from 'react';
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/wallet/WalletButton';
import { TipButton } from '@/components/stream/TipButton';
import { MintNFTButton } from '@/components/stream/MintNFTButton';
import { ArrowLeft, Share, Wallet, DollarSign, Eye, Heart, Play, Coins, Gem } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Link } from '@/lib/i18n';
import Image from 'next/image';
import { LikeButton } from '@/components/stream/LikeButton';
import { VideoFloatingHearts, VideoFloatingHeartsRef } from '@/components/stream/VideoFloatingHearts';
import { useViewerTracking } from '@/hooks/useViewerTracking';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { LivePlayer } from '@/components/streaming/LivePlayer';
import { LiveChat } from '@/components/stream/LiveChat';

type Stream = {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  playback_url: string;
  view_count: number;
  tip_count: number;
  nft_count: number;
  like_count: number;
  created_at: string;
  status: 'live' | 'ended' | 'scheduled';
  is_actually_live?: boolean;
  livepeer_playback_id: string;
  livepeer_stream_id: string;
  creator: {
    id: string;
    username: string;
    wallet_address: string;
    avatar_url: string;
  };
};

export default function StreamPage() {
  const router = useRouter();
  const { id, locale } = useParams<{ id: string; locale: string }>();
  const searchParams = useSearchParams();
  const action = searchParams.get('action');
  const { connected, publicKey } = useWallet();

  const [stream, setStream] = useState<Stream | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLive, setIsLive] = useState(false);
  const [videoElement, setVideoElement] = useState<HTMLVideoElement | null>(null);

  // Lazy load secondary data to improve initial navigation performance
  const [tipStats, setTipStats] = useState<{
    tipCount: number;
    totalAmount: number;
    totalCreatorAmount: number;
    currentFeePercentage: number;
  } | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const [relatedStreams, setRelatedStreams] = useState<Stream[]>([]);
  
  const liveCheckInterval = useRef<NodeJS.Timeout | null>(null);
  const videoHeartsRef = useRef<VideoFloatingHeartsRef>(null);
  const tipButtonRef = useRef<HTMLButtonElement>(null);
  const mintButtonRef = useRef<HTMLButtonElement>(null);
  
  // Function to refresh stream data (e.g., after tip or NFT actions)
  const refreshStreamData = async () => {
    if (!id) return;
    
    try {
      // Use stats parameter to avoid incrementing view count
      const response = await fetch(`/${locale}/api/streams/${id}?stats=refresh`);
      if (response.ok) {
        const data = await response.json();
        setStream(data.stream);
      }
      
      // Also refresh tip statistics
      const tipsResponse = await fetch(`/${locale}/api/tip?streamId=${id}`);
      if (tipsResponse.ok) {
        const tipsData = await tipsResponse.json();
        setTipStats(tipsData.statistics);
      }
    } catch (error) {
      console.error('Error refreshing stream data:', error);
    }
  };
  
  // Real-time viewer tracking
  const { viewerCount } = useViewerTracking({
    streamId: id,
    enabled: !!stream && isLive
  });
  
  // Prevent autoscroll on page load
  useEffect(() => {
    // Prevent default scroll restoration
    if ('scrollRestoration' in history) {
      history.scrollRestoration = 'manual';
    }
    
    // Scroll to top immediately on component mount
    window.scrollTo(0, 0);
    
    return () => {
      // Restore default scroll behavior on unmount
      if ('scrollRestoration' in history) {
        history.scrollRestoration = 'auto';
      }
    };
  }, []);
  
  // Check if we need to open the tip dialog - moved to top to follow Rules of Hooks
  useEffect(() => {
    if (action === 'tip' && stream && connected) {
      // This would be replaced with a more elegant solution using state
      const tipButton = document.getElementById('tip-button');
      if (tipButton) {
        tipButton.click();
      }
    }
  }, [action, stream, connected]);
  
  
  // Load stream data - simple and fast
  useEffect(() => {
    const fetchStream = async () => {
      if (!id) return;

      try {
        setLoading(true);

        const response = await fetch(`/${locale}/api/streams/${id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch stream');
        }

        const data = await response.json();
        setStream(data.stream);
        // Set initial live state based on API response
        setIsLive(data.stream.status === 'live' && data.stream.is_actually_live === true);

        // Set loading to false immediately
        setLoading(false);

        // Load related streams for all videos (both live and ended)
        fetch(`/${locale}/api/streams?creator=${data.stream.creator.id}&limit=4&status=all&skip_live_check=true`).then(res => {
          if (res.ok) {
            res.json().then(relatedData => {
              const filtered = relatedData.streams.filter((s: Stream) => s.id !== id);
              setRelatedStreams(filtered);
            });
          }
        }).catch(err => console.error('Error fetching related streams:', err));

        // For ended streams, skip heavy live operations
        if (data.stream.status === 'ended' || !data.stream.is_actually_live) {
          return; // Exit early - no live features needed
        }

        // Only for live streams: load additional data

        // Record view for live streams only
        fetch(`/${locale}/api/streams/${id}/view`, {
          method: 'POST'
        }).catch(err => console.error('Error recording view:', err));

        // Fetch tip statistics for live streams
        fetch(`/${locale}/api/tip?streamId=${id}`).then(res => {
          if (res.ok) {
            res.json().then(data => setTipStats(data.statistics));
          }
        }).catch(err => console.error('Error fetching tip stats:', err));

      } catch (err) {
        console.error('Error fetching stream:', err);
        setError('Failed to load stream. Please try again later.');
        setLoading(false);
      }
    };

    fetchStream();
  }, [id, locale]);

  // Ref to track live monitoring setup to prevent duplicate intervals
  const isLiveMonitoringSetup = useRef(false);
  
  // Live status monitoring - ONLY for confirmed live streams that are actually live
  useEffect(() => {
    // Skip entirely for ended streams, streams without livepeer ID, or streams not actually live
    if (!stream || stream.status === 'ended' || !stream.livepeer_stream_id || stream.is_actually_live === false) {
      return;
    }

    // Only set up live status checking for streams that should be live AND are actually live
    if (isLiveMonitoringSetup.current || stream.status !== 'live' || !stream.is_actually_live) {
      return;
    }

    // Mark that we've set up monitoring to prevent duplicate intervals
    isLiveMonitoringSetup.current = true;

    const streamId = stream.id;
    const currentLocale = locale;

    const checkStreamStatus = async () => {
      try {
        const response = await fetch(`/${currentLocale}/api/streams/${streamId}/status`);
        if (response.ok) {
          const data = await response.json();
          const newIsLive = data.isActive || false;
          setIsLive(newIsLive);
          
          // If stream goes offline, update the stream status and stop monitoring
          if (!newIsLive && stream.status === 'live') {
            setStream(prev => prev ? { ...prev, is_actually_live: false } : null);
            // Stop monitoring when stream goes offline
            if (liveCheckInterval.current) {
              clearInterval(liveCheckInterval.current);
              liveCheckInterval.current = null;
              isLiveMonitoringSetup.current = false;
            }
          }
        }
      } catch (err) {
        console.error('Error checking stream status:', err);
      }
    };

    // Check once immediately
    checkStreamStatus();
    // Set up interval only for confirmed live streams
    liveCheckInterval.current = setInterval(checkStreamStatus, 15000);

    return () => {
      if (liveCheckInterval.current) {
        clearInterval(liveCheckInterval.current);
        liveCheckInterval.current = null;
      }
      isLiveMonitoringSetup.current = false;
    };
  }, [stream, locale]);

  // Separate effect to handle stream status changes and manage live monitoring
  useEffect(() => {
    // If stream is no longer live or is confirmed not actually live, stop the monitoring
    if ((stream?.status !== 'live' || stream?.is_actually_live === false) && liveCheckInterval.current) {
      clearInterval(liveCheckInterval.current);
      liveCheckInterval.current = null;
      isLiveMonitoringSetup.current = false;
      setIsLive(false);
    }
  }, [stream?.status, stream?.is_actually_live]);

  // Separate effect to check ownership when wallet connects
  useEffect(() => {
    if (stream && connected && publicKey) {
      setIsOwner(stream.creator.wallet_address === publicKey.toString());
    }
  }, [stream, connected, publicKey]);
  
  
  // Handle share click
  const handleShare = async () => {
    try {
      const shareData = {
        title: stream?.title || 'bonkstream live video',
        text: `Watch ${stream?.creator.username || 'this creator'}'s stream on bonkstream`,
        url: window.location.href
      };
      
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback for browsers that don't support Web Share API
        navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      }
    } catch (err) {
      console.error('Error sharing:', err);
    }
  };

  // Handle ending the stream (only for stream owner)
  const [isEndingStream] = useState(false);

  // Render loading state - minimal for fast navigation
  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white">
        <div className="w-full aspect-video bg-gray-900 animate-pulse"></div>
        <div className="p-4">
          <div className="h-8 bg-gray-800 rounded animate-pulse mb-4"></div>
          <div className="h-4 bg-gray-800 rounded animate-pulse mb-2"></div>
          <div className="h-4 bg-gray-800 rounded animate-pulse w-3/4"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error || !stream) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold text-red-500 mb-4">Error</h2>
          <p className="text-white mb-6">{error || 'Stream not found'}</p>
          <button 
            onClick={() => router.back()} 
            className="px-6 py-3 bg-gray-800 hover:bg-gray-700 rounded-full text-white flex items-center gap-2"
          >
            <ArrowLeft size={18} />
            Back to Discover
          </button>
        </div>
      </div>
    );
  }


  
  return (
    <div className="min-h-screen bg-black text-white relative">
      {/* Stream ending overlay */}
      {isEndingStream && (
        <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-gray-900 border border-gray-700 rounded-lg p-8 text-center max-w-md mx-4">
            <LoadingSpinner size="lg" />
            <h3 className="text-xl font-bold mb-2 mt-4">Ending Stream...</h3>
            <p className="text-gray-400 mb-4">
              Your stream is being ended safely.
            </p>
            <p className="text-xs text-gray-500">
              You&apos;ll be redirected to your dashboard shortly.
            </p>
          </div>
        </div>
      )}
      
      {/* Video container - Mobile-first responsive like YouTube */}
      <div className="w-full flex justify-center">
        <div className="relative bg-black overflow-hidden aspect-video w-full max-w-screen-2xl">
        {/* Video player - check both status and actual live state */}
        {stream ? (
          // For ended streams OR live streams that are actually not live: show simple thumbnail
          (stream.status === 'ended' || (stream.status === 'live' && stream.is_actually_live === false)) ? (
            <div className="w-full h-full relative bg-gray-900 flex items-center justify-center">
              {stream.thumbnail_url ? (
                <Image
                  src={stream.thumbnail_url}
                  alt={stream.title}
                  fill
                  className="object-cover"
                  sizes="100vw"
                />
              ) : (
                <div className="text-gray-500">
                  <Play size={64} />
                </div>
              )}
              <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                <div className="bg-gray-800 text-white px-4 py-2 rounded-full font-semibold">
                  Stream Ended
                </div>
              </div>
            </div>
          ) : (
            // For live streams that are actually live: use full LivePlayer
            stream.livepeer_playback_id ? (
              <LivePlayer
                playbackId={stream.livepeer_playback_id}
                streamId={stream.livepeer_stream_id}
                isLive={isLive}
                title={stream.title}
                onVideoRef={setVideoElement}
                autoPlay={true}
                controls={true}
              />
            ) : (
              <video
                src={stream.playback_url || `https://livepeercdn.studio/hls/${stream.livepeer_playback_id}/index.m3u8`}
                poster={stream.thumbnail_url}
                className="w-full h-full object-cover"
                controls
                playsInline
                autoPlay
              />
            )
          )
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-black">
            <LoadingSpinner size="md" variant="simple" />
          </div>
        )}
        
        {/* Floating Hearts Overlay over video */}
        <VideoFloatingHearts ref={videoHeartsRef} />
        
        {/* Mobile-First Action Overlay (TikTok-style) - Hidden on desktop */}
        <div className="absolute inset-0 pointer-events-none lg:hidden">
          {/* Bottom-right action buttons - Mobile sticky */}
          <div className="absolute bottom-4 right-4 flex flex-col items-center gap-1 pointer-events-auto sm:bottom-6 sm:right-6">
            {/* Creator Profile */}
            <div className="flex flex-col items-center">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-gray-800 overflow-hidden border-2 border-white sm:w-12 sm:h-12 relative">
                  {stream.creator.avatar_url ? (
                    <Image
                      src={stream.creator.avatar_url}
                      alt={stream.creator.username || 'Creator'}
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 40px, 48px"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center">
                      <span className="text-black font-bold text-sm">
                        {stream.creator.username?.charAt(0).toUpperCase() || 'C'}
                      </span>
                    </div>
                  )}
                </div>
              </div>

            </div>

              {/* Tip Button - Main CTA */}
              {connected ? (
                <div className="flex flex-col items-center">
                  <button
                    onClick={() => {
                      // Find the actual TipButton by looking for the button with "Send BONK" text
                      const tipButtons = Array.from(document.querySelectorAll('button')).filter(btn =>
                        btn.textContent?.includes('Send BONK')
                      );
                      if (tipButtons.length > 0) {
                        tipButtons[0].click();
                      }
                    }}
                    className="w-10 h-10 rounded-full text-white hover:shadow-lg flex items-center justify-center p-0 text-xl sm:w-12 sm:h-12 transition-all duration-200"
                    style={{ backgroundColor: '#FF5C01' }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e5530a'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#FF5C01'}
                  >
                    <Coins className="w-4 h-4" />
                  </button>

                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <WalletButton className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 text-white hover:from-blue-500 hover:to-blue-600 flex items-center justify-center p-0 sm:w-12 sm:h-12">
                    <Wallet className="w-4 h-4" />
                  </WalletButton>

                </div>
              )}

              {/* Mint NFT Button */}
              {connected && stream && (
                <div className="flex flex-col items-center">
                  <button
                    onClick={() => {
                      // Find the actual MintNFTButton by looking for the button with "MINT NFT" text
                      const mintButtons = Array.from(document.querySelectorAll('button')).filter(btn =>
                        btn.textContent?.includes('MINT NFT')
                      );
                      if (mintButtons.length > 0) {
                        mintButtons[0].click();
                      }
                    }}
                    className="w-10 h-10 rounded-full text-white hover:shadow-lg flex items-center justify-center p-0 sm:w-12 sm:h-12 transition-all duration-200"
                    style={{ backgroundColor: '#FF5C01' }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#e5530a'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#FF5C01'}
                  >
                    <Gem className="w-4 h-4" />
                  </button>

                </div>
              )}

              {/* Like Button - Match desktop functionality */}
              <div className="flex flex-col items-center">
                <LikeButton 
                  streamId={stream?.id || ''}
                  variant="mobile"
                  showCount={false}
                  onLikeChange={refreshStreamData}
                  onHeartAnimation={() => videoHeartsRef.current?.triggerHearts()}
                />

              </div>

              {/* Share Button */}
              <div className="flex flex-col items-center">
                <button
                  onClick={handleShare}
                  className="w-10 h-10 rounded-full bg-gray-700 hover:bg-gray-600 text-white flex items-center justify-center sm:w-12 sm:h-12"
                >
                  <Share className="w-4 h-4" />
                </button>

              </div>
            </div>

            {/* Bottom-left stream info overlay - Mobile only */}
            <div className="absolute bottom-4 left-4 right-20 pointer-events-auto sm:bottom-6 sm:left-6 sm:right-24">
              <div className="bg-gradient-to-t from-black/80 to-transparent p-4 rounded-lg backdrop-blur-sm">
                <h2 className="text-lg font-bold mb-1 line-clamp-2 sm:text-xl">{stream?.title}</h2>
                <p className="text-sm text-gray-300 mb-2 line-clamp-2">{stream?.description}</p>
                
                {/* Stream stats */}
                <div className="flex items-center gap-4 text-xs text-gray-400">
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    <span>{stream?.view_count || 0}</span>
                  </div>
              <div className="flex items-center gap-1">
                <DollarSign className="w-3 h-3" />
                <span>{tipStats?.tipCount ?? stream?.tip_count ?? 0} tips</span>
              </div>
              <div className="flex items-center gap-1">
                <Heart className="w-3 h-3 text-red-400" />
                <span>{stream?.like_count || 0} likes</span>
              </div>
                  {isLive && (
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-red-400 font-medium">LIVE</span>
                      {viewerCount > 0 && <span>• {viewerCount} watching</span>}
                    </div>
                  )}
                </div>
              </div>
            </div>

          {/* Top overlay - Back button only */}
          <div className="absolute top-4 left-4 pointer-events-auto sm:top-6 sm:left-6">
            <button
              onClick={() => {
                // Use immediate navigation for better UX
                router.back();
              }}
              className="bg-black/40 rounded-full p-2 backdrop-blur-sm hover:bg-black/60 transition-colors"
            >
              <ArrowLeft size={20} className="text-white" />
            </button>
          </div>
        </div>
        </div>
      </div>
      
      {/* Stream information - Hidden on mobile when in fullscreen mode */}
      <div className="p-4 md:block">
        {/* Title and action buttons row */}
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1">
            <h1 className="text-xl font-bold md:text-2xl">{stream.title}</h1>
          </div>
          
          {/* Desktop action buttons */}
          <div className="hidden md:flex gap-2 ml-4">
            {/* Like Button */}
            <LikeButton 
              streamId={stream?.id || ''}
              size="md"
              showCount={false}
              onLikeChange={refreshStreamData}
              onHeartAnimation={() => videoHeartsRef.current?.triggerHearts()}
            />
            
            {/* Share Button */}
            <button 
              onClick={handleShare}
              className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2.5 rounded-lg flex items-center gap-2 transition-colors h-10"
            >
              <Share className="w-4 h-4" />
              Share
            </button>
            
            {/* Connected wallet buttons */}
            {connected && stream && (
              <>
                <div className="relative">
                  <TipButton 
                    streamId={stream.id}
                    creatorWalletAddress={stream.creator.wallet_address}
                    creatorName={stream.creator.username}
                    className="text-white font-bold px-4 py-2.5 rounded-lg h-10 flex items-center gap-2"
                    onSuccess={refreshStreamData}
                  />
                  {/* Hidden trigger button for mobile */}
                  <button 
                    ref={tipButtonRef}
                    className="absolute inset-0 opacity-0 pointer-events-none"
                    onClick={() => document.getElementById('tip-button')?.click()}
                  />
                </div>
                <div className="relative">
                  <MintNFTButton 
                    streamId={stream.id} 
                    creatorWalletAddress={stream.creator.wallet_address}
                    currentTime={0}
                    videoElement={videoElement || undefined} // Pass video element for frame capture
                    className="text-white font-bold px-4 py-2.5 rounded-lg h-10 flex items-center gap-2"
                    onSuccess={refreshStreamData}
                  />
                  {/* Hidden trigger button for mobile */}
                  <button 
                    ref={mintButtonRef}
                    className="absolute inset-0 opacity-0 pointer-events-none"
                    onClick={() => {
                      // Trigger the MintNFTButton's click event
                      const mintButton = document.querySelector('[class*="bg-gray-800 text-white"]') as HTMLButtonElement;
                      mintButton?.click();
                    }}
                  />
                </div>
              </>
            )}
          </div>
        </div>
        
        <p className="text-sm text-gray-400 mb-4 md:text-base">{stream.description}</p>
        
        {/* Stats */}
        <div className="flex items-center space-x-4 mb-6 flex-wrap">
          <div className="flex items-center">
            <Eye className="h-4 w-4 text-gray-500 mr-1.5" />
            <span className="text-sm text-gray-400">{stream?.view_count || 0} views</span>
          </div>
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 text-gray-500 mr-1.5" />
            <span className="text-sm text-gray-400">{tipStats?.tipCount ?? stream?.tip_count ?? 0} tips</span>
          </div>
          <div className="flex items-center">
            <Heart className="h-4 w-4 text-gray-500 mr-1.5" />
            <span className="text-sm text-gray-400">{stream?.like_count || 0} likes</span>
          </div>
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="text-sm text-gray-400">{stream?.nft_count || 0} NFTs</span>
          </div>
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-gray-400">{stream && formatDistanceToNow(new Date(stream.created_at), { addSuffix: true })}</span>
          </div>
        </div>
        
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 rounded-full bg-gray-800 overflow-hidden mr-3 md:w-12 md:h-12">
            {stream?.creator.avatar_url ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={stream.creator.avatar_url}
                alt={stream.creator.username || 'Creator'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center">
                <span className="text-black font-bold">{stream?.creator.username?.charAt(0).toUpperCase() || 'C'}</span>
              </div>
            )}
          </div>
          
          <div>
            <Link href={`/c/${stream?.creator.username}`} className="font-medium hover:underline">
              {stream?.creator.username || 'Anonymous'}
            </Link>
            <p className="text-xs text-gray-500">{stream?.creator.wallet_address.slice(0, 6)}...{stream?.creator.wallet_address.slice(-4)}</p>
          </div>
          
        </div>
        
        {/* Live Chat */}
          <LiveChat streamId={id} isCreator={isOwner} className="mt-6 mb-8" />

        {/* Related streams */}
        <h3 className="font-medium mb-3">More from {stream?.creator.username}</h3>
        {relatedStreams.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {relatedStreams.map((relatedStream) => (
              <Link
                key={relatedStream.id}
                href={`/stream/${relatedStream.id}`}
                className="bg-gray-900 rounded-lg overflow-hidden hover:bg-gray-800 transition-colors group"
              >
                <div className="aspect-video bg-gray-800 relative">
                  {relatedStream.thumbnail_url ? (
                    // eslint-disable-next-line @next/next/no-img-element
                    <img
                      src={relatedStream.thumbnail_url}
                      alt={relatedStream.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Play className="w-8 h-8 text-gray-500" />
                    </div>
                  )}
                  {relatedStream.is_actually_live && (
                    <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                      LIVE
                    </div>
                  )}
                </div>
                <div className="p-3">
                  <h4 className="font-medium text-sm mb-1 line-clamp-2">{relatedStream.title}</h4>
                  <div className="flex items-center gap-3 text-xs text-gray-400">
                    <span>{relatedStream.view_count || 0} views</span>
                    <span>{relatedStream.tip_count || 0} tips</span>
                    <span>{relatedStream.like_count || 0} likes</span>
                    <span>{formatDistanceToNow(new Date(relatedStream.created_at), { addSuffix: true })}</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="bg-gray-900 rounded-lg p-6 text-center">
            <p className="text-gray-400">No other streams from this creator yet.</p>
          </div>
        )}
      </div>
    </div>
  );
}
