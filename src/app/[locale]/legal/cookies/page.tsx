import React from 'react';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  return {
    title: `${t('cookies.title')} | bonkstream`,
    description: t('cookies.description'),
  };
}

export default async function CookiePolicyPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  
  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-8">{t('cookies.title')}</h1>
      
      <div className="prose prose-invert max-w-none">
        {/* Introduction */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.introduction.title')}</h2>
          <p>{t('cookies.sections.introduction.content')}</p>
        </section>

        {/* Types of Cookies */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.typesOfCookies.title')}</h2>
          <p className="mb-4">{t('cookies.sections.typesOfCookies.intro')}</p>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">{t('cookies.sections.typesOfCookies.items.essential.title')}</h3>
              <p>{t('cookies.sections.typesOfCookies.items.essential.content')}</p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">{t('cookies.sections.typesOfCookies.items.functional.title')}</h3>
              <p>{t('cookies.sections.typesOfCookies.items.functional.content')}</p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">{t('cookies.sections.typesOfCookies.items.analytics.title')}</h3>
              <p>{t('cookies.sections.typesOfCookies.items.analytics.content')}</p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">{t('cookies.sections.typesOfCookies.items.wallet.title')}</h3>
              <p>{t('cookies.sections.typesOfCookies.items.wallet.content')}</p>
            </div>
          </div>
        </section>

        {/* Purpose */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.purpose.title')}</h2>
          <p className="mb-4">{t('cookies.sections.purpose.intro')}</p>
          <ul className="list-disc ml-6 space-y-2">
            {Array.from({ length: 5 }, (_, i) => (
              <li key={i}>
                {t(`cookies.sections.purpose.items.${i}`)}
              </li>
            ))}
          </ul>
        </section>

        {/* Third Party */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.thirdParty.title')}</h2>
          <p>{t('cookies.sections.thirdParty.content')}</p>
        </section>

        {/* Management */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.management.title')}</h2>
          <p className="mb-4">{t('cookies.sections.management.intro')}</p>
          <ul className="list-disc ml-6 space-y-2">
            {Array.from({ length: 3 }, (_, i) => (
              <li key={i}>
                {t(`cookies.sections.management.items.${i}`)}
              </li>
            ))}
          </ul>
        </section>

        {/* Impact */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.impact.title')}</h2>
          <p>{t('cookies.sections.impact.content')}</p>
        </section>

        {/* Updates */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.updates.title')}</h2>
          <p>{t('cookies.sections.updates.content')}</p>
        </section>

        {/* Contact */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('cookies.sections.contact.title')}</h2>
          <p>{t('cookies.sections.contact.content')}</p>
        </section>
        
        <p className="mt-8 text-sm text-gray-400">
          {t('cookies.lastUpdated')}: {new Date().toLocaleDateString(locale)}
        </p>
      </div>
    </div>
  );
} 