import React from 'react';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  return {
    title: `${t('impressum.title')} | bonkstream`,
    description: t('impressum.description'),
  };
}

export default async function ImpressumPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  
  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-8">{t('impressum.title')}</h1>
      
      <div className="prose prose-invert max-w-none">
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">{t('impressum.company.name')}</h2>
          <p className="mb-2">{t('impressum.company.address')}</p>
          <p className="mb-2">{t('impressum.company.contact')}</p>
        </div>
        
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">{t('impressum.founder.title')}</h2>
          <div className="whitespace-pre-line mb-6">
            {t('impressum.founder.story')}
          </div>
          <p className="mb-2"><a href="https://futurewebservice.de">Founders Website</a></p>
        </div>
      </div>
    </div>
  );
} 