import React from 'react';
import { Link } from '@/lib/i18n';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({ }: { params: Promise<{ locale: string }> }) {
  return {
    title: `Legal | bonkstream`,
    description: 'Legal information for bonkstream',
  };
}

export default async function LegalIndexPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  
  const legalDocuments = [
    { key: 'terms', path: `/legal/terms` },
    { key: 'privacy', path: `/legal/privacy` },
    { key: 'cookies', path: `/legal/cookies` },
    { key: 'impressum', path: `/legal/impressum` },
    { key: 'platformFee', path: `/legal/platform-fee` },
  ];
  
  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-12">{t('title')}</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        {legalDocuments.map((doc) => (
          <Link
            key={doc.key}
            href={doc.path}
            locale={locale}
            className="block p-6 bg-gray-900 rounded-lg hover:bg-gray-800 transition"
          >
            <h2 className="text-xl font-semibold mb-2 text-yellow-400">
              {t(`${doc.key}.title`)}
            </h2>
            <p className="text-white/70">
              {t(`${doc.key}.description`)}
            </p>
          </Link>
        ))}
      </div>
    </div>
  );
} 