import React from 'react';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  return {
    title: `${t('terms.title')} | bonkstream`,
    description: t('terms.description'),
  };
}

export default async function TermsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  
  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-8">{t('terms.title')}</h1>
      
      <div className="prose prose-invert max-w-none">
        <h2 className="text-2xl font-semibold mt-8 mb-4">{t('terms.sections.introduction.title')}</h2>
        <p>{t('terms.sections.introduction.content')}</p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">{t('terms.sections.platformFee.title')}</h2>
        <p>{t('terms.sections.platformFee.intro')}</p>
        <ul className="list-disc ml-6 space-y-2">
          <li>
            <strong>{t('terms.sections.platformFee.items.fee.title')}:</strong> {t('terms.sections.platformFee.items.fee.content')}
          </li>
          <li>
            <strong>{t('terms.sections.platformFee.items.earnings.title')}:</strong> {t('terms.sections.platformFee.items.earnings.content')}
          </li>
          <li>
            <strong>{t('terms.sections.platformFee.items.calculation.title')}:</strong> {t('terms.sections.platformFee.items.calculation.content')}
          </li>
          <li>
            <strong>{t('terms.sections.platformFee.items.adjustments.title')}:</strong> {t('terms.sections.platformFee.items.adjustments.content')}
          </li>
        </ul>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">{t('terms.sections.wallet.title')}</h2>
        <p>{t('terms.sections.wallet.intro')}</p>
        <ul className="list-disc ml-6 space-y-2">
          <li>
            <strong>{t('terms.sections.wallet.items.supported.title')}:</strong> {t('terms.sections.wallet.items.supported.content')}
          </li>
          <li>
            <strong>{t('terms.sections.wallet.items.connection.title')}:</strong> {t('terms.sections.wallet.items.connection.content')}
          </li>
          <li>
            <strong>{t('terms.sections.wallet.items.responsibility.title')}:</strong> {t('terms.sections.wallet.items.responsibility.content')}
          </li>
          <li>
            <strong>{t('terms.sections.wallet.items.signing.title')}:</strong> {t('terms.sections.wallet.items.signing.content')}
          </li>
        </ul>
        
        {/* More sections following the same pattern */}
        
        <p className="mt-8 text-sm text-gray-400">
          {t('terms.lastUpdated')}: {new Date().toLocaleDateString(locale)}
        </p>
      </div>
    </div>
  );
} 