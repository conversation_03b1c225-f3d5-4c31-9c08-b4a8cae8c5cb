import React from 'react';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  return {
    title: `${t('platformFee.title')} | bonkstream`,
    description: t('platformFee.description'),
  };
}

export default async function PlatformFeePolicyPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  
  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-8">{t('platformFee.title')}</h1>
      
      <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-6 mb-8">
        <p className="text-lg font-semibold text-yellow-400">{t('platformFee.currentFee')}</p>
      </div>
      
      <div className="prose prose-invert max-w-none">
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('platformFee.sections.overview.title')}</h2>
          <p>{t('platformFee.sections.overview.content')}</p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('platformFee.sections.calculation.title')}</h2>
          <p>{t('platformFee.sections.calculation.content')}</p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('platformFee.sections.transparency.title')}</h2>
          <p>{t('platformFee.sections.transparency.content')}</p>
        </section>
        
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('platformFee.sections.history.title')}</h2>
          <p>{t('platformFee.sections.history.content')}</p>
        </section>
      </div>
    </div>
  );
} 