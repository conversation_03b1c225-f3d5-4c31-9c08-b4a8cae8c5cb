import React from 'react';
import { getTranslations } from 'next-intl/server';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  return {
    title: `${t('privacy.title')} | bonkstream`,
    description: t('privacy.description'),
  };
}

export default async function PrivacyPolicyPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });
  
  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-8">{t('privacy.title')}</h1>
      
      <div className="prose prose-invert max-w-none">
        {/* Introduction */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.introduction.title')}</h2>
          <p>{t('privacy.sections.introduction.content')}</p>
        </section>

        {/* Data Collection */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.dataCollection.title')}</h2>
          <p className="mb-4">{t('privacy.sections.dataCollection.intro')}</p>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">{t('privacy.sections.dataCollection.items.wallet.title')}</h3>
              <p>{t('privacy.sections.dataCollection.items.wallet.content')}</p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">{t('privacy.sections.dataCollection.items.profile.title')}</h3>
              <p>{t('privacy.sections.dataCollection.items.profile.content')}</p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">{t('privacy.sections.dataCollection.items.usage.title')}</h3>
              <p>{t('privacy.sections.dataCollection.items.usage.content')}</p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-2">{t('privacy.sections.dataCollection.items.technical.title')}</h3>
              <p>{t('privacy.sections.dataCollection.items.technical.content')}</p>
            </div>
          </div>
        </section>

        {/* Data Use */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.dataUse.title')}</h2>
          <p className="mb-4">{t('privacy.sections.dataUse.intro')}</p>
          <ul className="list-disc ml-6 space-y-2">
            {Array.from({ length: 5 }, (_, i) => (
              <li key={i}>
                {t(`privacy.sections.dataUse.items.${i}`)}
              </li>
            ))}
          </ul>
        </section>

        {/* Data Sharing */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.dataSharing.title')}</h2>
          <p>{t('privacy.sections.dataSharing.content')}</p>
        </section>

        {/* User Rights */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.rights.title')}</h2>
          <p className="mb-4">{t('privacy.sections.rights.intro')}</p>
          <ul className="list-disc ml-6 space-y-2">
            {Array.from({ length: 5 }, (_, i) => (
              <li key={i}>
                {t(`privacy.sections.rights.items.${i}`)}
              </li>
            ))}
          </ul>
        </section>

        {/* Security */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.security.title')}</h2>
          <p>{t('privacy.sections.security.content')}</p>
        </section>

        {/* Data Retention */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.retention.title')}</h2>
          <p>{t('privacy.sections.retention.content')}</p>
        </section>

        {/* Contact */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">{t('privacy.sections.contact.title')}</h2>
          <p>{t('privacy.sections.contact.content')}</p>
        </section>
        
        <p className="mt-8 text-sm text-gray-400">
          {t('privacy.lastUpdated')}: {new Date().toLocaleDateString(locale)}
        </p>
      </div>
    </div>
  );
} 