{"dashboard": {"title": "Creator Dashboard", "subtitle": "Manage your content and track your earnings", "connectWallet": {"title": "Connect Your Wallet", "description": "Connect your wallet to access the creator dashboard"}, "loading": "Loading dashboard...", "errors": {"loadProfileFailed": "Failed to load profile", "loadCreatorDataFailed": "Failed to load creator data", "loadDashboardFailed": "Failed to load dashboard", "onboardingFailed": "Failed to complete setup"}, "nav": {"overview": "Overview", "content": "Content", "analytics": "Analytics", "tips": "Tips", "settings": "Settings", "viewPublicPage": "View Public Page"}, "timeRanges": {"day": "24 Hours", "week": "7 Days", "month": "30 Days", "year": "Year", "all": "All Time"}, "stats": {"totalEarnings": "Total Earnings", "tipCount": "Number of Tips", "averageTip": "Average Tip", "viewCount": "Stream Views", "nftsMinted": "NFTs Minted"}, "tips": {"title": "Tips & Earnings", "description": "View your tip history and earnings", "recentTips": "Recent Tips", "noTips": {"title": "No T<PERSON><PERSON> Received Yet", "description": "Start streaming and sharing your content to receive your first BONK tips from viewers."}, "noTipsDescription": "You haven't received any tips yet. Share your streams to start earning!", "tipAmount": "Amount", "tipDate": "Date", "tipFrom": "From", "tipStream": "Stream", "platformFee": "Platform Fee", "yourEarnings": "Your Earnings", "totalReceived": "Total Received", "viewTransaction": "View Transaction", "table": {"date": "Date", "tipper": "<PERSON><PERSON><PERSON>", "stream": "Stream", "amountNet": "Amount (Net)", "platformFee": "Platform Fee", "explorer": "Explorer", "viewOnExplorerTitle": "View transaction on Solana Explorer"}}, "content": {"title": "Content Management", "streams": "Your Streams", "noStreams": {"title": "No Streams Yet", "description": "You haven't created any streams yet. Start your first stream to begin building your audience and earning BONK tips."}, "noStreamsDescription": "You haven't created any streams yet. Start your first stream to begin building your audience.", "startStreamPlaceholder": "Stream creation coming soon!", "streamStatus": "Status", "streamViews": "Views", "streamDate": "Date", "streamDuration": "Duration", "filter": {"label": "Filter Streams", "statusTitle": "Filter by Status", "all": "All Streams", "live": "Live", "recorded": "Recorded", "archived": "Archived"}, "table": {"thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "title": "Title", "status": "Status", "views": "Views", "duration": "Duration", "createdDate": "Created", "actions": "Actions", "manageButton": "Manage"}, "actions": {"edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure?", "deleteWarning": "This action cannot be undone. This will permanently delete the stream and remove all associated data including tips and NFTs.", "cancel": "Cancel", "deleting": "Deleting...", "deleteStream": "Delete Stream"}}, "analytics": {"title": "Analytics", "description": "Track your performance and growth", "viewsOverTime": "Views Over Time", "earningsOverTime": "Earnings Over Time", "topStreams": "Top Performing Streams", "audienceGrowth": "Audience Growth", "comingSoon": "Detailed analytics coming soon", "placeholder": "Analytics dashboard will show your stream performance, earnings trends, and audience insights.", "stats": {"totalViews": "Total Views", "uniqueViewers": "Unique Viewers", "avgWatchTime": "Avg. Watch Time"}, "charts": {"earningsTrendTitle": "Earnings Trend", "earningsTrendDescription": "Your BONK earnings over time", "placeholder": "Chart data will appear here once you have streams and earnings", "dataUnavailable": "No data available for the selected period"}, "highlights": {"topStreamTitle": "Top Performing Stream", "noTopStream": "No streams available"}}, "settings": {"title": "Profile Settings", "description": "Update your profile information and preferences", "sections": {"profileInfo": {"title": "Profile Information", "description": "Update your public profile details"}, "socialLinks": {"title": "Social Links", "description": "Add your social media links"}}, "fields": {"displayName": {"label": "Display Name", "placeholder": "Enter your display name", "hint": "This is your public username that viewers will see"}, "avatarUrl": {"label": "Avatar URL", "placeholder": "https://example.com/avatar.jpg"}, "bio": {"label": "Bio", "placeholder": "Tell your audience about yourself...", "hint": "Describe yourself and what you stream about"}}, "saveChangesButton": "Save Changes", "errors": {"notConnectedOrNoProfile": "Wallet not connected or profile not found", "displayNameRequired": "Display name is required", "displayNameMinLength": "Display name must be at least 3 characters", "avatarUrlInvalid": "Avatar URL must start with http:// or https://", "updateFailed": "Failed to update profile"}, "success": {"profileUpdated": "Profile updated successfully!"}}, "overview": {"title": "Dashboard Overview", "welcome": "Welcome back", "totalEarnings": "Total Earnings", "totalTips": "Total Tips", "totalStreams": "Total Streams", "totalNFTs": "NFTs Minted", "recentActivity": {"title": "Recent Activity", "description": "Your latest tips, streams, and NFT mints", "noRecentActivity": "No recent activity to display", "viewAll": "View All Activity", "tipReceived": "Tip received from {user}"}, "activeStream": {"noActiveStreamTitle": "No Active Stream", "noActiveStreamDescription": "You're not currently live. Start a new stream to connect with your audience.", "title": "Active Stream", "status": "Status", "views": "Views", "manageButton": "Manage Stream"}, "getStarted": "Get Started", "startFirstStream": "Start your first stream", "createContent": "Create amazing content and earn BONK tips from your audience", "goLive": "Go Live Now", "stats": {"totalEarnings": "Total Earnings", "totalEarningsDescription": "Total BONK earned from tips and donations", "totalTips": "Total Tips", "totalTipsDescription": "Number of tips received from viewers", "totalStreams": "Total Streams", "totalStreamsDescription": "Number of streams you've created", "nftsMinted": "NFTs Minted", "nftsMintedDescription": "Number of NFT moments minted from your streams"}, "tips": {"title": "Tips Overview", "table": {"date": "Date", "tipper": "<PERSON><PERSON><PERSON>", "stream": "Stream", "amountNet": "Amount (Net)", "platformFee": "Platform Fee", "explorer": "Explorer", "viewOnExplorerTitle": "View transaction on Solana Explorer"}, "noTips": {"title": "No Tips Yet", "description": "You haven't received any tips yet. Share your streams to start earning BONK!"}}}, "unexpectedError": "An unexpected error occurred"}, "onboarding": {"title": "Become a Creator", "subtitle": "Start your creator journey on bonkstream", "steps": {"profile": "Profile", "terms": "Terms", "complete": "Complete"}, "profileStep": {"title": "Set up your creator profile", "username": "Creator username", "bio": "Bio", "bioPlaceholder": "Tell viewers about yourself", "avatarUpload": "Upload profile picture", "next": "Next"}, "termsStep": {"title": "Accept creator terms", "agreement": "I agree to the creator terms and conditions", "platformFeeInfo": "bonkstream takes a small {fee}% platform fee on tips", "readTerms": "Read full terms", "back": "Back", "submit": "Submit"}, "completeStep": {"title": "You're now a creator!", "message": "Your creator profile has been set up successfully", "goToDashboard": "Go to Dashboard"}, "step1": {"title": "Become a Creator", "description": "Join the bonkstream creator community", "subDescription": "Set up your profile and start earning from your content", "cta": "Get Started"}, "step2": {"title": "Complete Your Profile", "displayNameLabel": "Display Name", "displayNamePlaceholder": "Choose your creator name", "displayNameHint": "This will be your public username", "acceptTermsLabel": "I agree to the Terms of Service", "termsLink": "Terms of Service", "contentGuidelinesLink": "Content Guidelines", "acceptFeesLabel": "I understand the fee structure", "feesDescriptionPart1": "Platform fee", "feePolicyLink": "Fee Policy", "cta": "Complete Setup"}, "errors": {"displayNameRequired": "Display name is required", "displayNameMinLength": "Display name must be at least 3 characters", "termsRequired": "You must accept the terms of service", "feesRequired": "You must acknowledge the fee structure"}}, "verifyingAccess": "Verifying wallet...", "signingMessage": "Please sign the authentication message", "checkWalletPrompt": "Please check your wallet for the signature request. This is required to access the creator dashboard.", "accessDenied": "Access Denied", "walletRequired": "You need to connect your wallet to access the creator dashboard", "returnHome": "Return to Home"}