{"title": "My Collection", "description": "View and manage your NFT moments from bonkstream", "your_nft_moments": "Your NFT Moments", "connectWallet": "Connect Wallet", "loading": "Loading your collection...", "error": "Error loading collection", "retry": "Try Again", "empty": "No NFT moments found", "sortBy": "Sort By", "dateMinted": "Date Minted", "streamTitle": "Stream Title", "mintDate": "Mint Date", "mintAddress": "Mint Address", "details": "Details", "transactionInformation": "Transaction Information", "transactionSignature": "Transaction Signature", "copy": "Copy", "watchStream": "Watch Stream", "viewOnExplorer": "View on Explorer", "loadMore": "Load More NFTs", "signing_message": "Signing Message", "check_wallet_prompt": "Please check your wallet and sign the authentication message to continue.", "authentication_required": "Authentication Required", "authentication_description": "You need to authenticate with your wallet to view your NFT collection.", "try_again": "Try Again", "filterBy": "Filter by", "dateCreated": "Date Created", "creatorName": "Creator Name", "viewDetails": "View Details", "nftDetails": {"title": "NFT Details", "creator": "Creator", "stream": "Stream", "timestamp": "Timestamp", "mintDate": "Mint Date", "tokenAddress": "Token Address", "platformFee": "Platform Fee at Mint", "transactionId": "Transaction ID", "viewOnExplorer": "View on Explorer", "close": "Close"}, "filters": {"all": "All NFTs", "recent": "Recently Minted", "favorites": "Favorites"}, "sorting": {"newest": "Newest First", "oldest": "Oldest First", "creatorAZ": "Creator (A-Z)", "creatorZA": "Creator (Z-A)", "streamAZ": "Stream (A-Z)", "streamZA": "Stream (Z-A)"}}