{"title": {"text": "Admin Dashboard"}, "nav": {"dashboard": "Dashboard", "users": "Users", "platformFee": "Platform Fee", "storage": "Storage", "analytics": "Analytics", "audit": "<PERSON><PERSON>", "settings": "Settings"}, "overview": "Dashboard Overview", "dashboard": {"title": "Dashboard Overview", "totalUsers": "Total Users", "activeUsers": "Active Users", "totalRevenue": "Total Revenue", "platformFees": "Platform Fees", "totalTips": "Total Tips", "totalNFTs": "Total NFTs", "storageUsed": "Storage Used"}, "platformFee": {"text": "Platform Fee", "title": "Platform Fee Settings", "currentFee": "Current Fee: {{fee}}%", "changeHistory": "Fee Change History", "updateFee": "Update Fee", "feeRange": "Fee range (2-5%)", "updateButton": "Update Fee", "confirmChange": "Confirm Fee Change", "justification": "Justification", "description": "The platform fee is applied to all BONK tips processed through the platform.", "changeSuccess": "Fee updated successfully"}, "users": {"text": "Users", "search": "Search Users", "role": "Role", "status": "Status", "walletAddress": "Wallet Address", "joined": "Joined", "lastActive": "Last Active", "totalTips": "Total Tips", "actions": "Actions", "viewProfile": "View Profile", "editUser": "Edit User", "creator": "Creator", "viewer": "Viewer", "admin": "Admin"}, "analytics": {"text": "Analytics", "overview": "Analytics Overview", "timeRange": "Time Range", "revenue": "Revenue", "users": "Users", "content": "Content", "tips": "Tips", "nfts": "NFTs", "day": "24 Hours", "week": "7 Days", "month": "30 Days", "year": "Year"}, "settings": {"text": "Settings", "general": "General Settings", "security": "Security Settings", "integration": "Integrations", "saveChanges": "Save Changes"}, "storage": {"text": "Storage", "overview": "Storage Overview", "provider": "Provider", "streams": "Streams", "size": "Size", "retention": "Retention", "archive": "Archive", "status": "Status", "totalStorage": "Total Storage Used", "streamStorage": "Stream Storage", "metadataStorage": "Metadata Storage", "ipfsStorage": "IPFS Storage"}, "audit": {"text": "<PERSON><PERSON>", "action": "Action", "user": "User", "timestamp": "Timestamp", "details": "Details", "ip": "IP Address", "filterByAction": "Filter by Action", "filterByUser": "Filter by User"}, "verifyingAccess": {"text": "Verifying admin access..."}, "backToSite": {"text": "Back to Site"}, "accessDenied": {"text": "Access Denied"}, "noAdminRights": {"text": "You do not have admin privileges"}, "returnHome": {"text": "Return to Home"}, "refresh": {"text": "Refresh"}}