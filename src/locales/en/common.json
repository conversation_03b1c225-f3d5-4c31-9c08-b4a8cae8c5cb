{"nav": {"home": "Home", "streams": "Streams", "discover": "Discover", "collection": "Collection", "profile": "Profile", "dashboard": "Dashboard", "admin": "Admin", "connect_wallet": "Connect", "stream_now": "Stream Now", "go_live": "Go Live", "manage_stream": "Manage Stream", "live": "LIVE"}, "auth": {"connect_wallet": "Connect", "disconnect": "Disconnect", "switch_wallet": "Switch Wallet"}, "stream": {"watch": "Watch", "tip": "Tip Creator", "mint": "Mint Moment", "views": "{{count}} views", "creator": "Created by {{name}}", "tip_success": "Thank you for your support!", "mint_success": "Moment minted successfully!", "chat": {"liveChat": "Live Chat", "loading": "Loading chat...", "connecting": "Connecting to chat...", "noMessages": "No messages yet. Start the conversation!", "connectWalletToChat": "Connect your wallet to participate in chat", "connectWallet": "Connect Wallet", "cannotSendMessages": "Unable to send messages at this time", "typeMessage": "Type a message...", "addEmote": "Add emote", "deleteMessage": "Delete message", "bonkEmotes": "BONK Emotes", "categories": {"faces": "Faces", "gestures": "Gestures", "symbols": "Symbols", "activities": "Activities"}}}, "tipping": {"amount": "Amount", "platform_fee": "Platform Fee ({{percentage}}%)", "creator_receives": "Creator Receives", "total": "Total", "confirm": "Confirm Tip", "cancel": "Cancel"}, "profile": {"edit": "Edit Profile", "transactions": "Transactions", "nfts": "NFT Collection", "settings": "Settings", "save": "Save Changes", "become_creator": "Become a Creator"}, "creator": {"earnings": "Earnings", "tips": "Tips", "streams": "Streams", "followers": "Followers", "new_stream": "New Stream"}, "admin": {"platform_fee": "Platform Fee", "users": "Users", "storage": "Storage", "analytics": "Analytics", "settings": "Settings", "audit": "<PERSON><PERSON>"}, "common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "success": "Success!", "cancel": "Cancel", "save": "Save", "delete": "Delete", "confirm": "Confirm", "dark_mode": "Dark Mode", "light_mode": "Light Mode", "language": "Language", "anonymousUser": "Anonymous", "discoverStreams": "Discover Streams", "profile": "Profile", "become_creator": "Become a Creator", "creator_description": "Join our platform as a creator and start earning from your streams", "wallet_required": "Wallet Required", "username": "Username", "bio": "Bio", "wallet_address": "Wallet Address", "wallet_address_note": "This will be your payment address for tips", "submit_application": "Submit Application"}, "footer": {"privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>", "impressum": "Impressum", "browseStreams": "Browse Streams", "platformFee": "Platform Fee (5%)"}, "nft": {"collection": "Your Collection", "details": "NFT Details", "timestamp": "Timestamp", "view_explorer": "View on Explorer", "mint_date": "Mint Date", "no_nfts": "No NFTs in your collection yet"}, "settings": {"appearance": "Appearance", "language": "Language", "notifications": "Notifications"}, "languages": {"en": "English", "de": "German (Deutsch)"}, "security": {"verify": "Verify with Wallet", "signing": "Signing...", "verification_required": "Additional Verification Required", "sign_message": "Please sign the message with your wallet"}, "errors": {"wallet_required": "Wallet connection required", "insufficient_balance": "Insufficient BONK balance", "transaction_failed": "Transaction failed", "unauthorized": "Unauthorized access", "load_failed": "Failed to load data", "update_failed": "Failed to update data", "generic": "An error occurred", "retry": "Please try again later", "submission_failed": "Failed to submit the form", "stream_load_failed": "Failed to load stream", "collection_load_failed": "Failed to load your NFT collection", "profile_load_failed": "Failed to load profile", "admin_verification_failed": "Failed to verify admin access", "dashboard_load_failed": "Failed to load dashboard data", "fee_data_failed": "Failed to load platform fee data", "fee_range": "Fee must be between 2% and 5%", "justification_required": "Please provide a detailed justification", "fee_update_failed": "Failed to update platform fee", "settings_update_failed": "Failed to update settings", "creator_registration_failed": "Failed to register as creator", "stream_creation_failed": "Failed to create stream"}, "cookies": {"preferences": "Cookie Preferences", "enhance_experience": "We use cookies to enhance your experience on our platform. You can choose which cookies you allow us to use.", "policy_link": "<PERSON><PERSON>", "always_on": "Always On", "essential": {"title": "Essential Cookies", "subtitle": "Required for the platform to function", "description": "These cookies are necessary for the website to function properly and cannot be switched off. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in, or filling in forms."}, "preferences_category": {"title": "Preference Cookies", "subtitle": "Remember your settings and preferences", "description": "These cookies allow us to remember choices you make and provide enhanced functionality. They may be set by us or by third party providers whose services we have added to our pages. If you do not allow these cookies, some or all of these features may not work properly."}, "analytics": {"title": "Analytics Cookies", "subtitle": "Help us improve our platform", "description": "These cookies collect information about how you use our platform, which pages you visited, and any errors you might have encountered. These cookies do not collect any information that could identify you directly – all the information is anonymous and used solely to improve how our platform works."}, "reject_all": "Reject All", "accept_all": "Accept All", "save_preferences": "Save Preferences", "settings": "<PERSON><PERSON>", "value_privacy": "We Value Your Privacy", "banner_description": "We use cookies to enhance your experience. Required cookies are always active, but you can choose which optional cookies to accept. Learn more in our", "customize": "Customize"}, "tip": "Tip", "hero_description": "A Web3-native streaming platform with direct creator monetization through BONK tokens and NFT moments.", "start_watching": "Start Watching", "popular_streams": "Popular Streams", "viewers": "viewers", "why_bonkstream": "Why bonkstream?", "feature_1_title": "STREAM", "feature_1_desc": "Stream live content in HD quality with real-time chat and engagement", "feature_2_title": "BONK", "feature_2_desc": "Earn BONK instantly while streaming - transparent 5% fee, maximum creator profits", "feature_3_title": "MINT", "feature_3_desc": "Capture epic frames from livestreams and save them to the blockchain forever", "how_it_works": "How it works", "step_1_title": "Connect your wallet", "step_1_desc": "Link your Phantom or Backpack wallet securely", "step_2_title": "Watch streams", "step_2_desc": "Discover and enjoy crypto-focused content", "step_3_title": "Tip with BONK", "step_3_desc": "Support creators directly with BONK tokens", "step_4_title": "Mint moments", "step_4_desc": "Create NFTs from your favorite stream moments", "platform_features": "Platform Features", "features": {"transparent_payments": "Transparent Payments", "transparent_payments_desc": "All transactions are processed transparently on the blockchain", "direct_monetization": "Direct Creator Monetization", "direct_monetization_desc": "Full earnings go to creators - no middleman taking percentages from tips", "no_ads": "No Advertisements", "no_ads_desc": "Enjoy content without interruptions or advertising", "no_moderation": "No Moderation", "no_moderation_desc": "Uncensored platform with freedom of expression", "anonymous_streaming": "Anonymous Streaming", "anonymous_streaming_desc": "Start streaming instantly with just a wallet - no complicated registration required", "no_manipulation": "No View Manipulation", "no_manipulation_desc": "Authentic view counts without algorithmic manipulation", "instant_payouts": "Instant Payouts", "instant_payouts_desc": "Immediate transfer of earnings to your wallet - no waiting periods", "fresh_content": "Fresh Content Only", "fresh_content_desc": "Only the most current and up-to-date content"}, "app_features": {"current": "**LIVE NOW:** Stream in HD quality • **CHAT** with real-time messaging • **SHARE** streams instantly • **LIKE** your favorite content • **DONATE** BONK tokens directly • **MINT NFTs** from epic video moments • **ANALYTICS** dashboard for creators", "coming_soon": "**COMING SOON:** Subscribe to creators for exclusive content • **DAILY BONK REWARDS** for streaming • **CUSTOM NFT PRICING** for creators • **RECORDED STREAMS** library • **PREMIUM FEATURES** for subscribers • **MO<PERSON>LE APP** for iOS & Android", "interactions": "**FREE** to start streaming • **5% platform fee** on tips (transparent & fair) • **NO HIDDEN COSTS** • **INSTANT PAYOUTS** to your wallet"}, "creatorPage": {"creator": "Creator", "live": "LIVE", "anonymous": "Anonymous", "copied": "Copied!", "share": "Share", "subscribe": "Subscribe", "comingSoon": "Coming soon", "connectWalletToTip": "Connect Wallet to Send BONK", "viewOnExplorer": "View on Solana Explorer", "totalAmount": "Total: {amount} BONK", "joined": "Joined {date}", "aboutUsername": "About {username}", "streamMoment": "Stream Moment #{timestamp}", "minted": "Minted {time}", "streams": "Streams", "views": "Views", "likes": "<PERSON>s", "bonk": "BONK", "bonkEarned": "BONK Earned", "tips": "Tips", "nfts": "NFTs", "totalStreams": "Total Streams", "totalViews": "Total Views", "totalLikes": "Total Likes", "tipsReceived": "<PERSON><PERSON><PERSON> Received", "nftsMinted": "NFTs Minted", "streamsWithCount": "Streams ({count})", "tipsWithCount": "Tips ({count})", "nftsWithCount": "NFTs ({count})", "about": "About", "recentTips": "Recent Tips", "tipSummary": "<PERSON><PERSON>", "totalTips": "Total Tips", "totalBonk": "Total BONK", "recentNftMints": "Recent NFT Mints", "nftCollection": "NFT Collection", "totalNfts": "Total NFTs", "statistics": "Statistics", "walletAddress": "Wallet Address", "memberSince": "Member Since", "avgStreamDuration": "Average Stream Duration", "backToDiscover": "Back to Discover", "followCreator": "Follow", "today": "Today", "yesterday": "Yesterday", "daysAgo": "{days} days ago", "weeksAgo": "{weeks} weeks ago", "monthsAgo": "{months} months ago", "yearsAgo": "{years} years ago", "noStreamsMessage": "This creator hasn't streamed any content yet", "noTipsMessage": "No tips received yet", "noNftsMessage": "No NFTs minted yet", "noBioMessage": "No bio available", "fetchFailed": "Could not load creator information", "creatorNotFound": "Creator not found", "sections": {"recentTips": "Recent Tips", "tipSummary": "<PERSON><PERSON>", "totalTips": "Total Tips", "totalBonk": "Total BONK", "recentNftMints": "Recent NFT Mints", "nftCollection": "NFT Collection", "totalNfts": "Total NFTs", "statistics": "Statistics", "walletAddress": "Wallet Address", "memberSince": "Member Since"}, "stats": {"totalStreams": "Total Streams", "totalViews": "Total Views", "totalLikes": "Total Likes", "bonkEarned": "BONK Earned", "tipsReceived": "<PERSON><PERSON><PERSON> Received", "nftsMinted": "NFTs Minted"}, "labels": {"live": "LIVE", "about": "About {username}", "anonymous": "Anonymous", "totalAmount": "Total: {amount} BONK", "viewOnExplorer": "View on Solana Explorer", "streamMoment": "Stream Moment #{timestamp}", "minted": "Minted {time}"}}, "cta": {"join_revolution": "Join the BONK Revolution", "start_streaming": "Start streaming, tipping, and minting NFT moments with BONKSTREAM!"}, "about": {"title": "ABOUT BONK", "description": "BONK is the social layer and community meme coin of Solana in three programmatic as a utility token across a wide range of applications and protocols while the world&apos;s ecosystem. As the community token is direct users to be completely airdropped to the Solana community and entirely ecosystem of integrations.", "holders": "HOLDERS", "integrations": "INTEGRATIONS", "chains": "CHAINS"}}