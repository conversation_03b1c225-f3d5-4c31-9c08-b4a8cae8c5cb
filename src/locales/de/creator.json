{"dashboard": {"title": "Creator Dashboard", "subtitle": "Verwalte deine Inhalte und verfolge deine Einnahmen", "connectWallet": {"title": "Verbinde dein Wallet", "description": "Verbinde dein Wallet, um auf das Creator Dashboard zuzugreifen"}, "loading": "Dashboard wird geladen...", "errors": {"loadProfileFailed": "<PERSON>il konnte nicht geladen werden", "loadCreatorDataFailed": "Creator-<PERSON><PERSON> konnten nicht geladen werden", "loadDashboardFailed": "Dashboard konnte nicht geladen werden", "onboardingFailed": "Setup konnte nicht abgeschlossen werden"}, "nav": {"overview": "Übersicht", "content": "Inhalte", "analytics": "<PERSON><PERSON><PERSON>", "tips": "<PERSON><PERSON><PERSON><PERSON>", "settings": "Einstellungen", "viewPublicPage": "Öffentliche Seite anzeigen", "profileSettings": "Profil-Einstellungen"}, "timeRanges": {"day": "24 Stunden", "week": "7 Tage", "month": "30 Tage", "year": "<PERSON><PERSON><PERSON>", "all": "Gesamte Zeit"}, "stats": {"totalEarnings": "Gesamteinnahmen", "tipCount": "Anzahl der Trinkgelder", "averageTip": "Durchschnittliches Trinkgeld", "viewCount": "Stream-Aufrufe", "nftsMinted": "Geprägte NFTs"}, "tips": {"title": "Trinkgelder & Einnahmen", "description": "Sieh dir deine Trinkgeld-Historie und Einnahmen an", "recentTips": "Kürz<PERSON> Trinkgelder", "noTips": {"title": "Noch keine Trinkgelder erhalten", "description": "Beginne zu streamen und teile deine Inhalte, um deine ersten BONK-<PERSON>nk<PERSON><PERSON> von <PERSON> zu erhalten."}, "noTipsDescription": "Du hast noch keine Trinkgelder erhalten. <PERSON><PERSON> deine Streams, um zu verdienen!", "tipAmount": "Betrag", "tipDate": "Datum", "tipFrom": "<PERSON>", "tipStream": "Stream", "platformFee": "Plattform-Gebühr", "yourEarnings": "<PERSON><PERSON>", "totalReceived": "Insgesamt erhalten", "viewTransaction": "Transaktion anzeigen", "table": {"date": "Datum", "tipper": "<PERSON>pender", "stream": "Stream", "amountNet": "Betrag (Netto)", "platformFee": "Plattform-Gebühr", "explorer": "Explorer", "viewOnExplorerTitle": "Transaktion im Solana Explorer anzeigen"}}, "content": {"title": "Inhaltsverwaltung", "streams": "Deine Streams", "noStreams": {"title": "Noch keine Streams", "description": "Du hast noch keine Streams erstellt. Starte deinen ersten Stream, um dein Publikum aufzubauen und BONK-Trinkgelder zu verdienen."}, "noStreamsDescription": "Du hast noch keine Streams erstellt. Starte deinen ersten Stream, um dein Publikum aufzubauen.", "startStreamPlaceholder": "Stream-Erstellung kommt bald!", "streamStatus": "Status", "streamViews": "Auf<PERSON><PERSON>", "streamDate": "Datum", "streamDuration": "<PERSON><PERSON>", "filter": {"label": "Streams filtern", "statusTitle": "Nach Status filtern", "all": "Alle Streams", "live": "Live", "recorded": "Aufgezeichnet", "archived": "<PERSON><PERSON><PERSON><PERSON>"}, "table": {"thumbnail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Titel", "status": "Status", "views": "Auf<PERSON><PERSON>", "duration": "<PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Aktionen", "manageButton": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "confirmDelete": "Sind Sie sicher?", "deleteWarning": "Diese Aktion kann nicht rückgängig gemacht werden. Der Stream wird dauerhaft gelöscht und alle zugehörigen Daten einschließlich Trinkgelder und NFTs werden entfernt.", "cancel": "Abbrechen", "deleting": "Wird gel<PERSON>t...", "deleteStream": "Stream löschen"}}, "analytics": {"title": "<PERSON><PERSON><PERSON>", "description": "Verfolge deine Leistung und dein Wachstum", "viewsOverTime": "Aufrufe über Zeit", "earningsOverTime": "Einnahmen über Zeit", "topStreams": "Beste Streams", "audienceGrowth": "Publikumswachstum", "comingSoon": "Detaillierte <PERSON> kommen bald", "placeholder": "Das Analyse-Dashboard zeigt deine Stream-Leistung, Einnahmentrends und Publikumseinblicke.", "stats": {"totalViews": "Gesamte Aufrufe", "uniqueViewers": "Einzigartige Zuschauer", "avgWatchTime": "Durchschn. Zuschauzeit"}, "charts": {"earningsTrendTitle": "Einnahmentrend", "earningsTrendDescription": "Deine BONK-Einnahmen über Zeit", "placeholder": "Diagramm<PERSON>n er<PERSON><PERSON> hier, sobald du <PERSON>s und Einnahmen hast", "dataUnavailable": "<PERSON><PERSON> Daten für den ausgewählten Zeitraum verfügbar"}, "highlights": {"topStreamTitle": "Bester Stream", "noTopStream": "Keine Streams verfügbar"}}, "settings": {"title": "Profil-Einstellungen", "description": "Aktualisiere deine Profilinformationen und Einstellungen", "sections": {"profileInfo": {"title": "Profilinformationen", "description": "Aktualisiere deine öffentlichen Profildetails"}, "socialLinks": {"title": "Social Media Links", "description": "Füge deine Social Media Links hinzu"}}, "fields": {"displayName": {"label": "Anzeigename", "placeholder": "Gib deinen Anzeigenamen ein", "hint": "Dies ist dein öffentlicher Benutzername, den die Zuschauer sehen"}, "avatarUrl": {"label": "Avatar URL", "placeholder": "https://example.com/avatar.jpg"}, "bio": {"label": "Bio", "placeholder": "<PERSON><PERSON><PERSON><PERSON> deinem Publikum von dir...", "hint": "Beschreibe dich und worüber du streamst"}}, "saveChangesButton": "Änderungen speichern", "errors": {"notConnectedOrNoProfile": "Wallet nicht verbunden oder Profil nicht gefunden", "displayNameRequired": "Anzeigename ist erforderlich", "displayNameMinLength": "Anzeigename muss mindestens 3 Zeichen lang sein", "avatarUrlInvalid": "Avatar URL muss mit http:// oder https:// beginnen", "updateFailed": "Profil-Update fehlgeschlagen"}, "success": {"profileUpdated": "Profil erfolgreich aktualisiert!"}}, "overview": {"title": "Dashboard Übersicht", "welcome": "Willkommen zurück", "totalEarnings": "Gesamteinnahmen", "totalTips": "Gesamte Trinkgelder", "totalStreams": "Gesamte Streams", "totalNFTs": "Geprägte NFTs", "recentActivity": {"title": "Kürzliche Aktivität", "description": "<PERSON>ine neuesten Trinkgelder, Streams und NFT-Prägungen", "noRecentActivity": "Keine kürzliche Aktivität anzuzeigen", "viewAll": "Alle Aktivitäten anzeigen", "tipReceived": "Trinkgeld erhalten von {user}"}, "getStarted": "<PERSON><PERSON><PERSON>", "startFirstStream": "Starte deinen ersten Stream", "createContent": "<PERSON><PERSON><PERSON> großartigen Inhalt und verdiene BONK-Trinkgelder von deinem Publikum", "goLive": "Jetzt Live gehen", "stats": {"totalEarnings": "Gesamteinnahmen", "totalEarningsDescription": "Gesamt verdiente BONK aus Trinkgeldern und Spenden", "totalTips": "Gesamte Trinkgelder", "totalTipsDescription": "<PERSON><PERSON><PERSON> <PERSON> von Zuschauern erhaltenen Trinkgelder", "totalStreams": "Gesamte Streams", "totalStreamsDescription": "<PERSON><PERSON><PERSON> der von dir erstellten Streams", "nftsMinted": "Geprägte NFTs", "nftsMintedDescription": "An<PERSON><PERSON> der von deinen Streams geprägten NFT-Momente"}, "activeStream": {"noActiveStreamTitle": "Kein aktiver <PERSON>", "noActiveStreamDescription": "Du bist derzeit nicht live. Starte einen neuen Stream, um dich mit deinem Publikum zu verbinden.", "title": "Aktiver Stream", "status": "Status", "views": "Auf<PERSON><PERSON>", "manageButton": "Stream verwalten"}, "tips": {"title": "Trinkgeld-Übersicht", "table": {"date": "Datum", "tipper": "Trinkgeld-Geber", "stream": "Stream", "amountNet": "Betrag (Netto)", "platformFee": "Plattform-Gebühr", "explorer": "Explorer", "viewOnExplorerTitle": "Transaktion im Solana Explorer anzeigen"}, "noTips": {"title": "Noch keine Trinkgelder", "description": "Du hast noch keine Trinkgelder erhalten. <PERSON><PERSON> deine Streams, um BONK zu verdi<PERSON>n!"}}}, "unexpectedError": "Ein unerwarteter Fehler ist aufgetreten"}, "onboarding": {"title": "Werde ein Creator", "subtitle": "Starte deine Creator-<PERSON><PERSON> auf bonkstream", "steps": {"profile": "Profil", "terms": "Bedingungen", "complete": "Abgeschlossen"}, "profileStep": {"title": "Richte dein Creator-Profil ein", "username": "Creator-Benutzern<PERSON>", "bio": "Bio", "bioPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> Z<PERSON>chauern etwas über dich", "avatarUpload": "<PERSON>il<PERSON><PERSON> ho<PERSON>n", "next": "<PERSON><PERSON>"}, "termsStep": {"title": "Akzeptiere die Creator-Bedingungen", "agreement": "Ich stimme den Creator-Bedingungen zu", "platformFeeInfo": "bonkstream erhebt eine kleine {fee}% Plattformgebühr auf Trinkgelder", "readTerms": "Vollständige Bedingungen lesen", "back": "Zurück", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "completeStep": {"title": "Du bist jetzt ein Creator!", "message": "<PERSON><PERSON>-<PERSON><PERSON> wurde erfolgreich eingerichtet", "goToDashboard": "Zum Dashboard"}, "step1": {"title": "Werde ein Creator", "description": "Tritt der bonkstream Creator-Community bei", "subDescription": "Richte dein Profil ein und verdiene mit deinen Inhalten", "cta": "<PERSON><PERSON><PERSON>"}, "step2": {"title": "Vervollständige dein Profil", "displayNameLabel": "Anzeigename", "displayNamePlaceholder": "<PERSON>ähle deinen Creator-Namen", "displayNameHint": "Dies wird dein öffentlicher Benutzername", "acceptTermsLabel": "Ich stimme den Nutzungsbedingungen zu", "termsLink": "Nutzungsbedingungen", "contentGuidelinesLink": "Inhaltsrichtlinien", "acceptFeesLabel": "Ich verstehe die Gebührenstruktur", "feesDescriptionPart1": "Plattform-Gebühr", "feePolicyLink": "Gebührenrichtlinie", "cta": "Setup abschließen"}, "errors": {"displayNameRequired": "Anzeigename ist erforderlich", "displayNameMinLength": "Anzeigename muss mindestens 3 Zeichen lang sein", "termsRequired": "Du musst die Nutzungsbedingungen akzeptieren", "feesRequired": "Du musst die Gebührenstruktur anerkennen"}}, "verifyingAccess": "Wallet wird überprüft...", "signingMessage": "Bitte signiere die Authentifizierungsnachricht", "checkWalletPrompt": "Bitte überprüfe deine Wallet für die Signaturanfrage. Dies ist erford<PERSON>lich, um auf das Creator Dashboard zuzugreifen.", "accessDenied": "<PERSON><PERSON><PERSON> verweigert", "walletRequired": "Du musst deine Wallet verbinden, um auf das Creator Dashboard zuzugreifen", "returnHome": "Zurück zur Startseite"}