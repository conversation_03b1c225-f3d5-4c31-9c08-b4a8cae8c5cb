{"title": "Ersteller-Profil", "joinedOn": "Beigetreten am {date}", "streamsSectionTitle": "Streams", "views": "Auf<PERSON><PERSON>", "stats": {"streams": "Streams", "views": "Auf<PERSON><PERSON>", "bonkEarned": "BONK <PERSON>ent", "tips": "<PERSON><PERSON><PERSON><PERSON>", "nfts": "NFTs"}, "tabs": {"streams": "Streams", "tips": "<PERSON><PERSON><PERSON><PERSON>", "nfts": "NFTs", "about": "<PERSON><PERSON>"}, "sections": {"recentTips": "Aktuelle Trinkgelder", "tipSummary": "Trinkgeld-Übersicht", "totalTips": "Gesamte Trinkgelder", "totalBonk": "Gesamt BONK", "recentNftMints": "Aktuelle NFT-Prägungen", "nftCollection": "NFT-Sammlung", "totalNfts": "Gesamt NFTs", "statistics": "Statistiken", "walletAddress": "Wall<PERSON><PERSON><PERSON><PERSON><PERSON>", "memberSince": "<PERSON><PERSON><PERSON><PERSON> seit", "avgStreamDuration": "Durchschnittliche Stream-Dauer"}, "actions": {"tipBonk": "BONK <PERSON>geld", "share": "Teilen", "copyWallet": "Wallet-<PERSON><PERSON><PERSON> k<PERSON>"}, "errors": {"fetchFailed": "Ersteller-Informationen konnten nicht geladen werden", "creatorNotFound": "Ersteller nicht gefunden"}, "buttons": {"backToDiscover": "Zurück zur Entdecken-Seite", "followCreator": "Folgen"}, "noStreamsMessage": "Die<PERSON> Ersteller hat noch keine Inhalte gestreamt", "noTipsMessage": "Noch keine Trinkgelder erhalten", "noNftsMessage": "Noch keine NFTs geprägt", "noBioMessage": "<PERSON><PERSON> Biografie ve<PERSON>ü<PERSON>bar"}