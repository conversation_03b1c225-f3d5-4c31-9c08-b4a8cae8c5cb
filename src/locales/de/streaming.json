{"goLive": "Live Gehen", "setupStream": "Stream Einrichten", "streamTitle": "Stream-Titel", "enterStreamTitle": "G<PERSON> einen Titel für deinen Stream ein", "streamDescription": "Beschreibung (optional)", "enterStreamDescription": "Füge Details zu deinem Stream hinzu", "startStreaming": "Streaming Starten", "endStream": "Stream <PERSON>", "back": "Zurück", "connectWalletFirst": "<PERSON><PERSON><PERSON><PERSON>", "walletRequiredForStreaming": "Du musst dein Wallet verbinden, um zu streamen.", "streamEnded": "<PERSON>", "streamEndedSuccess": "<PERSON>in Stream wurde erfolgreich beendet.", "streamEndError": "Fehler beim Beenden des Streams", "comments": "Kommentare", "noComments": "Noch keine Kommentare. Se<PERSON> der Erste, der kommentiert!", "typeComment": "Schreibe einen Kommentar...", "error": "<PERSON><PERSON>", "cameraAccessError": "<PERSON><PERSON> Zugriff auf deine Kamera. Bitte überprüfe deine Berechtigungen.", "walletNotConnected": "Bitte verbinde dein Wallet, um zu streamen.", "liveBroadcast": "Live-Übertragung", "viewers": "<PERSON><PERSON><PERSON><PERSON>", "likes": "<PERSON>s", "like": "Gefällt mir", "liked": "Gefällt mir", "likeStream": "Stream liken", "connectToLike": "Verbinde dein Wallet um den Stream zu liken", "camera": "<PERSON><PERSON><PERSON>", "microphone": "Mikrofon", "settings": "Einstellungen", "flipCamera": "<PERSON><PERSON><PERSON>", "deviceNotSupported": "Dein Gerät unterstützt keine Kamera-Streams", "permissionDenied": "Kamera-Berechtigung verweigert", "tooManyViewers": "Der Stream ist ausgelastet", "lowBandwidth": "Geringe Bandbreite erkannt", "reconnecting": "Verbindung wird wiederhergestellt...", "mobileStreamingGuide": "<PERSON><PERSON><PERSON> beste Ergebnisse halten Sie Ihr Telefon vertikal", "shareYourStream": "Teilen Sie Ihren Stream", "rotateDevice": "Drehen Sie Ihr Gerät für die beste Erfahrung", "chat": {"title": "Live Chat", "chatWithViewers": "<PERSON><PERSON> chatten...", "viewerCount": "{count} <PERSON><PERSON><PERSON><PERSON>", "noMessages": "Noch keine Nachrichten. Beginnen Si<PERSON> zu chatten!", "connectToChat": "Wallet verbinden zum Chatten", "minimizeChat": "Chat minimieren", "maximizeChat": "<PERSON><PERSON> er<PERSON>tern", "messageCount": "{count} <PERSON><PERSON><PERSON><PERSON>"}}