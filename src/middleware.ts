import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './lib/i18n';

interface RateLimitStore {
  [ip: string]: {
    count: number;
    resetTime: number;
  };
}

const adminApiStore: RateLimitStore = {};
const transactionApiStore: RateLimitStore = {};
const authApiStore: RateLimitStore = {};
const generalApiStore: RateLimitStore = {};

const rateLimits = {
  admin: { limit: 15, windowSizeInSeconds: 60 },
  transaction: { limit: 25, windowSizeInSeconds: 60 },
  auth: { limit: 8, windowSizeInSeconds: 60 },
  general: { limit: 50, windowSizeInSeconds: 60 },
};

setInterval(() => {
  const now = Date.now();
  const stores = [adminApiStore, transactionApiStore, authApiStore, generalApiStore];

  stores.forEach(store => {
    Object.keys(store).forEach(ip => {
      if (store[ip].resetTime < now) {
        delete store[ip];
      }
    });
  });
}, 300000);

function isRateLimited(
  ip: string,
  store: RateLimitStore,
  limit: number,
  windowSizeInSeconds: number
): boolean {
  const now = Date.now();
  const windowSize = windowSizeInSeconds * 1000;

  if (!store[ip] || now > store[ip].resetTime) {
    store[ip] = {
      count: 1,
      resetTime: now + windowSize,
    };
    return false;
  }

  store[ip].count += 1;

  return store[ip].count > limit;
}

function getRateLimitConfig(path: string): {
  store: RateLimitStore;
  limit: number;
  windowSize: number;
} {
  if (path.startsWith('/api/admin')) {
    return {
      store: adminApiStore,
      limit: rateLimits.admin.limit,
      windowSize: rateLimits.admin.windowSizeInSeconds,
    };
  }

  if (path.startsWith('/api/tip') || path.startsWith('/api/mint-moment')) {
    return {
      store: transactionApiStore,
      limit: rateLimits.transaction.limit,
      windowSize: rateLimits.transaction.windowSizeInSeconds,
    };
  }

  if (path.includes('/api/stream/chat/send')) {
    return {
      store: transactionApiStore,
      limit: 10,
      windowSize: 60,
    };
  }

  if (path.startsWith('/api/auth')) {
    return {
      store: authApiStore,
      limit: rateLimits.auth.limit,
      windowSize: rateLimits.auth.windowSizeInSeconds,
    };
  }

  return {
    store: generalApiStore,
    limit: rateLimits.general.limit,
    windowSize: rateLimits.general.windowSizeInSeconds,
  };
}

function getIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  return '127.0.0.1';
}

const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always',
});

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;
  const passwordProtect = process.env.PASSWORD_PROTECT;
  if (passwordProtect && !pathname.startsWith('/api')) {
    const cookie = request.cookies.get('bonkstream_pw')?.value;
    const pw = searchParams.get('pw');
    if (cookie !== passwordProtect) {
      // If password submitted via query, set cookie and redirect to clean URL
      if (pw === passwordProtect) {
        // Determine the appropriate locale for redirect
        const locale = request.cookies.get('NEXT_LOCALE')?.value ||
                      request.headers.get('accept-language')?.split(',')[0]?.substring(0, 2) ||
                      defaultLocale;

        // Ensure locale is supported
        const validLocale = locales.includes(locale as (typeof locales)[number]) ? locale : defaultLocale;

        // Create clean URL without password parameter
        const cleanUrl = new URL(`/${validLocale}`, request.url);

        // Create redirect response with cookie
        const response = NextResponse.redirect(cleanUrl);
        response.cookies.set('bonkstream_pw', passwordProtect, { httpOnly: true, maxAge: 60 * 60 * 24 });
        return response;
      }
      return new NextResponse(`<!DOCTYPE html><html><head><title>Password Required</title></head><body style='font-family:sans-serif;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;'><form method='GET'><input type='password' name='pw' placeholder='Password' style='padding:8px;font-size:1rem;'/><button type='submit' style='margin-left:8px;padding:8px 16px;'>Enter</button></form></body></html>`, {
        status: 401,
        headers: { 'Content-Type': 'text/html' }
      });
    }
  }

  if (pathname.startsWith('/admin') && !pathname.match(/^\/[a-z]{2}\/admin/)) {
    const locale = request.cookies.get('NEXT_LOCALE')?.value ||
                  request.headers.get('accept-language')?.split(',')[0]?.substring(0, 2) ||
                  defaultLocale;

    if (locales.includes(locale as (typeof locales)[number])) {
      const url = new URL(`/${locale}${pathname}`, request.url);
      url.search = request.nextUrl.search;
      return NextResponse.redirect(url);
    }
  }

  if (pathname.startsWith('/api')) {
    if (process.env.NODE_ENV !== 'development') {
      const ip = getIP(request);

      const { store, limit, windowSize } = getRateLimitConfig(pathname);

      if (isRateLimited(ip, store, limit, windowSize)) {
        return new NextResponse(
          JSON.stringify({
            error: 'Rate limit exceeded',
            message: 'Too many requests, please try again later.'
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              'Retry-After': `${windowSize}`,
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            }
          }
        );
      }
    }

    return NextResponse.next();
  }

  return intlMiddleware(request);
}

export const config = {
  matcher: [
    '/((?!_next|static|favicon.ico|robots.txt|.*\\.png|.*\\.jpg|.*\\.jpeg|.*\\.gif|.*\\.svg|.*\\.webp|.*\\.ico|.*\\.ttf|.*\\.woff|.*\\.woff2).*)'
  ],
};