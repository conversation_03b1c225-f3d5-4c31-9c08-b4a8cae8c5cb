export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          wallet_address: string;
          username: string | null;
          avatar_url: string | null;
          is_creator: boolean;
          is_admin: boolean;
          created_at: string;
          bio: string | null;
        };
        Insert: {
          id?: string;
          wallet_address: string;
          username?: string | null;
          avatar_url?: string | null;
          is_creator?: boolean;
          is_admin?: boolean;
          created_at?: string;
          bio?: string | null;
        };
        Update: {
          id?: string;
          wallet_address?: string;
          username?: string | null;
          avatar_url?: string | null;
          is_creator?: boolean;
          is_admin?: boolean;
          created_at?: string;
          bio?: string | null;
        };
      };
      streams: {
        Row: {
          id: string;
          creator_id: string | null;
          title: string;
          description: string | null;
          thumbnail_url: string | null;
          playback_url: string | null;
          storage_provider: string | null;
          storage_id: string | null;
          arweave_id: string | null;
          status: string;
          duration: number | null;
          view_count: number;
          tip_count: number;
          nft_count: number;
          created_at: string;
          retention_expires_at: string | null;
        };
        Insert: {
          id?: string;
          creator_id?: string | null;
          title: string;
          description?: string | null;
          thumbnail_url?: string | null;
          playback_url?: string | null;
          storage_provider?: string | null;
          storage_id?: string | null;
          arweave_id?: string | null;
          status?: string;
          duration?: number | null;
          view_count?: number;
          tip_count?: number;
          nft_count?: number;
          created_at?: string;
          retention_expires_at?: string | null;
        };
        Update: {
          id?: string;
          creator_id?: string | null;
          title?: string;
          description?: string | null;
          thumbnail_url?: string | null;
          playback_url?: string | null;
          storage_provider?: string | null;
          storage_id?: string | null;
          arweave_id?: string | null;
          status?: string;
          duration?: number | null;
          view_count?: number;
          tip_count?: number;
          nft_count?: number;
          created_at?: string;
          retention_expires_at?: string | null;
        };
      };
      tips: {
        Row: {
          id: string;
          stream_id: string | null;
          tipper_id: string | null;
          recipient_id: string | null;
          amount: number;
          platform_fee_percentage: number;
          platform_fee_amount: number;
          creator_amount: number;
          tx_signature: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          stream_id?: string | null;
          tipper_id?: string | null;
          recipient_id?: string | null;
          amount: number;
          platform_fee_percentage: number;
          platform_fee_amount: number;
          creator_amount: number;
          tx_signature: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          stream_id?: string | null;
          tipper_id?: string | null;
          recipient_id?: string | null;
          amount?: number;
          platform_fee_percentage?: number;
          platform_fee_amount?: number;
          creator_amount?: number;
          tx_signature?: string;
          created_at?: string;
        };
      };
      nfts: {
        Row: {
          id: string;
          mint_address: string;
          owner_id: string | null;
          stream_id: string | null;
          metadata_uri: string;
          timestamp: number;
          platform_fee_at_mint: number;
          tx_signature: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          mint_address: string;
          owner_id?: string | null;
          stream_id?: string | null;
          metadata_uri: string;
          timestamp: number;
          platform_fee_at_mint: number;
          tx_signature: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          mint_address?: string;
          owner_id?: string | null;
          stream_id?: string | null;
          metadata_uri?: string;
          timestamp?: number;
          platform_fee_at_mint?: number;
          tx_signature?: string;
          created_at?: string;
        };
      };
      platform_fees: {
        Row: {
          id: string;
          percentage: number;
          effective_from: string;
          changed_by: string | null;
          change_reason: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          percentage: number;
          effective_from?: string;
          changed_by?: string | null;
          change_reason?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          percentage?: number;
          effective_from?: string;
          changed_by?: string | null;
          change_reason?: string | null;
          created_at?: string;
        };
      };
      video_archives: {
        Row: {
          id: string;
          stream_id: string | null;
          original_url: string;
          archive_url: string | null;
          tx_id: string | null;
          size_bytes: number | null;
          status: string;
          created_at: string;
          completed_at: string | null;
        };
        Insert: {
          id?: string;
          stream_id?: string | null;
          original_url: string;
          archive_url?: string | null;
          tx_id?: string | null;
          size_bytes?: number | null;
          status?: string;
          created_at?: string;
          completed_at?: string | null;
        };
        Update: {
          id?: string;
          stream_id?: string | null;
          original_url?: string;
          archive_url?: string | null;
          tx_id?: string | null;
          size_bytes?: number | null;
          status?: string;
          created_at?: string;
          completed_at?: string | null;
        };
      };
      settings: {
        Row: {
          id: string;
          key: string;
          value: Json;
          description: string | null;
          updated_by: string | null;
          updated_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          key: string;
          value: Json;
          description?: string | null;
          updated_by?: string | null;
          updated_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          key?: string;
          value?: Json;
          description?: string | null;
          updated_by?: string | null;
          updated_at?: string;
          created_at?: string;
        };
      };
      admin_audit_logs: {
        Row: {
          id: string;
          action: string;
          entity_type: string;
          entity_id: string | null;
          admin_wallet: string;
          details: Json | null;
          signature: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          action: string;
          entity_type: string;
          entity_id?: string | null;
          admin_wallet: string;
          details?: Json | null;
          signature: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          action?: string;
          entity_type?: string;
          entity_id?: string | null;
          admin_wallet?: string;
          details?: Json | null;
          signature?: string;
          created_at?: string;
        };
      };
      creator_settings: {
        Row: {
          id: string;
          creator_id: string;
          display_name: string | null;
          bio: string | null;
          donation_goal: number | null;
          social_links: Json | null;
          stream_settings: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          creator_id: string;
          display_name?: string | null;
          bio?: string | null;
          donation_goal?: number | null;
          social_links?: Json | null;
          stream_settings?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          creator_id?: string;
          display_name?: string | null;
          bio?: string | null;
          donation_goal?: number | null;
          social_links?: Json | null;
          stream_settings?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      stream_views: {
        Row: {
          id: string;
          stream_id: string;
          viewer_id: string | null;
          ip_address: string;
          viewed_at: string;
        };
        Insert: {
          id?: string;
          stream_id: string;
          viewer_id?: string | null;
          ip_address: string;
          viewed_at?: string;
        };
        Update: {
          id?: string;
          stream_id?: string;
          viewer_id?: string | null;
          ip_address?: string;
          viewed_at?: string;
        };
      };
      data_exports: {
        Row: {
          id: string;
          user_id: string;
          export_type: string;
          status: string;
          file_url: string | null;
          created_at: string;
          completed_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          export_type: string;
          status?: string;
          file_url?: string | null;
          created_at?: string;
          completed_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          export_type?: string;
          status?: string;
          file_url?: string | null;
          created_at?: string;
          completed_at?: string | null;
        };
      };
      account_deletions: {
        Row: {
          id: string;
          user_id: string;
          requested_at: string;
          scheduled_for: string;
          reason: string | null;
          status: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          requested_at?: string;
          scheduled_for: string;
          reason?: string | null;
          status?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          requested_at?: string;
          scheduled_for?: string;
          reason?: string | null;
          status?: string;
        };
      };
      feature_flags: {
        Row: {
          id: string;
          name: string;
          enabled: boolean;
          description: string | null;
          updated_by: string | null;
          updated_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          enabled?: boolean;
          description?: string | null;
          updated_by?: string | null;
          updated_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          enabled?: boolean;
          description?: string | null;
          updated_by?: string | null;
          updated_at?: string;
          created_at?: string;
        };
      };
      chat_emotes: {
        Row: {
          id: string;
          name: string;
          image_url: string;
          created_by: string | null;
          is_active: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          image_url: string;
          created_by?: string | null;
          is_active?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          image_url?: string;
          created_by?: string | null;
          is_active?: boolean;
          created_at?: string;
        };
      };
      chat_messages: {
        Row: {
          id: string;
          stream_id: string | null;
          sender_id: string | null;
          sender_wallet_address: string;
          message_content: string;
          emotes: Json;
          is_deleted: boolean;
          deleted_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          stream_id?: string | null;
          sender_id?: string | null;
          sender_wallet_address: string;
          message_content: string;
          emotes?: Json;
          is_deleted?: boolean;
          deleted_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          stream_id?: string | null;
          sender_id?: string | null;
          sender_wallet_address?: string;
          message_content?: string;
          emotes?: Json;
          is_deleted?: boolean;
          deleted_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      stream_viewer_count: {
        Row: {
          id: string;
          stream_id: string | null;
          viewer_count: number;
          updated_at: string;
        };
        Insert: {
          id?: string;
          stream_id?: string | null;
          viewer_count?: number;
          updated_at?: string;
        };
        Update: {
          id?: string;
          stream_id?: string | null;
          viewer_count?: number;
          updated_at?: string;
        };
      };
      chat_rate_limits: {
        Row: {
          id: string;
          user_id: string | null;
          stream_id: string | null;
          message_count: number;
          window_start: string;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          stream_id?: string | null;
          message_count?: number;
          window_start?: string;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          stream_id?: string | null;
          message_count?: number;
          window_start?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      increment_view_count: {
        Args: {
          stream_id: string;
        };
        Returns: undefined;
      };
      increment_tip_count: {
        Args: {
          stream_id: string;
        };
        Returns: undefined;
      };
      increment_nft_count: {
        Args: {
          stream_id: string;
        };
        Returns: undefined;
      };
      update_viewer_count: {
        Args: {
          p_stream_id: string;
          p_count: number;
        };
        Returns: undefined;
      };
      check_chat_rate_limit: {
        Args: {
          p_user_id: string;
          p_stream_id: string;
          p_max_messages?: number;
          p_window_seconds?: number;
        };
        Returns: boolean;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
