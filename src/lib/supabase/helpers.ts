/* eslint-disable @typescript-eslint/no-explicit-any */

import { supabase } from './client';
import { Database } from './types';

/**
 * Get the current platform fee percentage
 */
export async function getCurrentPlatformFee(): Promise<number> {
  const { data, error } = await supabase
    .from('platform_fees')
    .select('percentage')
    .order('effective_from', { ascending: false })
    .limit(1)
    .single();
  
  if (error) {
    console.error('Error fetching platform fee:', error);
    return 5; // Default to 5% if there's an error
  }
  
  return data.percentage;
}

/**
 * Get a profile by wallet address
 */
export async function getProfileByWallet(walletAddress: string) {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('wallet_address', walletAddress)
    .single();
  
  if (error && error.code !== 'PGRST116') { // PGRST116 is the error code for "no rows returned"
    console.error('Error fetching profile:', error);
    return null;
  }
  
  return data;
}

/**
 * Create or update a profile
 */
export async function upsertProfile(profile: Database['public']['Tables']['profiles']['Insert']) {
  const { data, error } = await supabase
    .from('profiles')
    .upsert(profile, { onConflict: 'wallet_address' })
    .select()
    .single();
  
  if (error) {
    console.error('Error upserting profile:', error);
    return null;
  }
  
  return data;
}

/**
 * Get streams with pagination
 */
export async function getStreams(page = 1, pageSize = 10) {
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;
  
  const { data, error, count } = await supabase
    .from('streams')
    .select('*, profiles(username, avatar_url)', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(from, to);
  
  if (error) {
    console.error('Error fetching streams:', error);
    return { data: [], count: 0 };
  }
  
  return { data, count };
}

/**
 * Get stream by ID with creator information
 */
export async function getStreamById(id: string) {
  const { data, error } = await supabase
    .from('streams')
    .select('*, profiles(username, avatar_url, wallet_address)')
    .eq('id', id)
    .single();
  
  if (error) {
    console.error('Error fetching stream:', error);
    return null;
  }
  
  return data;
}

/**
 * Log tip transaction
 */
export async function logTipTransaction(tipData: Database['public']['Tables']['tips']['Insert']) {
  const { data, error } = await supabase
    .from('tips')
    .insert(tipData)
    .select()
    .single();
  
  if (error) {
    console.error('Error logging tip transaction:', error);
    return null;
  }
  
  return data;
}

/**
 * Log NFT minting
 */
export async function logNftMint(nftData: Database['public']['Tables']['nfts']['Insert']) {
  const { data, error } = await supabase
    .from('nfts')
    .insert(nftData)
    .select()
    .single();
  
  if (error) {
    console.error('Error logging NFT mint:', error);
    return null;
  }
  
  return data;
}

/**
 * Get system setting
 */
export async function getSystemSetting<T>(key: string): Promise<T | null> {
  const { data, error } = await supabase
    .from('system_settings')
    .select('value')
    .eq('key', key)
    .single();
  
  if (error) {
    console.error(`Error fetching system setting ${key}:`, error);
    return null;
  }
  
  return data.value as T;
}

/**
 * Update system setting
 */
export async function updateSystemSetting(key: string, value: any, userId?: string) {
  const { data, error } = await supabase
    .from('system_settings')
    .upsert({
      key,
      value,
      updated_by: userId,
      updated_at: new Date().toISOString()
    }, { onConflict: 'key' })
    .select()
    .single();
  
  if (error) {
    console.error(`Error updating system setting ${key}:`, error);
    return null;
  }
  
  return data;
}

/**
 * Get feature flag status
 */
export async function getFeatureFlag(name: string): Promise<boolean> {
  const { data, error } = await supabase
    .from('feature_flags')
    .select('enabled')
    .eq('name', name)
    .single();
  
  if (error) {
    console.error(`Error fetching feature flag ${name}:`, error);
    return false; // Default to disabled if error
  }
  
  return data.enabled;
}

/**
 * Update feature flag
 */
export async function updateFeatureFlag(name: string, enabled: boolean, userId?: string) {
  const { data, error } = await supabase
    .from('feature_flags')
    .upsert({
      name,
      enabled,
      updated_by: userId,
      updated_at: new Date().toISOString()
    }, { onConflict: 'name' })
    .select()
    .single();
  
  if (error) {
    console.error(`Error updating feature flag ${name}:`, error);
    return null;
  }
  
  return data;
}

/**
 * Log audit event
 */
export async function logAuditEvent(
  action: string,
  entityType: string,
  entityId?: string,
  userId?: string,
  ipAddress?: string,
  details?: any
) {
  const { data, error } = await supabase
    .from('audit_logs')
    .insert({
      action,
      entity_type: entityType,
      entity_id: entityId,
      user_id: userId,
      ip_address: ipAddress,
      details,
    })
    .select()
    .single();
  
  if (error) {
    console.error('Error logging audit event:', error);
    return null;
  }
  
  return data;
}

/**
 * Check if user is admin
 */
export async function isUserAdmin(walletAddress: string): Promise<boolean> {
  const { data, error } = await supabase
    .from('profiles')
    .select('is_admin')
    .eq('wallet_address', walletAddress)
    .single();
  
  if (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
  
  return data.is_admin || false;
}

/**
 * Get user tips with pagination
 */
export async function getUserTips(userId: string, page = 1, pageSize = 10) {
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;
  
  const { data, error, count } = await supabase
    .from('tips')
    .select(`
      *,
      streams(title, thumbnail_url),
      profiles:recipient_id(username, wallet_address, avatar_url)
    `, { count: 'exact' })
    .eq('tipper_id', userId)
    .order('created_at', { ascending: false })
    .range(from, to);
  
  if (error) {
    console.error('Error fetching user tips:', error);
    return { data: [], count: 0 };
  }
  
  return { data, count };
}

/**
 * Get user NFTs with pagination
 */
export async function getUserNfts(userId: string, page = 1, pageSize = 10) {
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;
  
  const { data, error, count } = await supabase
    .from('nfts')
    .select(`
      *,
      streams(title, thumbnail_url),
      profiles:owner_id(username, wallet_address, avatar_url)
    `, { count: 'exact' })
    .eq('owner_id', userId)
    .order('created_at', { ascending: false })
    .range(from, to);
  
  if (error) {
    console.error('Error fetching user NFTs:', error);
    return { data: [], count: 0 };
  }
  
  return { data, count };
} 