/* eslint-disable @typescript-eslint/no-unused-vars */

import { Connection, PublicKey, clusterApiUrl } from '@solana/web3.js';

// Helius RPC endpoints with fallbacks
const RPC_ENDPOINTS = [
  // Primary: Helius RPC (with API key if available)
  process.env.NEXT_PUBLIC_SOLANA_RPC || 'https://mainnet.helius-rpc.com',
  // Fallback: Helius without API key
  'https://mainnet.helius-rpc.com',
  // Additional fallbacks
  'https://api.mainnet-beta.solana.com',
  'https://solana-mainnet.g.alchemy.com/v2/demo',
  'https://rpc.ankr.com/solana',
  clusterApiUrl('mainnet-beta'), // Official Solana RPC as final fallback
];

// Devnet endpoints for development
const DEVNET_RPC_ENDPOINTS = [
  process.env.NEXT_PUBLIC_SOLANA_RPC || 'https://devnet.helius-rpc.com',
  'https://devnet.helius-rpc.com',
  'https://api.devnet.solana.com',
  clusterApiUrl('devnet'),
];

// Get the appropriate endpoints based on network
const getEndpoints = () => {
  const isDevnet = process.env.NEXT_PUBLIC_SOLANA_NETWORK === 'devnet';
  return isDevnet ? DEVNET_RPC_ENDPOINTS : RPC_ENDPOINTS;
};

// Create a Solana connection with Helius RPC
export const getSolanaConnection = () => {
  // Try custom RPC first if provided
  const customRPC = process.env.NEXT_PUBLIC_SOLANA_RPC;
  
  // If custom RPC is set, use it (should be Helius endpoint)
  if (customRPC) {
    return new Connection(customRPC, {
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 30000,
    });
  }
  
  // Otherwise use the first Helius endpoint
  const endpoints = getEndpoints();
  return new Connection(endpoints[0], {
    commitment: 'confirmed',
    confirmTransactionInitialTimeout: 30000,
  });
};

// Create connection with fallback retry logic using Helius first
export const getSolanaConnectionWithFallback = async () => {
  const endpoints = getEndpoints();
  
  for (let i = 0; i < endpoints.length; i++) {
    try {
      const connection = new Connection(endpoints[i], {
        commitment: 'confirmed',
        confirmTransactionInitialTimeout: 30000,
      });
      
      // Test the connection with a quick call
      await connection.getLatestBlockhash();
      return connection;
    } catch (error) {
      if (i === endpoints.length - 1) {
        // Last endpoint failed, throw error
        throw new Error('All Helius RPC endpoints failed');
      }
    }
  }
  
  // Fallback to default Helius endpoint
  const defaultEndpoint = getEndpoints()[0];
  return new Connection(defaultEndpoint);
};

// BONK token constants - Official mainnet address
export const BONK_MINT_ADDRESS = new PublicKey('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263'); // Official BONK mainnet
export const BONK_DECIMALS = 5;

// Platform wallet and fee
export const PLATFORM_WALLET_ADDRESS = (() => {
  try {
    return process.env.PLATFORM_WALLET_ADDRESS
      ? new PublicKey(process.env.PLATFORM_WALLET_ADDRESS)
      : new PublicKey('********************************'); // Valid system program ID
  } catch (error) {
    return new PublicKey('********************************'); // Valid system program ID
  }
})();

// Platform fee calculation
export const getPlatformFeePercentage = (): number => {
  return parseFloat(process.env.NEXT_PUBLIC_PLATFORM_FEE_PERCENTAGE || "5");
};

export const calculatePlatformFee = (amount: number): { 
  platformFee: number, 
  creatorAmount: number 
} => {
  const feePercentage = getPlatformFeePercentage();
  const platformFee = Math.floor((amount * feePercentage) / 100);
  const creatorAmount = amount - platformFee;
  
  return {
    platformFee,
    creatorAmount
  };
}; 