import { createClient } from './supabase/server';

// Get current platform fee from database
export async function getCurrentPlatformFee(): Promise<number> {
  try {
    const supabase = await createClient();
    
    // Get the most recent fee
    const { data, error } = await supabase
      .from('platform_fees')
      .select('percentage')
      .order('effective_from', { ascending: false })
      .limit(1);
      
    if (error) {
      throw error;
    }
    
    return data && data.length > 0 
      ? data[0].percentage 
      : parseFloat(process.env.NEXT_PUBLIC_PLATFORM_FEE_PERCENTAGE || "5");
  } catch (error) {
    // Fallback to env variable if DB lookup fails
    console.error('Error fetching platform fee:', error);
    return parseFloat(process.env.NEXT_PUBLIC_PLATFORM_FEE_PERCENTAGE || "5");
  }
}

// Get platform fee history with admin info
export async function getPlatformFeeHistory(limit = 10, offset = 0) {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('platform_fees')
      .select(`
        id,
        percentage,
        effective_from,
        created_at,
        changed_by,
        profiles(username, wallet_address)
      `)
      .order('effective_from', { ascending: false })
      .range(offset, offset + limit - 1);
      
    if (error) {
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('Error fetching platform fee history:', error);
    return [];
  }
}

// Export alias for backward compatibility
export const getPlatformFeePercentage = getCurrentPlatformFee;