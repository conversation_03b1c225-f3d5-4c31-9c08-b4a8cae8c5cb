import { getPlatformFeePercentage } from './solana';

// NFT Metadata type
export type NFTMetadata = {
  name: string;
  description: string;
  image: string;
  attributes: {
    trait_type: string;
    value: string;
  }[];
  properties: {
    streamId: string;
    timestamp: number;
    platform: string;
    platformFeePercentage: number;
    [key: string]: string | number;
  };
  external_url?: string;
};

/**
 * Upload image and metadata to IPFS using free services
 */
export async function uploadMetadataToIPFS(metadata: NFTMetadata): Promise<string> {
  try {
    let imageUrl = metadata.image;
    
    // If it's a data URL (captured image), upload it to IPFS first
    if (metadata.image && metadata.image.startsWith('data:')) {
      imageUrl = await uploadImageToIPFS(metadata.image);
    }
    
    // Create HYBRID metadata approach:
    // - Rich metadata goes to IPFS (via URI) for collection pages
    // - Minimal on-chain metadata keeps transaction under 1232 bytes
    const nftMetadata = {
      name: metadata.name.substring(0, 25), // Reasonable name length
      description: metadata.description.substring(0, 100), // Meaningful description
      image: imageUrl,
      external_url: `https://bonkstream.com/stream/${metadata.properties.streamId}?t=${metadata.properties.timestamp}`, // Stream URL for collection page
      attributes: [
        { trait_type: "Platform", value: "bonkstream" },
        { trait_type: "Creator", value: metadata.attributes.find(a => a.trait_type === 'Creator')?.value || 'Unknown' },
        { trait_type: "Stream", value: metadata.properties.streamId.substring(0, 8) }, // Short stream ID
        { trait_type: "Timestamp", value: metadata.properties.timestamp.toString() },
        { trait_type: "Time", value: metadata.attributes.find(a => a.trait_type === 'Time')?.value || '0:00' }
      ],
      properties: {
        streamId: metadata.properties.streamId,
        timestamp: metadata.properties.timestamp,
        platform: 'bonkstream',
        streamUrl: `https://bonkstream.com/stream/${metadata.properties.streamId}`,
        category: 'Stream Moment'
      }
    };
  
    
    // Upload metadata to IPFS
    const metadataUrl = await uploadJSONToIPFS(nftMetadata);

    
    return metadataUrl;
  } catch (error) {
    console.error('Error uploading to IPFS:', error);
    throw error; // Fail gracefully - no fallbacks
  }
}

/**
 * Upload image data URL to IPFS using alternative services (requires API keys)
 */
async function uploadImageToIPFS(dataUrl: string): Promise<string> {
  try {
    
    // Extract base64 data and mime type
    const matches = dataUrl.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
    if (!matches || matches.length !== 3) {
      throw new Error('Invalid data URL format');
    }
    
    const mimeType = matches[1];
    const base64Data = matches[2];
    
    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, 'base64');
    
    // Try Pinata first (most reliable, free tier available)
    if (process.env.PINATA_JWT || (process.env.PINATA_API_KEY && process.env.PINATA_SECRET_KEY)) {
      try {
        const formData = new FormData();
        const blob = new Blob([buffer], { type: mimeType });
        const extension = mimeType.split('/')[1] || 'png';
        formData.append('file', blob, `bonk-moment-${Date.now()}.${extension}`);
        
        // Use JWT if available (recommended), otherwise fall back to API key/secret
        const headers: Record<string, string> = {};
        if (process.env.PINATA_JWT) {
          headers['Authorization'] = `Bearer ${process.env.PINATA_JWT}`;
        } else {
          headers['pinata_api_key'] = process.env.PINATA_API_KEY!;
          headers['pinata_secret_api_key'] = process.env.PINATA_SECRET_KEY!;
        }
        
        const response = await fetch('https://api.pinata.cloud/pinning/pinFileToIPFS', {
          method: 'POST',
          headers,
          body: formData,
        });
        
        if (response.ok) {
          const result = await response.json();
          const imageUrl = `https://ipfs.io/ipfs/${result.IpfsHash}`;
          return imageUrl;
        } else {
          console.error('Pinata failed with status:', response.status);
          const errorText = await response.text();
          throw new Error(`Pinata upload failed: ${response.status} - ${errorText}`);
        }
      } catch (pinataError) {
        console.error('Pinata error:', pinataError);
        throw new Error(`Pinata upload failed: ${pinataError}`);
      }
    }
    
    // No Pinata credentials provided
    throw new Error('PINATA_JWT or (PINATA_API_KEY and PINATA_SECRET_KEY) are required for IPFS image uploads.');
    
  } catch (error) {
    console.error('Error uploading image to IPFS:', error);
    throw error; // Propagate error instead of using fallbacks
  }
}

/**
 * Upload JSON metadata to IPFS with ultra-short URLs for Solana limits
 */
async function uploadJSONToIPFS(metadata: object): Promise<string> {
  try {
    const jsonString = JSON.stringify(metadata, null, 0); // No formatting to save space
    const buffer = Buffer.from(jsonString, 'utf8');
    
    const formData = new FormData();
    const blob = new Blob([buffer], { type: 'application/json' });
    formData.append('file', blob, `m${Date.now()}.json`); // Ultra short filename
    
    // Use Pinata for metadata upload
    if (!process.env.PINATA_JWT && (!process.env.PINATA_API_KEY || !process.env.PINATA_SECRET_KEY)) {
      throw new Error('PINATA_JWT or (PINATA_API_KEY and PINATA_SECRET_KEY) are required for metadata upload.');
    }
    
    try {
      // Use JWT if available (recommended), otherwise fall back to API key/secret
      const headers: Record<string, string> = {};
      if (process.env.PINATA_JWT) {
        headers['Authorization'] = `Bearer ${process.env.PINATA_JWT}`;
      } else {
        headers['pinata_api_key'] = process.env.PINATA_API_KEY!;
        headers['pinata_secret_api_key'] = process.env.PINATA_SECRET_KEY!;
      }
      
      const response = await fetch('https://api.pinata.cloud/pinning/pinFileToIPFS', {
        method: 'POST',
        headers,
        body: formData,
      });
      
      if (response.ok) {
        const result = await response.json();
        const metadataUrl = `https://ipfs.io/ipfs/${result.IpfsHash}`;
        return metadataUrl;
      } else {
        console.error('Pinata metadata upload failed with status:', response.status);
        const errorText = await response.text();
        throw new Error(`Pinata metadata upload failed: ${response.status} - ${errorText}`);
      }
    } catch (pinataError) {
      console.error('Pinata metadata upload error:', pinataError);
      throw new Error(`Pinata metadata upload failed: ${pinataError}`);
    }
    
  } catch (error) {
    console.error('Error uploading metadata to IPFS:', error);
    throw error;
  }
}

/**
 * Create real NFT mint transaction using Metaplex SDK
 * This creates a proper NFT with metadata that shows up in wallet collectibles
 */
export async function createNftMintTransaction(
  metadata: NFTMetadata,
  ownerWalletAddress: string
) {
  try {
    // Upload metadata to IPFS first
    const metadataUri = await uploadMetadataToIPFS(metadata);
    
    // Create a simple collectible NFT that will show up in Phantom's collectibles
    return createSimpleCollectibleNft(metadata, ownerWalletAddress, metadataUri);
  } catch (error) {
    console.error('Error creating NFT:', error);
    throw new Error(`Failed to create NFT transaction: ${(error as Error).message}`);
  }
}

/**
 * Create a secure NFT using client-side wallet signing
 * This bypasses server transaction size limits and creates real NFTs
 */
export async function createSimpleCollectibleNft(metadata: NFTMetadata, ownerWalletAddress: string, metadataUri?: string) {
  try {
    // Always use client-side creation to avoid transaction size issues
    
    // Upload metadata to IPFS if not provided
    const finalMetadataUri = metadataUri || await uploadMetadataToIPFS(metadata);
    
    // Return instructions for client-side NFT creation
    return {
      clientSideCreation: true,
      metadataUri: finalMetadataUri,
      mintAddress: null, // Will be generated client-side
      platformFeePercentage: getPlatformFeePercentage(),
      instructions: {
        type: 'CREATE_COLLECTIBLE_NFT',
        metadata: {
          name: metadata.name.substring(0, 32),
          symbol: 'BONK',
          uri: finalMetadataUri,
          sellerFeeBasisPoints: 500, // 5% royalties
          attributes: metadata.attributes,
        }
      }
    };
  } catch (error) {
    console.error('Error preparing collectible NFT:', error);
    throw new Error(`Failed to prepare NFT: ${(error as Error).message}`);
  }
}

/**
 * Create a client-side NFT transaction that bypasses server transaction limits
 * This approach uses the user's wallet to create the NFT directly
 */
export async function createClientSideNft(metadata: NFTMetadata) {
  try {
    // For client-side NFT creation, we return instructions for the frontend
    // to create the NFT using wallet.signTransaction() directly
    
    const finalMetadataUri = await uploadMetadataToIPFS(metadata);
        
    // Return data needed for client-side NFT creation
    return {
      metadataUri: finalMetadataUri,
      mintAddress: null, // Will be generated client-side
      platformFeePercentage: getPlatformFeePercentage(),
      clientSideCreation: true,
      instructions: {
        type: 'CREATE_NFT',
        metadata: {
          name: metadata.name.substring(0, 32),
          symbol: 'BONK',
          uri: finalMetadataUri,
          sellerFeeBasisPoints: 500,
        }
      }
    };
  } catch (error) {
    console.error('Error preparing client-side NFT:', error);
    throw new Error(`Failed to prepare NFT: ${(error as Error).message}`);
  }
}

/**
 * Generate NFT metadata format for stream moments
 */
export function createNFTMetadata(
  streamTitle: string,
  timestamp: number,
  imageUrl: string,
  creatorName: string,
  streamId: string
): NFTMetadata {
  // Format timestamp as MM:SS
  const minutes = Math.floor(timestamp / 60);
  const seconds = Math.floor(timestamp % 60);
  const formattedTimestamp = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  
  return {
    name: `${streamTitle.substring(0, 20)} - ${formattedTimestamp}`, // Shorter name
    description: `Stream moment at ${formattedTimestamp} by ${creatorName}`, // Much shorter description
    image: imageUrl,
    attributes: [
      { trait_type: 'Creator', value: creatorName },
      { trait_type: 'Time', value: formattedTimestamp },
      { trait_type: 'Platform', value: 'bonkstream' }
    ], // Reduced attributes
    properties: {
      streamId,
      timestamp,
      platform: 'bonkstream',
      platformFeePercentage: 25
    } // Minimal properties
  };
} 