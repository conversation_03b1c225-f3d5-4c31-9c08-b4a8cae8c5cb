/**
 * Video frame capture utilities for NFT minting
 */

/**
 * Capture a frame from video element at current time
 * Returns a data URL that can be used directly as image source
 */
export function captureVideoFrame(
  videoElement: HTMLVideoElement,
  width: number = 1280,
  height: number = 720
): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('Could not get canvas context');
      }
      
      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;
      
      // Draw current video frame to canvas
      ctx.drawImage(videoElement, 0, 0, width, height);
      
      // Convert to base64 data URL
      const dataURL = canvas.toDataURL('image/jpeg', 0.8);
      resolve(dataURL);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Capture frame from video at specific timestamp
 */
export function captureVideoFrameAtTime(
  videoElement: HTMLVideoElement,
  timestamp: number,
  width: number = 1280,
  height: number = 720
): Promise<string> {
  return new Promise((resolve, reject) => {
    const originalTime = videoElement.currentTime;
    
    const handleSeeked = async () => {
      try {
        const frameData = await captureVideoFrame(videoElement, width, height);
        
        // Restore original time
        videoElement.currentTime = originalTime;
        videoElement.removeEventListener('seeked', handleSeeked);
        
        resolve(frameData);
      } catch (error) {
        videoElement.currentTime = originalTime;
        videoElement.removeEventListener('seeked', handleSeeked);
        reject(error);
      }
    };
    
    videoElement.addEventListener('seeked', handleSeeked);
    videoElement.currentTime = timestamp;
  });
}
