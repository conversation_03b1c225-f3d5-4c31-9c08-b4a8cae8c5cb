import { cookies } from 'next/headers';
import { createClient } from './supabase/server';
import { PublicKey } from '@solana/web3.js';
import nacl from 'tweetnacl';

// Get or create a user profile
export async function getOrCreateProfile(walletAddress: string) {
  try {
    const supabase = await createClient();
    
    // Check if profile exists
    const { data: existingProfile, error: selectError } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', walletAddress)
      .single();
      
    if (selectError && selectError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      throw selectError;
    }
    
    // Return existing profile if found
    if (existingProfile) {
      return existingProfile;
    }
    
    // Create new profile
    const { data: newProfile, error: insertError } = await supabase
      .from('profiles')
      .insert({
        wallet_address: walletAddress,
        is_creator: false,
        is_admin: false,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
      
    if (insertError) {
      throw insertError;
    }
    
    return newProfile;
  } catch (error) {
    console.error('Error getting or creating profile:', error);
    throw error;
  }
}

// Check if a user has admin privileges
export async function checkAdminStatus(walletAddress: string): Promise<boolean> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('wallet_address', walletAddress)
      .single();
      
    if (error) {
      throw error;
    }
    
    return data?.is_admin || false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Session management
export async function setSession(userId: string, walletAddress: string) {
  const cookieStore = await cookies();
  cookieStore.set('user_id', userId, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 1 week
  });
  
  cookieStore.set('wallet_address', walletAddress, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 1 week
  });
}

export async function clearSession() {
  const cookieStore = await cookies();
  cookieStore.delete('user_id');
  cookieStore.delete('wallet_address');
}

// Get current user session
export async function getSession() {
  const cookieStore = await cookies();
  const userIdCookie = cookieStore.get('user_id');
  const walletAddressCookie = cookieStore.get('wallet_address');
  
  const userId = userIdCookie?.value;
  const walletAddress = walletAddressCookie?.value;
  
  if (!userId || !walletAddress) {
    return null;
  }
  
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .eq('wallet_address', walletAddress)
      .single();
      
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error getting session:', error);
    await clearSession();
    return null;
  }
}

// Extended auth function that checks both session cookies and request headers
export async function auth(requestHeaders?: Headers) {
  try {
    // First try to get session from cookies (server-side auth)
    const session = await getSession();
    if (session) {
      return {
        user: {
          id: session.id,
          walletAddress: session.wallet_address,
          isAdmin: session.is_admin || false
        }
      };
    }
    
    // If no session from cookies, try to get wallet from authorization header
    // This helps bridge client-side localStorage auth with server APIs
    if (requestHeaders) {
      const authHeader = requestHeaders.get('authorization');
      const signatureHeader = requestHeaders.get('x-wallet-signature');
      const messageHeader = requestHeaders.get('x-wallet-message');
      
      // For all sensitive operations, require wallet signature verification
      // This ensures only the true wallet owner can access their data
      if (authHeader && authHeader.startsWith('Wallet ')) {
        const walletAddress = authHeader.substring(7);
        
        // For sensitive operations requiring data access or modifications
        // Always verify signature to confirm wallet ownership
        if (signatureHeader && messageHeader) {
          const isSignatureValid = await verifyWalletSignature(walletAddress, signatureHeader, messageHeader);
          
          if (!isSignatureValid) {
            console.error('Invalid wallet signature');
            return null;
          }
        } else {
          // Without signature verification, only allow public read-only operations
          // For any operation that accesses private data or modifies state,
          // return null here to enforce proper signature verification
          console.warn('Missing signature verification for wallet operation');
          // Only set to true for specific public API routes, otherwise always require signature
          const isPublicReadOnlyRoute = false; 
          if (!isPublicReadOnlyRoute) {
            return null;
          }
        }
        
        const profile = await getProfileByWallet(walletAddress);
        
        if (profile) {
          return {
            user: {
              id: profile.id,
              walletAddress: profile.wallet_address,
              isAdmin: profile.is_admin || false
            }
          };
        }
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error in auth function:', error);
    return null;
  }
}

// Verify a wallet signature
export async function verifyWalletSignature(walletAddress: string, signature: string, message: string): Promise<boolean> {
  try {
    // Convert signature from base64 to Uint8Array
    const signatureBytes = Buffer.from(signature, 'base64');
    
    // Create a PublicKey from the wallet address
    const publicKey = new PublicKey(walletAddress);
    
    // Convert message to Uint8Array
    const messageBytes = new TextEncoder().encode(message);
    
    // Verify the signature using nacl
    return nacl.sign.detached.verify(
      messageBytes,
      signatureBytes,
      publicKey.toBytes()
    );
  } catch (error) {
    console.error('Error verifying wallet signature:', error);
    return false;
  }
}

// Helper to get profile by wallet address
async function getProfileByWallet(walletAddress: string) {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('wallet_address', walletAddress)
      .single();
      
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error getting profile by wallet:', error);
    return null;
  }
} 