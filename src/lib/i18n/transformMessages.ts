import { set } from 'lodash';

export function transformFlatMessages(messages: Record<string, string>): Record<string, unknown> {
  return Object.entries(messages).reduce(
    (acc, [key, value]) => set(acc, key, value),
    {}
  );
}

export function transformMessages(messages: Record<string, string>): Record<string, unknown> {
  const hasNestedStructure = Object.keys(messages).some(key =>
    typeof messages[key] === 'object' && messages[key] !== null
  );

  if (hasNestedStructure) {
    return messages;
  }

  const hasDotNotation = Object.keys(messages).some(key => key.includes('.'));

  if (hasDotNotation) {
    return transformFlatMessages(messages);
  }

  return messages;
}