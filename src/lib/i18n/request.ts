import { getRequestConfig } from 'next-intl/server';
import { locales, defaultLocale } from './index';
import type { Locale } from './index';

interface MessageNamespace {
  [key: string]: unknown;
}

interface Messages {
  common: MessageNamespace;
  admin: MessageNamespace;
  stream: MessageNamespace;
  streaming: MessageNamespace;
  profile: MessageNamespace;
  collection: MessageNamespace;
  creator: MessageNamespace;
  creatorPublicPage: MessageNamespace;
  legal: MessageNamespace;
}

async function getAllMessages(locale: string): Promise<Messages> {
  try {
    const [
      common,
      admin,
      stream,
      streaming,
      profile,
      collection,
      creator,
      creatorPublicPage,
      legal
    ] = await Promise.all([
      import(`../../locales/${locale}/common.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/admin.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/stream.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/streaming.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/profile.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/collection.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/creator.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/creatorPublicPage.json`).then(m => m.default).catch(() => ({})),
      import(`../../locales/${locale}/legal.json`).then(m => m.default).catch(() => ({}))
    ]);
    
    return {
      common,
      admin,
      stream,
      streaming,
      profile,
      collection,
      creator,
      creatorPublicPage,
      legal
    };
  } catch (error) {
    console.error(`Error loading messages for ${locale}:`, error);
    return {
      common: {},
      admin: {},
      stream: {},
      streaming: {},
      profile: {},
      collection: {},
      creator: {},
      creatorPublicPage: {},
      legal: {}
    };
  }
}

export default getRequestConfig(async ({ locale }) => {
  const safeLocale: string = typeof locale === 'string' && locales.includes(locale as Locale) 
    ? locale 
    : defaultLocale;
  
  const messages = await getAllMessages(safeLocale);
  
  return {
    messages,
    locale: safeLocale,
    timeZone: 'Europe/Berlin'
  };
}); 