/* eslint-disable @typescript-eslint/no-explicit-any */

import { Livepeer } from "livepeer";

// VideoProvider interface
// Base metadata interface
interface AssetMetadata {
  name: string;
  description?: string;
  [key: string]: unknown;
}

// Asset details interface
interface AssetDetails {
  id: string;
  name: string;
  status: string;
  playbackUrl?: string;
  downloadUrl?: string;
  size?: number;
  duration?: number;
  createdAt: number;
  [key: string]: unknown;
}

export interface VideoProvider {
  // Asset management
  getAssetDetails(assetId: string): Promise<AssetDetails>;
  createAssetFromUrl(url: string, metadata: AssetMetadata): Promise<AssetDetails>;
  deleteAsset(assetId: string): Promise<boolean>;
  getPlaybackUrl(assetId: string): Promise<string>;
  
  // Live streaming
  createStream(request: CreateStreamRequest): Promise<LivepeerStream>;
  getStream(streamId: string): Promise<LivepeerStream | null>;
  updateStream(streamId: string, updates: Partial<CreateStreamRequest>): Promise<LivepeerStream>;
  deleteStream(streamId: string): Promise<boolean>;
  getStreamPlaybackUrl(playbackId: string): Promise<string>;
  getStreamIngestUrl(streamKey: string): Promise<string>;
  suspendStream(streamId: string): Promise<boolean>;
  resumeStream(streamId: string): Promise<boolean>;
  getStreams(): Promise<LivepeerStream[]>;
}

// Initialize Livepeer client using the official SDK
const LIVEPEER_API_KEY = process.env.LIVEPEER_API_KEY;

if (!LIVEPEER_API_KEY) {
  console.warn('Missing Livepeer API key');
}

// Livepeer Stream interface
export interface LivepeerStream {
  id: string;
  name: string;
  streamKey: string;
  playbackId: string;
  isActive: boolean;
  lastSeen: number;
  record: boolean;
  suspended: boolean;
  sourceSegments: number;
  transcodedSegments: number;
  sourceSegmentsDuration: number;
  transcodedSegmentsDuration: number;
  createdAt: number;
  profiles?: {
    name: string;
    bitrate: number;
    fps: number;
    width: number;
    height: number;
  }[];
}

// Stream creation request
export interface CreateStreamRequest {
  name: string;
  record?: boolean;
  profiles?: {
    name: string;
    bitrate: number;
    fps: number;
    width: number;
    height: number;
  }[];
}

// Stream session interface for active stream tracking
export interface StreamSession {
  id: string;
  streamId: string;
  userId: string;
  status: 'active' | 'ended';
  startedAt: Date;
  endedAt?: Date;
  viewerCount: number;
}

export class LivepeerProvider implements VideoProvider {
  private livepeer: Livepeer;
  
  constructor(apiKey = LIVEPEER_API_KEY) {
    if (!apiKey) {
      throw new Error('Livepeer API key is required');
    }
    this.livepeer = new Livepeer({ apiKey });
  }
  
  async getAssetDetails(assetId: string): Promise<AssetDetails> {
    try {
      const response = await this.livepeer.asset.get(assetId);
      const asset = response.asset;
      return {
        id: asset?.id || assetId,
        name: asset?.name || 'Unknown',
        playbackId: asset?.playbackId || '',
        status: (asset?.status?.toString() || 'unknown') as string,
        createdAt: asset?.createdAt || Date.now()
      };
    } catch (error) {
      throw error;
    }
  }
  
  async createAssetFromUrl(url: string, metadata: AssetMetadata): Promise<AssetDetails> {
    try {
      const response = await this.livepeer.asset.create({
        name: metadata.name || 'Untitled Stream',
        // Note: URL-based asset creation may require different payload structure
        // Check Livepeer SDK documentation for correct payload format
      } as any);
      
      // Handle different possible response structures
      const asset = (response as any)?.asset || response;
      return {
        id: asset?.id || '',
        name: asset?.name || metadata.name || 'Untitled Stream',
        status: (asset?.status?.toString() || 'processing') as string,
        playbackId: asset?.playbackId || '',
        createdAt: asset?.createdAt || Date.now()
      };
    } catch (error) {
      throw error;
    }
  }
  
  async deleteAsset(assetId: string) {
    try {
      await this.livepeer.asset.delete(assetId);
      return true;
    } catch (error) {
      console.error('Error deleting Livepeer asset:', error);
      return false;
    }
  }
  
  async getPlaybackUrl(assetId: string) {
    try {
      const asset = await this.getAssetDetails(assetId);
      
      if (!asset.playbackId) {
        throw new Error('No playback ID found for asset');
      }
      
      return `https://livepeercdn.studio/hls/${asset.playbackId}/index.m3u8`;
    } catch (error) {
      console.error('Error getting Livepeer playback URL:', error);
      throw error;
    }
  }
  
  // Live streaming methods
  async createStream(request: CreateStreamRequest): Promise<LivepeerStream> {
    try {
      const response = await this.livepeer.stream.create({
        name: request.name,
        record: request.record || false,
        profiles: request.profiles || [
          {
            name: '1080p',
            bitrate: 4000000,
            fps: 30,
            width: 1920,
            height: 1080
          },
          {
            name: '720p',
            bitrate: 2000000,
            fps: 30,
            width: 1280,
            height: 720
          },
          {
            name: '480p',
            bitrate: 1000000,
            fps: 30,
            width: 854,
            height: 480
          },
          {
            name: '360p',
            bitrate: 500000,
            fps: 30,
            width: 640,
            height: 360
          }
        ]
      });
      
      // Extract stream data from the correct response structure
      const streamData = (response as any)?.stream;
      
      if (!streamData) {
        console.error('Invalid Livepeer response:', response);
        throw new Error('Invalid response from Livepeer API');
      }
      
      return {
        id: streamData.id,
        name: streamData.name,
        streamKey: streamData.streamKey,
        playbackId: streamData.playbackId,
        isActive: streamData.isActive || false,
        lastSeen: streamData.lastSeen || 0,
        record: streamData.record || false,
        suspended: streamData.suspended || false,
        sourceSegments: streamData.sourceSegments || 0,
        transcodedSegments: streamData.transcodedSegments || 0,
        sourceSegmentsDuration: streamData.sourceSegmentsDuration || 0,
        transcodedSegmentsDuration: streamData.transcodedSegmentsDuration || 0,
        createdAt: streamData.createdAt || Date.now(),
        profiles: streamData.profiles || []
      } as LivepeerStream;
    } catch (error) {
      console.error('Error creating Livepeer stream:', error);
      throw error;
    }
  }

  async getStream(streamId: string): Promise<LivepeerStream | null> {
    try {
      const response = await this.livepeer.stream.get(streamId);
      // Map SDK response to our interface
      const streamData = (response as any)?.stream || (response as any)?.object || response;
      
      if (!streamData) {
        console.warn(`No stream data found for ID: ${streamId}`);
        return null;
      }
      
      return {
        id: streamData?.id || streamId,
        name: streamData?.name || '',
        streamKey: streamData?.streamKey || streamData?.stream_key || '',
        playbackId: streamData?.playbackId || streamData?.playback_id || '',
        isActive: streamData?.isActive || streamData?.is_active || false,
        lastSeen: streamData?.lastSeen || streamData?.last_seen || 0,
        record: streamData?.record || false,
        suspended: streamData?.suspended || false,
        sourceSegments: streamData?.sourceSegments || 0,
        transcodedSegments: streamData?.transcodedSegments || 0,
        sourceSegmentsDuration: streamData?.sourceSegmentsDuration || 0,
        transcodedSegmentsDuration: streamData?.transcodedSegmentsDuration || 0,
        createdAt: streamData?.createdAt || Date.now(),
        profiles: streamData?.profiles || []
      } as LivepeerStream;
    } catch (error: any) {
      // Handle 404 errors gracefully - the stream might have been deleted or expired
      if (error?.statusCode === 404 || error?.status === 404) {
        console.warn(`Stream with ID ${streamId} not found on Livepeer. It may have been deleted or expired.`);
        return null;
      }
      
      // Handle 429 rate limit errors gracefully
      if (error?.statusCode === 429 || error?.status === 429) {
        console.warn(`Rate limit exceeded for stream ${streamId}. Using cached/fallback data.`);
        // Return a basic stream object with minimal data to prevent breaking the UI
        return {
          id: streamId,
          name: 'Rate Limited Stream',
          streamKey: '',
          playbackId: '',
          isActive: false,
          lastSeen: 0,
          record: false,
          suspended: false,
          sourceSegments: 0,
          transcodedSegments: 0,
          sourceSegmentsDuration: 0,
          transcodedSegmentsDuration: 0,
          createdAt: Date.now(),
          profiles: []
        } as LivepeerStream;
      }
      
      // Handle network errors (fetch failed, ECONNRESET, etc.)
      if (error?.code === 'ECONNRESET' || error?.message?.includes('fetch failed') || error?.name === 'TypeError') {
        console.warn(`Network error for stream ${streamId}:`, error.message);
        return null;
      }
      
      // Handle other HTTP errors gracefully
      if (error?.statusCode >= 400 && error?.statusCode < 500) {
        console.warn(`Client error ${error.statusCode} for stream ${streamId}:`, error.message);
        return null;
      }
      
      console.error(`Error fetching Livepeer stream ${streamId}:`, error);
      // Return null instead of throwing to prevent breaking the caller
      return null;
    }
  }

  async updateStream(streamId: string, updates: Partial<CreateStreamRequest>): Promise<LivepeerStream> {
    try {
      // Use proper SDK parameters - stream ID and updates object
      const response = await this.livepeer.stream.update(streamId as any, updates as any);
      // Map SDK response to our interface
      const streamData = (response as any)?.stream || (response as any)?.object || response;
      return {
        id: streamData?.id || streamId,
        name: streamData?.name || '',
        streamKey: streamData?.streamKey || streamData?.stream_key || '',
        playbackId: streamData?.playbackId || streamData?.playback_id || '',
        isActive: streamData?.isActive || streamData?.is_active || false,
        lastSeen: streamData?.lastSeen || streamData?.last_seen || 0,
        record: streamData?.record || false,
        suspended: streamData?.suspended || false,
        sourceSegments: streamData?.sourceSegments || streamData?.source_segments || 0,
        transcodedSegments: streamData?.transcodedSegments || streamData?.transcoded_segments || 0,
        sourceSegmentsDuration: streamData?.sourceSegmentsDuration || streamData?.source_segments_duration || 0,
        transcodedSegmentsDuration: streamData?.transcodedSegmentsDuration || streamData?.transcoded_segments_duration || 0,
        createdAt: streamData?.createdAt || streamData?.created_at || Date.now(),
        profiles: streamData?.profiles || []
      } as LivepeerStream;
    } catch (error) {
      console.error('Error updating Livepeer stream:', error);
      throw error;
    }
  }

  async deleteStream(streamId: string): Promise<boolean> {
    try {
      await this.livepeer.stream.delete(streamId);
      return true;
    } catch (error) {
      console.error('Error deleting Livepeer stream:', error);
      return false;
    }
  }

  async getStreamPlaybackUrl(playbackId: string): Promise<string> {
    return `https://livepeercdn.studio/hls/${playbackId}/index.m3u8`;
  }

  async getStreamIngestUrl(streamKey: string): Promise<string> {
    return `rtmp://rtmp.livepeer.com/live/${streamKey}`;
  }

  async suspendStream(streamId: string): Promise<boolean> {
    try {
      
      // First try to update the stream to stop recording and suspend it
      try {
        await this.livepeer.stream.update(streamId as any, {
          record: false,
          suspended: true
        } as any);
        return true;
      } catch (updateError) {
        console.warn('Stream update failed, trying to delete:', updateError);
        
        // If update fails, try to delete the stream as fallback
        await this.livepeer.stream.delete(streamId);
        return true;
      }
    } catch (error) {
      console.error('Error stopping Livepeer stream:', error);
      
      // Log more details about the error
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack
        });
      }
      
      // Return false to indicate stopping failed, but database can still be updated
      return false;
    }
  }

  async resumeStream(streamId: string): Promise<boolean> {
    try {
      await this.livepeer.stream.update(streamId as any, { suspended: false } as any);
      return true;
    } catch (error) {
      console.error('Error resuming Livepeer stream:', error);
      return false;
    }
  }

  // Get all streams for a user
  async getStreams(): Promise<LivepeerStream[]> {
    try {
      const response = await this.livepeer.stream.getAll();
      return response.data as LivepeerStream[];
    } catch (error) {
      console.error('Error fetching Livepeer streams:', error);
      throw error;
    }
  }

  // Check if a stream is currently live
  async isStreamLive(streamId: string): Promise<boolean> {
    try {
      if (!streamId) {
        return false;
      }

      // Add timeout to prevent hanging requests
      const timeoutPromise = new Promise<LivepeerStream | null>((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 3000); // 3 second timeout
      });

      const streamPromise = this.getStream(streamId);
      const stream = await Promise.race([streamPromise, timeoutPromise]);
      
      if (!stream) {
        return false;
      }
      
      // If we got a rate-limited response, assume stream is not live
      if (stream.name === 'Rate Limited Stream') {
        return false;
      }
      
      // Check multiple indicators to determine if stream is really live
      // 1. isActive flag
      const isActive = stream.isActive === true;
      
      // 2. lastSeen timestamp - if the stream was active in the last 2 minutes
      const recentActivity = stream.lastSeen ? 
        (Date.now() - stream.lastSeen) < 1000 * 60 * 2 : false;
        
      // 3. Has sourceSegments (meaning data is being processed)
      const hasSegments = (stream.sourceSegments || 0) > 0;
      
      // Stream is considered live if it's marked active and either has recent activity or segments
      return isActive && (recentActivity || hasSegments);
    } catch (error) {
      console.warn(`Error checking if stream is live for ${streamId}:`, error);
      // Return false instead of throwing to prevent breaking the caller
      return false;
    }
  }

  // Get viewer count for a stream (note: this is an estimate)
  async getStreamViewerCount(streamId: string): Promise<number> {
    try {
      const stream = await this.getStream(streamId);
      
      if (!stream) {
        return 0;
      }
      
      // Livepeer doesn't provide viewer count directly, so we use sourceSegments as a rough indicator
      // that the stream is active and being viewed
      return stream.isActive ? (stream.sourceSegments || 0) : 0;
    } catch (error) {
      console.error('Error getting stream viewer count:', error);
      return 0;
    }
  }

  // Check stream health (for diagnostics)
  async getStreamHealth(streamId: string): Promise<any> {
    try {
      // First get the stream details
      const stream = await this.getStream(streamId);
      
      if (!stream) {
        return { error: 'Stream not found' };
      }
      
      // The Livepeer SDK doesn't have a direct getSession method in the current version
      // So we'll construct a health report from available stream data
      return {
        isActive: stream.isActive,
        lastSeen: stream.lastSeen,
        sourceSegments: stream.sourceSegments,
        transcodedSegments: stream.transcodedSegments,
        suspended: stream.suspended,
        health: stream.isActive ? 'healthy' : 'offline'
      };
    } catch (error) {
      console.error('Error getting stream health:', error);
      return { error: 'Failed to get stream health' };
    }
  }
}

// Export singleton instance
export const livepeerProvider = new LivepeerProvider();