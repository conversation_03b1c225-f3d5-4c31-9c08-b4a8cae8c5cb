import { StorageProvider } from './types';
import { livepeerProvider, VideoProvider } from './livepeer';

// Factory function to get the appropriate provider
export function getVideoProvider(provider: StorageProvider): VideoProvider {
  switch (provider) {
    case StorageProvider.LIVEPEER:
      return livepeerProvider;
    default:
      throw new Error(`Unsupported video provider: ${provider}`);
  }
}

// Helper to get the default provider based on environment config
export function getDefaultProvider(): VideoProvider {
  const defaultProvider = process.env.DEFAULT_VIDEO_PROVIDER as StorageProvider || StorageProvider.LIVEPEER;
  return getVideoProvider(defaultProvider);
}

// Re-export the VideoProvider interface and types
export type { VideoProvider } from './livepeer';
export { StorageProvider, StreamStatus } from './types'; 