/**
 * Core types for video providers without storage dependencies
 */

// Video storage providers
export enum StorageProvider {
  LIVEPEER = 'livepeer'
}

// Stream status
export enum StreamStatus {
  LIVE = 'live',
  PROCESSING = 'processing',
  ARCHIVED = 'archived',
  ENDED = 'ended'
}

// Stream metadata interface
export interface StreamMetadata {
  id?: string;
  title: string;
  description?: string;
  creator_id: string;
  thumbnail_url?: string;
  storage_provider: StorageProvider;
  storage_id?: string;
  playback_url?: string;
}
