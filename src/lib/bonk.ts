/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { 
  PublicKey, 
  Transaction,
  LAMPORTS_PER_SOL,
  TransactionInstruction
} from '@solana/web3.js';
import { 
  createTransferInstruction,
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  TOKEN_PROGRAM_ID
} from '@solana/spl-token';
import { getSolanaConnection, getSolanaConnectionWithFallback } from './solana';

// BONK token constants - Production mainnet
export const BONK_MINT_ADDRESS = new PublicKey('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263'); // Official BONK mainnet address
export const BONK_DECIMALS = 5;
// Platform wallet with validation - this should be set in environment variables
export const PLATFORM_WALLET_ADDRESS = (() => {
  const walletAddress = process.env.NEXT_PUBLIC_PLATFORM_WALLET_ADDRESS || process.env.PLATFORM_WALLET_ADDRESS;
  
  // In production, this must be set and validated
  if (!walletAddress) {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('PLATFORM_WALLET_ADDRESS must be set in production environment');
    }
    return new PublicKey('********************************'); // System program ID as fallback
  }
  
  try {
    const publicKey = new PublicKey(walletAddress);
    
    // Additional validation: ensure it's not the system program or other known addresses
    const systemProgram = '********************************';
    if (walletAddress === systemProgram) {
      throw new Error('Platform wallet cannot be the system program ID');
    }
    
    return publicKey;
  } catch (error) {
    throw new Error(`Invalid PLATFORM_WALLET_ADDRESS: ${(error as Error).message}`);
  }
})();

// Price cache with longer duration to reduce API calls
const priceCache: { 
  bonk?: { price: number; timestamp: number };
  sol?: { price: number; timestamp: number };
} = {};
const CACHE_DURATION = 60000; // 60 seconds

/**
 * Fetch BONK and SOL prices from multiple sources
 */
export async function fetchCryptoPrices(): Promise<{ bonk: number; sol: number }> {
  const now = Date.now();
  
  // Check cache first
  const bonkCached = priceCache.bonk && (now - priceCache.bonk.timestamp) < CACHE_DURATION;
  const solCached = priceCache.sol && (now - priceCache.sol.timestamp) < CACHE_DURATION;
  
  if (bonkCached && solCached) {
    return { 
      bonk: priceCache.bonk!.price, 
      sol: priceCache.sol!.price 
    };
  }
  
  try {
    
    // Try CoinGecko first
    const response = await fetch(
      'https://api.coingecko.com/api/v3/simple/price?ids=bonk,solana&vs_currencies=usd'
    );
    
    if (response.ok) {
      const data = await response.json();
      const bonkPrice = data.bonk?.usd || 0;
      const solPrice = data.solana?.usd || 0;
      
      if (bonkPrice > 0 && solPrice > 0) {
        priceCache.bonk = { price: bonkPrice, timestamp: now };
        priceCache.sol = { price: solPrice, timestamp: now };
        
        return { bonk: bonkPrice, sol: solPrice };
      }
    }
    
    // Fallback: try individual APIs
    const [bonkResponse, solResponse] = await Promise.allSettled([
      fetch('https://api.coingecko.com/api/v3/simple/price?ids=bonk&vs_currencies=usd'),
      fetch('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd')
    ]);
    
    let bonkPrice = 0;
    let solPrice = 0;
    
    if (bonkResponse.status === 'fulfilled' && bonkResponse.value.ok) {
      const bonkData = await bonkResponse.value.json();
      bonkPrice = bonkData.bonk?.usd || 0;
    }
    
    if (solResponse.status === 'fulfilled' && solResponse.value.ok) {
      const solData = await solResponse.value.json();
      solPrice = solData.solana?.usd || 0;
    }
    
    if (bonkPrice > 0) {
      priceCache.bonk = { price: bonkPrice, timestamp: now };
    }
    if (solPrice > 0) {
      priceCache.sol = { price: solPrice, timestamp: now };
    }
    
    return { bonk: bonkPrice, sol: solPrice };
    
  } catch (error) {
    
    // Return cached prices if available, otherwise default values
    return {
      bonk: priceCache.bonk?.price || 0,
      sol: priceCache.sol?.price || 0
    };
  }
}

/**
 * Convert BONK amount to fiat (USD or EUR)
 */
export async function convertBonkToFiat(bonkAmount: number, currency: 'USD' | 'EUR' = 'USD'): Promise<number> {
  // Use cached price if available and recent (within 2 minutes)
  const now = Date.now();
  if (priceCache.bonk && (now - priceCache.bonk.timestamp) < 120000) {
    const bonkPrice = priceCache.bonk.price;
    if (bonkPrice <= 0) return 0;
    const usdValue = bonkAmount * bonkPrice;
    return currency === 'EUR' ? usdValue * 0.85 : usdValue; // Simple EUR conversion
  }
  
  const { bonk: bonkPrice } = await fetchCryptoPrices();
  
  if (bonkPrice <= 0) return 0;
  
  const usdValue = bonkAmount * bonkPrice;
  
  if (currency === 'EUR') {
    // Simple EUR conversion (you could fetch real exchange rates)
    return usdValue * 0.85; // Approximate EUR rate
  }
  
  return usdValue;
}

/**
 * Convert SOL amount to fiat (USD or EUR)
 */
export async function convertSolToFiat(solAmount: number, currency: 'USD' | 'EUR' = 'USD'): Promise<number> {
  // Use cached price if available and recent (within 2 minutes)
  const now = Date.now();
  if (priceCache.sol && (now - priceCache.sol.timestamp) < 120000) {
    const solPrice = priceCache.sol.price;
    if (solPrice <= 0) return 0;
    const usdValue = solAmount * solPrice;
    return currency === 'EUR' ? usdValue * 0.85 : usdValue; // Simple EUR conversion
  }
  
  const { sol: solPrice } = await fetchCryptoPrices();
  
  if (solPrice <= 0) return 0;
  
  const usdValue = solAmount * solPrice;
  
  if (currency === 'EUR') {
    return usdValue * 0.85; // Approximate EUR rate
  }
  
  return usdValue;
}

/**
 * Get SOL balance for a wallet
 */
export async function getSolBalance(walletAddress: PublicKey): Promise<number> {
  try {
    
    const connection = getSolanaConnection();
    const balance = await connection.getBalance(walletAddress);
    const solBalance = balance / LAMPORTS_PER_SOL;
    
    return solBalance;
    
  } catch (error: any) {
    return 0;
  }
}

/**
 * Get BONK token balance with improved reliability
 */
export async function getBonkBalance(walletAddress: PublicKey): Promise<number> {
  
  try {
    // Try to get a working connection with fallback logic
    let connection;
    try {
      connection = await getSolanaConnectionWithFallback();
    } catch (connectionError) {
      connection = getSolanaConnection();
    }
    
    // Use the more reliable method: getParsedTokenAccountsByOwner
    
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
      walletAddress,
      { mint: BONK_MINT_ADDRESS }
    );
    
    
    if (tokenAccounts.value.length === 0) {
      return 0;
    }
    
    // Sum up all BONK balances across accounts
    let totalBalance = 0;
    for (const tokenAccount of tokenAccounts.value) {
      const accountData = tokenAccount.account.data.parsed.info;
      const accountBalance = parseFloat(accountData.tokenAmount.uiAmount || '0');
      totalBalance += accountBalance;
      
    }
    
    return totalBalance;
    
  } catch (error: any) {
    if (error.message?.includes('Failed to fetch') || error.message?.includes('net::ERR_CONNECTION_TIMED_OUT')) {
    } else if (error.message?.includes('403')) {
    }

    return 0;
  }
}

/**
 * Check if wallet has sufficient BONK balance for tip
 */
export async function checkSufficientBalance(
  walletAddress: PublicKey, 
  amount: number
): Promise<{ sufficient: boolean; balance: number; required: number }> {
  const balance = await getBonkBalance(walletAddress);
  const required = amount;
  
  return {
    sufficient: balance >= required,
    balance,
    required
  };
}

/**
 * Calculate platform fee split for a BONK token tip (5% platform, 95% creator)
 * 
 * @param amount Amount of BONK tokens
 * @returns Object containing platform fee, creator amount and percentage
 */
export function calculateTipPlatformFee(amount: number) {
  const feePercentage = 5; // 5% platform fee for tips
  const platformFee = Math.floor((amount * feePercentage) / 100);
  const creatorAmount = amount - platformFee;
  
  return {
    platformFeePercentage: feePercentage,
    platformFeeAmount: platformFee,
    creatorAmount
  };
}

/**
 * Calculate platform fee split for NFT moment capture (25% platform, 75% creator)
 * 
 * @param amount Amount of BONK tokens
 * @returns Object containing platform fee, creator amount and percentage
 */
export function calculateNftPlatformFee(amount: number) {
  const feePercentage = 25; // 25% platform fee for NFT moments
  const platformFee = Math.floor((amount * feePercentage) / 100);
  const creatorAmount = amount - platformFee;
  
  return {
    platformFeePercentage: feePercentage,
    platformFeeAmount: platformFee,
    creatorAmount
  };
}

/**
 * @deprecated Use calculateTipPlatformFee or calculateNftPlatformFee instead
 */
export function calculatePlatformFee(amount: number) {
  return calculateTipPlatformFee(amount);
}

/**
 * Create a transaction for sending BONK tokens with platform fee
 * 
 * @param amount Amount of BONK tokens
 * @param fromWallet Sender's wallet public key
 * @param creatorWallet Creator's wallet public key
 * @returns Transaction and fee details
 */
export async function createBonkTipTransaction(
  amount: number,
  fromWallet: PublicKey,
  creatorWallet: PublicKey
) {
  // Validate that sender is not trying to tip themselves
  if (fromWallet.equals(creatorWallet)) {
    throw new Error('You cannot tip yourself! Please select a different creator to support.');
  }
  
  // Validate that platform wallet is different from sender
  if (fromWallet.equals(PLATFORM_WALLET_ADDRESS)) {
    throw new Error('Invalid transaction: Platform wallet cannot send tips to itself');
  }
  
  // Validate that platform wallet is properly configured
  if (PLATFORM_WALLET_ADDRESS.toString() === '********************************') {
  }
  
  // Convert amount to BONK token units (with decimals)
  const tokenAmount = Math.floor(amount * Math.pow(10, BONK_DECIMALS));
  
  // Calculate fee split (5% platform, 95% creator for tips)
  const { platformFeePercentage, platformFeeAmount, creatorAmount } = calculateTipPlatformFee(tokenAmount);
  
  // Additional validation: warn if creator and platform are the same (this is okay but noteworthy)
  if (creatorWallet.equals(PLATFORM_WALLET_ADDRESS)) {
  }
  
  // Get connection
  const connection = getSolanaConnection();
  
  // Create a new transaction
  const transaction = new Transaction();
  
  // Get token accounts
  const fromTokenAccount = await getAssociatedTokenAddress(BONK_MINT_ADDRESS, fromWallet);
  const creatorTokenAccount = await getAssociatedTokenAddress(BONK_MINT_ADDRESS, creatorWallet);
  const platformTokenAccount = await getAssociatedTokenAddress(BONK_MINT_ADDRESS, PLATFORM_WALLET_ADDRESS);
  
  // Check if creator has a token account, if not create one
  try {
    await connection.getTokenAccountBalance(creatorTokenAccount);
  } catch {
    // Token account doesn't exist, create it
    transaction.add(
      createAssociatedTokenAccountInstruction(
        fromWallet, // payer
        creatorTokenAccount, // ata
        creatorWallet, // owner
        BONK_MINT_ADDRESS // mint
      )
    );
  }
  
  // Check if platform has a token account, if not create one (only if different from creator)
  if (!creatorWallet.equals(PLATFORM_WALLET_ADDRESS)) {
    try {
      await connection.getTokenAccountBalance(platformTokenAccount);
    } catch (error) {
      // Token account doesn't exist, create it
      transaction.add(
        createAssociatedTokenAccountInstruction(
          fromWallet, // payer
          platformTokenAccount, // ata
          PLATFORM_WALLET_ADDRESS, // owner
          BONK_MINT_ADDRESS // mint
        )
      );
    }
  }
  
  
  // Add instruction to send to creator (97.5%)
  transaction.add(
    createTransferInstruction(
      fromTokenAccount, // source
      creatorTokenAccount, // destination
      fromWallet, // owner
      creatorAmount, // amount
      [], // multiSigners
      TOKEN_PROGRAM_ID // programId
    )
  );
  
  // Only add platform fee transfer if platform wallet is different from creator
  if (!creatorWallet.equals(PLATFORM_WALLET_ADDRESS)) {
    
    // Add instruction to send platform fee (5%)
    transaction.add(
      createTransferInstruction(
        fromTokenAccount, // source
        platformTokenAccount, // destination
        fromWallet, // owner
        platformFeeAmount, // amount
        [], // multiSigners
        TOKEN_PROGRAM_ID // programId
      )
    );
  } else {
    // If creator and platform are the same, add the platform fee to creator amount
    transaction.add(
      createTransferInstruction(
        fromWallet, // source
        creatorTokenAccount, // destination
        fromWallet, // owner
        platformFeeAmount, // amount (additional platform fee goes to creator)
        [], // multiSigners
        TOKEN_PROGRAM_ID // programId
      )
    );
  }
  
  // Add a memo instruction to make the transaction purpose clear in the wallet
  const memoText = creatorWallet.equals(PLATFORM_WALLET_ADDRESS) 
    ? `BONK Tip: ${amount} BONK to creator (platform = creator)`
    : `BONK Tip: ${amount} BONK (${(creatorAmount / Math.pow(10, BONK_DECIMALS)).toFixed(2)} to creator, ${(platformFeeAmount / Math.pow(10, BONK_DECIMALS)).toFixed(2)} platform fee)`;
    
  const memoInstruction = new TransactionInstruction({
    keys: [],
    programId: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'), // Memo program
    data: Buffer.from(memoText, 'utf8')
  });
  
  transaction.add(memoInstruction);
  
  // Get a recent blockhash
  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = fromWallet;
  
  
  return {
    transaction,
    platformFeePercentage,
    platformFeeAmount,
    creatorAmount
  };
}

/**
 * Get BONK to SOL conversion rate
 */
export async function getBonkToSolRate(): Promise<number> {
  try {
    const { bonk: bonkPrice, sol: solPrice } = await fetchCryptoPrices();
    
    if (bonkPrice > 0 && solPrice > 0) {
      // Calculate how many BONK = 1 SOL
      const bonkPerSol = solPrice / bonkPrice;
      return bonkPerSol;
    }
    
    // Fallback rate: 1 SOL ≈ 2,500,000 BONK
    return 2500000;
  } catch (error) {
    return 2500000; // Fallback rate
  }
}

/**
 * Create a BONK-based NFT mint transaction that includes BONK payment to creator and platform
 * This follows the same pattern as the tip system but for NFT minting
 */
export async function createBonkNftMintTransaction(
  userWalletAddress: string,
  creatorWalletAddress: string,
  bonkAmount: number
): Promise<{
  transaction: Transaction;
  platformFeePercentage: number;
  platformFeeAmount: number;
  creatorAmount: number;
}> {
  try {
    const userWallet = new PublicKey(userWalletAddress);
    const creatorWallet = new PublicKey(creatorWalletAddress);
    
    // Validate inputs
    if (!userWallet || !creatorWallet) {
      throw new Error('Invalid wallet addresses provided');
    }
    
    if (bonkAmount <= 0) {
      throw new Error('BONK amount must be positive');
    }
    
    // Validate that platform wallet is different from user
    if (userWallet.equals(PLATFORM_WALLET_ADDRESS)) {
      throw new Error('Invalid transaction: Platform wallet cannot mint NFTs for itself');
    }
    
    // Validate that platform wallet is properly configured
    if (PLATFORM_WALLET_ADDRESS.toString() === '********************************') {
    }
    
    // Convert amount to BONK token units (with decimals)
    const tokenAmount = Math.floor(bonkAmount * Math.pow(10, BONK_DECIMALS));
    
    // Calculate fee split (25% platform, 75% creator for NFT moments)
    const { platformFeePercentage, platformFeeAmount, creatorAmount } = calculateNftPlatformFee(tokenAmount);

    
    // Get connection
    const connection = getSolanaConnection();
    
    // Create a new transaction
    const transaction = new Transaction();
    
    // Get token accounts
    const userTokenAccount = await getAssociatedTokenAddress(BONK_MINT_ADDRESS, userWallet);
    const creatorTokenAccount = await getAssociatedTokenAddress(BONK_MINT_ADDRESS, creatorWallet);
    const platformTokenAccount = await getAssociatedTokenAddress(BONK_MINT_ADDRESS, PLATFORM_WALLET_ADDRESS);
    
    
    // Check if creator has a token account, if not create one
    try {
      await connection.getTokenAccountBalance(creatorTokenAccount);
    } catch {
      // Don't create it here to save transaction space - SPL transfers will fail gracefully with better error
    }
    
    // Check if platform has a token account, if not log but don't create (to save space)
    if (!creatorWallet.equals(PLATFORM_WALLET_ADDRESS)) {
      try {
        await connection.getTokenAccountBalance(platformTokenAccount);
      } catch {
        // Don't create it here to save transaction space
      }
    }
    
    
    // Add instruction to send to creator (majority)
    transaction.add(
      createTransferInstruction(
        userTokenAccount, // source
        creatorTokenAccount, // destination
        userWallet, // owner
        creatorAmount, // amount
        [], // multiSigners
        TOKEN_PROGRAM_ID // programId
      )
    );
    
    // Only add platform fee transfer if platform wallet is different from creator
    if (!creatorWallet.equals(PLATFORM_WALLET_ADDRESS)) {
      
      // Add instruction to send platform fee
      transaction.add(
        createTransferInstruction(
          userTokenAccount, // source
          platformTokenAccount, // destination
          userWallet, // owner
          platformFeeAmount, // amount
          [], // multiSigners
          TOKEN_PROGRAM_ID // programId
        )
      );
    } else {
      // If creator and platform are the same, add the platform fee to creator amount
      transaction.add(
        createTransferInstruction(
          userTokenAccount, // source
          creatorTokenAccount, // destination
          userWallet, // owner
          platformFeeAmount, // amount (additional platform fee goes to creator)
          [], // multiSigners
          TOKEN_PROGRAM_ID // programId
        )
      );
    }
    
    // Get a recent blockhash
    const { blockhash } = await connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = userWallet;
    
    
    return {
      transaction,
      platformFeePercentage,
      platformFeeAmount,
      creatorAmount
    };
  } catch (error) {
    throw new Error(`Failed to create BONK NFT transaction: ${(error as Error).message}`);
  }
} 