// Authentication storage keys
const AUTH_STORAGE_KEYS = {
  signature: 'walletAuthSignature',
  message: 'walletAuthMessage', 
  address: 'walletAddress',
  expiry: 'walletAuthExpiry'
} as const;

// 24 hour authentication validity
const AUTH_VALIDITY_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export interface WalletAuthData {
  signature: string;
  message: string;
  walletAddress: string;
  expiresAt: number;
}

/**
 * Get stored authentication data for the current wallet
 */
export function getStoredAuth(walletAddress?: string): WalletAuthData | null {
  try {
    if (typeof window === 'undefined') return null;
    
    const signature = localStorage.getItem(AUTH_STORAGE_KEYS.signature);
    const message = localStorage.getItem(AUTH_STORAGE_KEYS.message);
    const address = localStorage.getItem(AUTH_STORAGE_KEYS.address);
    const expiry = localStorage.getItem(AUTH_STORAGE_KEYS.expiry);
    
    // Check if all required data exists
    if (!signature || !message || !address || !expiry) {
      return null;
    }
    
    // Check if it's for the current wallet (if specified)
    if (walletAddress && address !== walletAddress) {
      return null;
    }
    
    const expiresAt = parseInt(expiry);
    
    // Check if expired
    if (Date.now() > expiresAt) {
      clearStoredAuth();
      return null;
    }
    
    return {
      signature,
      message,
      walletAddress: address,
      expiresAt
    };
  } catch (error) {
    console.error('Error getting stored auth:', error);
    return null;
  }
}

/**
 * Store authentication data
 */
export function storeAuth(authData: Omit<WalletAuthData, 'expiresAt'>): void {
  try {
    if (typeof window === 'undefined') return;
    
    const expiresAt = Date.now() + AUTH_VALIDITY_DURATION;
    
    localStorage.setItem(AUTH_STORAGE_KEYS.signature, authData.signature);
    localStorage.setItem(AUTH_STORAGE_KEYS.message, authData.message);
    localStorage.setItem(AUTH_STORAGE_KEYS.address, authData.walletAddress);
    localStorage.setItem(AUTH_STORAGE_KEYS.expiry, expiresAt.toString());
  } catch (error) {
    console.error('Error storing auth:', error);
  }
}

/**
 * Clear stored authentication data
 */
export function clearStoredAuth(): void {
  try {
    if (typeof window === 'undefined') return;
    
    localStorage.removeItem(AUTH_STORAGE_KEYS.signature);
    localStorage.removeItem(AUTH_STORAGE_KEYS.message);
    localStorage.removeItem(AUTH_STORAGE_KEYS.address);
    localStorage.removeItem(AUTH_STORAGE_KEYS.expiry);
    
    // Also clear old page-specific auth data
    localStorage.removeItem('collectionAuthSignature');
    localStorage.removeItem('collectionAuthExpires');
    localStorage.removeItem('profileAuthSignature');
    localStorage.removeItem('profileAuthExpires');
    localStorage.removeItem('creatorAuthSignature');
    localStorage.removeItem('creatorAuthExpires');
  } catch (error) {
    console.error('Error clearing auth:', error);
  }
}

/**
 * Check if current authentication is valid for a wallet
 */
export function isAuthValid(walletAddress: string): boolean {
  const authData = getStoredAuth(walletAddress);
  return authData !== null;
}

/**
 * Create authentication headers for API requests
 */
export function createAuthHeaders(walletAddress: string): Record<string, string> {
  const authData = getStoredAuth(walletAddress);
  
  if (!authData) {
    throw new Error('No valid authentication found');
  }
  
  return {
    'Authorization': `Wallet ${walletAddress}`,
    'x-wallet-signature': authData.signature,
    'x-wallet-message': authData.message
  };
}

/**
 * Create a new authentication message
 */
export function createAuthMessage(walletAddress: string): string {
  return `bonkstream authentication ${walletAddress} ${Date.now()}`;
}

/**
 * Authenticate a wallet by requesting a signature
 * This is a standalone function that pages can use for authentication
 */
export async function authenticateWallet(walletAddress: string): Promise<boolean> {
  try {
    // Check if already authenticated
    if (isAuthValid(walletAddress)) {
      return true;
    }

    // We need to get the wallet adapter from the current context
    // This is a fallback function - ideally should be called from Header
    throw new Error('Authentication should be handled by the Header component. Please ensure wallet is connected and try accessing the page again.');
    
  } catch (error) {
    console.error('Wallet authentication error:', error);
    return false;
  }
} 