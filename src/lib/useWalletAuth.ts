import { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  authenticate: () => Promise<boolean>;
}

export function useWalletAuth(): AuthState {
  const { connected, publicKey, signMessage } = useWallet();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Safely get wallet address string for dependency arrays
  const walletAddress = publicKey?.toString() || null;

  // Check authentication status when wallet changes
  useEffect(() => {
    const checkAuth = async () => {
      if (!connected || !walletAddress) {
        setIsAuthenticated(false);
        return;
      }

      // Check if we have valid stored authentication
      try {
        const { isAuthValid } = await import('@/lib/wallet-auth');
        
        if (isAuthValid(walletAddress)) {
          setIsAuthenticated(true);
        } else {
          setIsAuthenticated(false);
        }
      } catch (err) {
        console.error('Error checking auth:', err);
        setIsAuthenticated(false);
      }
    };

    checkAuth();
  }, [connected, walletAddress]);

  // Function to trigger authentication
  const authenticate = async (): Promise<boolean> => {
    if (!connected || !publicKey || !signMessage || isLoading) {
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const walletAddress = publicKey.toString();

      // Check if already authenticated
      const { isAuthValid } = await import('@/lib/wallet-auth');
      if (isAuthValid(walletAddress)) {
        setIsAuthenticated(true);
        setIsLoading(false);
        return true;
      }

      // Create authentication message
      const { createAuthMessage, storeAuth } = await import('@/lib/wallet-auth');
      const message = createAuthMessage(walletAddress);

      // Request signature from wallet
      const messageBytes = new TextEncoder().encode(message);
      const signature = await signMessage(messageBytes);

      if (!signature) {
        throw new Error('Failed to sign authentication message');
      }

      // Convert signature to base64
      const signatureBase64 = Buffer.from(signature).toString('base64');

      // Store authentication
      storeAuth({
        signature: signatureBase64,
        message,
        walletAddress
      });

      setIsAuthenticated(true);
      setIsLoading(false);
      return true;
    } catch (err) {
      console.error('Authentication error:', err);
      setError('Authentication failed. Please try again.');
      setIsLoading(false);
      return false;
    }
  };

  return {
    isAuthenticated,
    isLoading,
    error,
    authenticate
  };
} 