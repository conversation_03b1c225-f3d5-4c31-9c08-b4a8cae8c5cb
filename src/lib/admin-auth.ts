import { NextRequest, NextResponse } from 'next/server';
import { checkAdminStatus, verifyWalletSignature } from '@/lib/auth';

/**
 * Shared function to verify admin authentication from request headers
 * Reduces code duplication across admin API routes
 */
export async function verifyAdminAuth(request: NextRequest): Promise<{ isValid: boolean; walletAddress?: string; error?: NextResponse }> {
  // Check admin authentication using wallet headers
  const authHeader = request.headers.get('authorization');
  const signatureHeader = request.headers.get('x-wallet-signature');
  const messageHeader = request.headers.get('x-wallet-message');
  
  if (!authHeader || !authHeader.startsWith('Wallet ') || !signatureHeader || !messageHeader) {
    return {
      isValid: false,
      error: NextResponse.json(
        { error: 'Unauthorized - Missing wallet authentication' },
        { status: 401 }
      )
    };
  }
  
  const walletAddress = authHeader.substring(7);
  
  // Verify signature
  const isSignatureValid = await verifyWalletSignature(walletAddress, signatureHeader, messageHeader);
  
  if (!isSignatureValid) {
    return {
      isValid: false,
      error: NextResponse.json(
        { error: 'Unauthorized - Invalid signature' },
        { status: 401 }
      )
    };
  }
  
  // Check admin status
  const isAdmin = await checkAdminStatus(walletAddress);
  
  if (!isAdmin) {
    return {
      isValid: false,
      error: NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      )
    };
  }

  return { isValid: true, walletAddress };
}
