'use client';

import React from 'react';
import { Link, usePathname } from '@/lib/i18n';
import { useTranslations } from 'next-intl';

export default function LegalNavigation() {
  const pathname = usePathname();
  const t = useTranslations('legal');
  
  const isActive = (path: string) => {
    return pathname === path || (pathname && pathname.startsWith(path)) 
      ? 'text-[#FFD700] border-[#FFD700]' 
      : 'text-white/70 border-transparent hover:text-white hover:border-white/30';
  };
  
  return (
    <nav className="mb-12">
      <div className="flex flex-wrap items-center justify-center gap-2 md:gap-6 border-b border-white/10 pb-4">
        <Link 
          href="/legal/terms" 
          className={`px-3 py-2 text-sm font-medium border-b-2 transition ${isActive('/legal/terms')}`}
        >
          {t('terms.title')}
        </Link>
        
        <Link 
          href="/legal/impressum" 
          className={`px-3 py-2 text-sm font-medium border-b-2 transition ${isActive('/legal/impressum')}`}
        >
          {t('impressum.title')}
        </Link>
        
        <Link 
          href="/legal/platform-fee" 
          className={`px-3 py-2 text-sm font-medium border-b-2 transition ${isActive('/legal/platform-fee')}`}
        >
          {t('platformFee.title')}
        </Link>
        
        <Link 
          href="/legal/privacy" 
          className={`px-3 py-2 text-sm font-medium border-b-2 transition ${isActive('/legal/privacy')}`}
        >
          {t('privacy.title')}
        </Link>
        
        <Link 
          href="/legal/cookies" 
          className={`px-3 py-2 text-sm font-medium border-b-2 transition ${isActive('/legal/cookies')}`}
        >
          {t('cookies.title')}
        </Link>
      </div>
    </nav>
  );
} 