'use client';

import { Link } from '@/lib/i18n';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

// Helper function for safer translations
function useTranslationsWithFallback(namespace: string) {
  try {
    return useTranslations(namespace);
  } catch (error) {
    console.error(`Error loading translations for ${namespace}:`, error);
    // Return a function that returns the key as fallback
    return (key: string) => key;
  }
}

export default function Footer() {
  // Use the proper namespace that matches our JSON structure with fallback
  const t = useTranslationsWithFallback('common');
  const legalT = useTranslationsWithFallback('legal');
  
  // Current year for copyright
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-b from-gray-900 via-gray-800 to-black text-white py-16">
      <div className="container mx-auto px-4">
        {/* Main Navigation Grid - 4 columns to prevent horizontal layout */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-16">
          
          {/* BONK Column - First column matching footer.png exactly */}
          <div className="md:col-span-1">
            <div className="flex items-center mb-6">
              {/* Real BONK Head from StandAloneHead.png */}
              <div className="w-16 h-16 mr-4 relative">
                <Image
                  src="/StandAloneHead.png"
                  alt="BONK Head"
                  width={64}
                  height={64}
                  className="w-[64px] h-[64px] object-contain"
                />
              </div>
              <div>
                <h3 className="text-2xl font-herborn font-black text-white uppercase tracking-wide">
                  <span className="text-[#FF0000]">BONK</span>STREAM
                </h3>
              </div>
            </div>
            
            {/* BONK links exactly like in footer.png */}
            <ul className="space-y-3 text-sm font-helvetica">
              <li>
                <a href="https://bonkcoin.com/labs" target="_blank" rel="noopener noreferrer" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  BONK Labs
                </a>
              </li>
              <li>
                <a href="https://bonkcoin.com/foundation" target="_blank" rel="noopener noreferrer" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  BONK Foundation
                </a>
              </li>
            </ul>
          </div>

          {/* Platform Section - Original bonkstream content */}
          <div>
            <h4 className="font-herborn text-white mb-6 uppercase tracking-wide text-sm font-black">Platform</h4>
            <ul className="space-y-3 text-sm font-helvetica">
              <li>
                <Link href="/streams" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  {t('footer.browseStreams')}
                </Link>
              </li>
              <li>
                <Link href="/discover" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  Discover
                </Link>
              </li>

              <li>
                <Link href="/collection" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  NFT Collection
                </Link>
              </li>
            </ul>
          </div>

          {/* Creators Section - Original bonkstream content */}
          <div>
            <h4 className="font-herborn text-white mb-6 uppercase tracking-wide text-sm font-black">Creators</h4>
            <ul className="space-y-3 text-sm font-helvetica">

              <li>
                <Link href="/creator/dashboard" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  Creator Dashboard
                </Link>
              </li>
              <li>
                <Link href="/go-live" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  Go Live
                </Link>
              </li>
              <li>
                <Link href="/legal/platform-fee" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  {t('footer.platformFee')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Support & Legal Section - Original bonkstream content */}
          <div>
            <h4 className="font-herborn text-white mb-6 uppercase tracking-wide text-sm font-black">Support & Legal</h4>
            <ul className="space-y-3 text-sm font-helvetica">
              <li>
                <Link href="/legal/terms" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  {legalT('terms.title') || 'Terms of Service'}
                </Link>
              </li>
              <li>
                <Link href="/legal/privacy" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  {legalT('privacy.title') || 'Privacy Policy'}
                </Link>
              </li>
              <li>
                <Link href="/legal/impressum" className="text-white/70 hover:text-white transition-colors duration-200 font-normal">
                  {t('footer.impressum') || 'Impressum'}
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        {/* Bottom Section with BONKSTREAM branding and social icons */}
        <div className="border-t border-white/20 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            
            {/* BONKSTREAM Brand and Copyright */}
            <div className="flex items-center space-x-3 mb-6 md:mb-0">
              <div>
                <p className="text-xs text-white/60 font-helvetica mb-1">
                  Stream. BONK. Mint
                </p>
                <p className="text-sm text-white font-helvetica font-medium">
                  &copy; {currentYear} <span className="font-herborn font-bold uppercase">BONKSTREAM</span>
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-xs text-white/60 font-helvetica">
                    Web3 streaming powered by BONK on
                  </p>
                  <div className="w-4 h-4 relative">
                    <Image
                      src="/solana-sol-logo.png"
                      alt="Solana"
                      width={16}
                      height={16}
                      className="object-contain"
                    />
                  </div>
                  <span className="text-xs text-white/60 font-helvetica">Solana</span>
                </div>
              </div>
            </div>
            
            {/* Social Media Icons Row */}
            <div className="flex space-x-4">
              <a href="https://x.com/bonkstream" target="_blank" rel="noopener noreferrer" className="text-white/60 hover:text-white transition-colors duration-200">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </a>             
              <a href="https://discord.gg/HkQMHSduuD" target="_blank" rel="noopener noreferrer" className="text-white/60 hover:text-white transition-colors duration-200">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.210.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.010c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.120.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.30zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418Z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
} 