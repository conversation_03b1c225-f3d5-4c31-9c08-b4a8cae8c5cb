'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
import { useWallet } from '@solana/wallet-adapter-react';
import LanguageSwitcher from '../language-switcher/LanguageSwitcher';
import { usePathname, Link, useRouter } from '@/lib/i18n';
import { defaultLocale, getUserLocale, getLocaleFromPath } from '@/lib/i18n';
import type { Locale } from '@/lib/i18n';
import supabase from '@/lib/supabase/client';
import Image from 'next/image';
import { WalletErrorBoundary } from '../wallet/WalletErrorBoundary';

function useTranslationsWithFallback(namespace: string) {
  try {
    return useTranslations(namespace);
  } catch (error) {
    return ((key: string) => key) as unknown as ReturnType<typeof useTranslations>;
  }
}

const WalletMultiButton = dynamic(
  () => import('@solana/wallet-adapter-react-ui').then(mod => mod.WalletMultiButton),
  { ssr: false }
);

const WalletButtonWithErrorHandling = () => (
  <WalletErrorBoundary>
    <WalletMultiButton />
  </WalletErrorBoundary>
);

export default function Header() {
  const wallet = useWallet();
  const router = useRouter();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  // Removed isCreator state since everyone can stream now
  const [authInProgress] = useState(false);
  const [isBroadcasting, setIsBroadcasting] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  // Use direct translation namespaces that match our JSON files with fallback
  const t = useTranslationsWithFallback('common');
  const pathname = usePathname();

  // Safely get wallet address string for dependency arrays
  const walletAddress = wallet?.publicKey?.toString() || null;

  // Handle wallet connection requests from other components
  useEffect(() => {
    const handleWalletConnectionRequest = () => {
      if (!wallet.connected) {
        // Open wallet selection modal
        const walletButton = document.querySelector('.wallet-adapter-button');
        if (walletButton instanceof HTMLElement) {
          walletButton.click();
        }
      }
    };

    window.addEventListener('requestWalletConnection', handleWalletConnectionRequest as EventListener);
    return () => {
      window.removeEventListener('requestWalletConnection', handleWalletConnectionRequest as EventListener);
    };
  }, [wallet.connected]);

  // Prefetch common routes to improve navigation performance - reduced to essential routes only
  useEffect(() => {
    if (typeof window !== 'undefined' && isAuthenticated) {
      // Only prefetch routes when actually authenticated to avoid unnecessary requests
      router.prefetch('/');
      router.prefetch('/creator/dashboard');
    }
  }, [router, isAuthenticated]);

  // Get the current locale more reliably using our utility functions
  const getCurrentLocale = useCallback((): Locale => {
    // 1. First try to get from cookie (client-side preference)
    const cookieLocale = getUserLocale();
    if (cookieLocale) return cookieLocale;
    
    // 2. Then try to extract from URL path using our utility function
    const pathLocale = getLocaleFromPath(pathname);
    if (pathLocale) return pathLocale;
    
    // 3. Fall back to default locale
    return defaultLocale;
  }, [pathname]);

  const currentLocale = getCurrentLocale();

  // Check if the user is an admin when wallet is connected
  const checkAdminStatus = useCallback(async () => {
    // Use current wallet state at the time of execution
    const currentWallet = wallet;
    if (!currentWallet.connected || !currentWallet.publicKey) {
      setIsAdmin(false);
      return;
    }
    
    try {
      // First try direct Supabase query (more reliable)
      const { data, error } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('wallet_address', currentWallet.publicKey.toString())
        .single();
      
      if (error) {
        // Don't log errors for non-existent profiles (PGRST116 = no rows)
        // This is normal for new users and not an actual error condition
        if (error.code !== 'PGRST116') {
          console.error('Error checking admin status:', error);
        }
        
        // Just set admin to false if error or no rows
        setIsAdmin(false);
        
        // If user has no profile yet, we should create one via the profile API
        if (error.code === 'PGRST116') {
          try {
            // Get current locale
            const currentLocale = getCurrentLocale();
            
            // For simple public profile creation, no signature needed
            await fetch(`/${currentLocale}/api/profile?wallet=${currentWallet.publicKey.toString()}`);
          } catch (createError) {
            console.error('Error creating profile:', createError);
          }
        }
      } else {
        // Set admin status based on profile data
        const profile = data as { is_admin: boolean } | null;
        setIsAdmin(!!profile?.is_admin);
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    }
  }, [getCurrentLocale, wallet]); // Only depend on stable functions

  // Simplified - everyone can be a creator now, so we don't need to check this



  useEffect(() => {
    checkAdminStatus();
    checkBroadcastingStatus(); // Check broadcasting status on mount
  }, [wallet.connected, walletAddress, checkAdminStatus]);

  // No longer need to check creator status since everyone can stream

  // Check if user is broadcasting (simple localStorage check)
  const checkBroadcastingStatus = () => {
    const isBroadcastingNow = localStorage.getItem('bonkstream_broadcasting') === 'true';
    setIsBroadcasting(isBroadcastingNow);
  };

  // Listen for localStorage changes to update immediately
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'bonkstream_broadcasting') {
        checkBroadcastingStatus();
      }
    };

    // Also listen for custom events (for same-tab changes)
    const handleBroadcastingChange = () => {
      checkBroadcastingStatus();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('bonkstream_broadcasting_changed', handleBroadcastingChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('bonkstream_broadcasting_changed', handleBroadcastingChange);
    };
  }, []);

  // Check broadcasting status when user is authenticated
  useEffect(() => {
    if (wallet.connected) {
      checkBroadcastingStatus(); // Check broadcasting status
      
      // Set up an interval to periodically check broadcasting status
      const interval = setInterval(() => {
        checkBroadcastingStatus();
      }, 30000); // Check every 30 seconds
      
      return () => clearInterval(interval);
    } else {
      setIsBroadcasting(false);
    }
  }, [wallet.connected]);

  useEffect(() => {
    const checkStoredAuth = async () => {
      if (wallet.connected && walletAddress) {
        const { getStoredAuth } = await import('@/lib/wallet-auth');

        const authData = getStoredAuth(walletAddress);

        if (authData) {
          setIsAuthenticated(true);
          await checkAdminStatus();
          return;
        }

        if (!isAuthenticated && !authInProgress) {
        }
      } else {
        setIsAuthenticated(false);
        setIsAdmin(false);
        setIsBroadcasting(false);
        
        // Clear authentication using centralized system
        const { clearStoredAuth } = await import('@/lib/wallet-auth');
        clearStoredAuth();
        localStorage.removeItem('bonkstream_broadcasting'); // Clear broadcasting flag
      }
    };
    
    checkStoredAuth();
  }, [wallet.connected, walletAddress, checkAdminStatus, isAuthenticated, authInProgress]);

  // Close mobile menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Don't close mobile menu when clicking the hamburger button
      if (event.target instanceof Element && event.target.closest('[data-mobile-menu-button]')) {
        return;
      }
      
      // Close mobile menu when clicking outside
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Prevent body scrolling when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [mobileMenuOpen]);

  // Cleanup broadcasting flag on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Only clear if user is not on go-live page
      if (!pathname.includes('/go-live')) {
        localStorage.removeItem('bonkstream_broadcasting');
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [pathname]);

  // Navigation items with fallback values in case translations are missing
  const navItems = {
    home: (t('nav.home') || 'Home').toUpperCase(),
    streams: (t('nav.streams') || 'Streams').toUpperCase(),
    discover: (t('nav.discover') || 'Discover').toUpperCase(),
    collection: (t('nav.collection') || 'Collection').toUpperCase(),
    profile: (t('nav.profile') || 'Profile').toUpperCase(),
    dashboard: (t('nav.dashboard') || 'Dashboard').toUpperCase(),
    admin: (t('nav.admin') || 'Admin').toUpperCase(),

    connectWallet: (t('nav.connect_wallet') || 'Connect').toUpperCase(),
    goLive: (t('nav.go_live') || 'Go Live').toUpperCase(),
    streamNow: (t('nav.stream_now') || 'Stream Now').toUpperCase(),
    live: (t('nav.live') || 'LIVE').toUpperCase()
  };



  return (
    <header className="bg-bonk-gradient-webpage border-b border-bonk-orange/20 shadow-bonk-lg relative z-50 pointer-events-auto">
      <div className="container mx-auto px-4 flex items-center justify-between h-20">
        {/* Logo and site name - reduced size */}
        <Link href="/" className="flex items-center space-x-3 z-10 group">
          <div className="w-10 h-10 relative">
            <Image
              src="/StandAloneHead.png"
              alt="BONK Head"
              width={40}
              height={40}
              className="object-contain group-hover:scale-110 transition-transform duration-300"
            />
          </div>
          <h1 className="text-2xl font-herborn font-black text-[#FF0000] tracking-wide transition-all duration-300 group-hover:scale-105">
            BONK<span className="text-white">STREAM</span>
          </h1>
        </Link>

        {/* Main navigation (desktop) */}
        <nav className="hidden md:flex items-center space-x-6 lg:space-x-8 relative z-10">
          <Link href="/" className="text-white font-herborn font-bold text-sm tracking-wide hover:text-bonk-yellow transition-colors duration-200">
            {navItems.home}
          </Link>
          <Link href="/streams" className="text-white font-herborn font-bold text-sm tracking-wide hover:text-bonk-yellow transition-colors duration-200">
            {navItems.streams}
          </Link>
          <Link href="/discover" className="text-white font-herborn font-bold text-sm tracking-wide hover:text-bonk-yellow transition-colors duration-200">
            {navItems.discover}
          </Link>
          
          {/* Only show authenticated user content */}
          {wallet.connected && (
            <>
              <Link href="/collection" className="text-white font-herborn font-bold text-sm tracking-wide hover:text-bonk-yellow transition-colors duration-200">
                {navItems.collection}
              </Link>
              <Link href="/creator/dashboard" className="text-white font-herborn font-bold text-sm tracking-wide hover:text-bonk-yellow transition-colors duration-200">
                {navItems.dashboard}
              </Link>
              {/* Show Admin link only if the user is an admin */}
              {isAdmin && (
                <Link href="/admin" className="text-bonk-yellow font-herborn font-bold text-sm tracking-wide hover:text-white transition-colors duration-200 bg-bonk-yellow/10 px-3 py-1 rounded-lg">
                  {navItems.admin}
                </Link>
              )}
            </>
          )}
        </nav>

        {/* Right side: stream now, language, wallet */}
        <div className="flex items-center space-x-2 sm:space-x-4 z-10">
          {/* Go Live / Live Button (for all authenticated users) */}
          {wallet.connected && (
            <Link
              href="/go-live"
              className={`hidden md:flex items-center gap-2 bonk-btn text-sm font-herborn font-black uppercase ${
                isBroadcasting ? 'bg-red-600 animate-pulse border-red-500' : ''
              }`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              {isBroadcasting ? navItems.live : navItems.goLive}
            </Link>
          )}

          {/* Language Switcher - hidden on small mobile screens */}
          <div className="hidden sm:block">
            <LanguageSwitcher currentLocale={currentLocale} />
          </div>

          {/* Wallet Connect Button - compact on mobile */}
          <div className="hidden sm:block">
            <WalletButtonWithErrorHandling />
          </div>

          {/* Mobile menu button - always visible on mobile with better spacing */}
          <button
            data-mobile-menu-button
            className="md:hidden p-3 text-white hover:text-bonk-yellow transition-colors duration-200 bg-black/20 rounded-lg border border-white/10"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu (slide-in drawer) */}
      <div
        ref={mobileMenuRef}
        className="fixed inset-0 z-[100] transform md:hidden -translate-x-full transition-transform duration-300 ease-in-out"
        style={{ transform: mobileMenuOpen ? 'translateX(0)' : 'translateX(-100%)' }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setMobileMenuOpen(false)}></div>
        
        {/* Drawer Content */}
        <div className="absolute inset-y-0 left-0 max-w-[320px] w-[85vw] shadow-bonk-2xl flex flex-col border-r border-bonk-orange/20 bg-bonk-gradient-orange">
          {/* Mobile Menu Header */}
          <div className="flex items-center justify-between p-4 border-b border-white/20">
            <Link href="/" className="flex items-center space-x-3" onClick={() => setMobileMenuOpen(false)}>
              <div className="w-8 h-8 relative">
                <Image
                  src="/StandAloneHead.png"
                  alt="BONK Head"
                  width={32}
                  height={32}
                  className="object-contain"
                />
              </div>
              <span className="text-2xl font-herborn font-black tracking-wide">
                <span className="text-[#FF0000]">BONK</span><span className="text-white">STREAM</span>
              </span>
            </Link>
            <button
              onClick={() => setMobileMenuOpen(false)}
              className="p-2 text-white hover:text-bonk-yellow transition-colors duration-200"
              aria-label="Close menu"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Mobile Menu Navigation Links */}
          <nav className="flex-1 overflow-y-auto py-4">
            <Link
              href="/"
              className="block px-4 py-3 text-white font-herborn font-bold tracking-wide hover:bg-bonk-orange/20 hover:text-bonk-yellow border-l-4 border-transparent hover:border-bonk-yellow transition-all duration-200"
              onClick={() => setMobileMenuOpen(false)}
            >
              {navItems.home}
            </Link>
            <Link
              href="/streams"
              className="block px-4 py-3 text-white font-herborn font-bold tracking-wide hover:bg-bonk-orange/20 hover:text-bonk-yellow border-l-4 border-transparent hover:border-bonk-yellow transition-all duration-200"
              onClick={() => setMobileMenuOpen(false)}
            >
              {navItems.streams}
            </Link>
            <Link
              href="/discover"
              className="block px-4 py-3 text-white font-herborn font-bold tracking-wide hover:bg-bonk-orange/20 hover:text-bonk-yellow border-l-4 border-transparent hover:border-bonk-yellow transition-all duration-200"
              onClick={() => setMobileMenuOpen(false)}
            >
              {navItems.discover}
            </Link>
            
            {/* Show Collection, Profile, Dashboard only if connected and authenticated */}
            {wallet.connected && (
              <>
                <Link
                  href="/collection"
                  className="block px-4 py-3 text-white font-herborn font-bold tracking-wide hover:bg-bonk-orange/20 hover:text-bonk-yellow border-l-4 border-transparent hover:border-bonk-yellow transition-all duration-200"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {navItems.collection}
                </Link>
                <Link
                  href="/creator/dashboard"
                  className="block px-4 py-3 text-white font-herborn font-bold tracking-wide hover:bg-bonk-orange/20 hover:text-bonk-yellow border-l-4 border-transparent hover:border-bonk-yellow transition-all duration-200"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {navItems.dashboard}
                </Link>
                
                {/* Go Live / Live button for all authenticated users */}
                <Link
                  href="/go-live"
                  className="block px-4 py-3 text-white bg-bonk-red font-herborn font-black uppercase tracking-wide hover:bg-black border-l-4 border-transparent hover:border-white transition-all duration-200 m-4 rounded-xl"
                  style={{
                    animation: isBroadcasting ? 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite' : 'none',
                    backgroundColor: isBroadcasting ? '#dc2626' : undefined
                  }}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                    <span suppressHydrationWarning>{isBroadcasting ? navItems.live : navItems.goLive}</span>
                  </div>
                </Link>
              </>
            )}
            
            {/* Add Admin link to mobile menu if user is admin */}
            {wallet.connected && isAuthenticated && isAdmin && (
              <Link
                href="/admin"
                className="block px-4 py-3 text-bonk-yellow font-herborn font-bold tracking-wide hover:bg-bonk-orange/20 hover:text-white border-l-4 border-transparent hover:border-white transition-all duration-200"
                onClick={() => setMobileMenuOpen(false)}
              >
                {navItems.admin}
              </Link>
            )}
          </nav>

          {/* Mobile Menu Footer - Wallet and Language */}
          <div className="border-t border-white/20 p-4 space-y-4">
            {/* Language Switcher for Mobile */}
            <div className="flex items-center justify-between">
              <span className="text-white font-herborn font-bold text-sm">Language</span>
              <LanguageSwitcher currentLocale={currentLocale} />
            </div>

            {/* Wallet Connect for Mobile */}
            <div className="w-full">
              <WalletButtonWithErrorHandling />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
