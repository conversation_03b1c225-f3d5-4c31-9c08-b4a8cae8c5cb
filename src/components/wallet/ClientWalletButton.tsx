'use client';

import dynamic from 'next/dynamic';
import { Loader2 } from 'lucide-react';

// Dynamically import WalletMultiButton to avoid SSR hydration issues
const WalletMultiButton = dynamic(
  () => import('@solana/wallet-adapter-react-ui').then((mod) => mod.WalletMultiButton),
  {
    ssr: false,
    loading: () => (
      <button className="wallet-adapter-button wallet-adapter-button-trigger">
        <Loader2 className="w-4 h-4 animate-spin" />
        <span>Loading...</span>
      </button>
    ),
  }
);

interface ClientWalletButtonProps {
  className?: string;
  children?: React.ReactNode;
}

export default function ClientWalletButton({ className, children }: ClientWalletButtonProps) {
  if (children) {
    return <div className={className}>{children}</div>;
  }
  
  return <WalletMultiButton className={className} />;
}
