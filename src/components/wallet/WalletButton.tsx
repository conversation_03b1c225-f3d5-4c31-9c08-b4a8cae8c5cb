'use client';

import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

interface WalletButtonProps {
  className?: string;
  children?: React.ReactNode;
}

export function WalletButton({ className, children }: WalletButtonProps) {
  const { connected, publicKey } = useWallet();
  const [mounted, setMounted] = useState(false);
  
  // Only show the UI once mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Try to get translations, fallback gracefully if not available
  let connectText = 'Connect';
  try {
    const t = useTranslations('common');
    connectText = t('nav.connect_wallet');
  } catch {
    // If translation fails, use default text
  }

  const formatWalletAddress = (address: string) => {
    if (!address) return '';
    return `${address.substring(0, 4)}...${address.substring(address.length - 4)}`;
  };

  const baseClasses = "px-4 py-2 rounded-lg text-sm font-bold transition-all duration-300";
  const defaultClasses = "bg-bonk-orange hover:bg-bonk-red text-white shadow-md hover:shadow-lg";
  
  // Apply custom classes with default styling as fallback
  const buttonClasses = cn(baseClasses, defaultClasses, className);

  // Render a placeholder during SSR to prevent hydration mismatch
  if (!mounted) {
    return (
      <button className={buttonClasses}>
        {children || connectText}
      </button>
    );
  }

  if (connected && publicKey) {
    return (
      <button className={buttonClasses}>
        {formatWalletAddress(publicKey.toString())}
      </button>
    );
  }

  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(buttonClasses, "cursor-pointer")}
        onClick={() => {
          // Try multiple approaches to trigger wallet connection
          // 1. Try by ID first
          const walletBtnById = document.getElementById('wallet-adapter-modal-btn');
          if (walletBtnById) {
            walletBtnById.click();
            return;
          }

          // 2. Try by class selector (header approach)
          const walletButton = document.querySelector('.wallet-adapter-button');
          if (walletButton instanceof HTMLElement) {
            walletButton.click();
            return;
          }

          // 3. Dispatch custom event as fallback
          window.dispatchEvent(new CustomEvent('requestWalletConnection'));
        }}
      >
        {children || connectText}
      </div>
      {/* Only render WalletMultiButton on client */}
      {mounted && (
        <div className="hidden">
          <WalletMultiButton />
        </div>
      )}
    </div>
  );
} 