'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: string;
}

export class WalletErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: '',
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: '',
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Wallet Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    
    this.setState({
      errorInfo: errorInfo.componentStack || '',
    });
  }

  handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: '',
    });
    window.location.reload();
  };

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <div className="p-4 rounded-lg bg-red-950/30 border border-red-700 text-white my-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="text-red-500" size={20} />
            <h3 className="font-medium">Wallet Error</h3>
          </div>
          
          <div className="mb-3 text-sm text-white/80">
            <p>{this.state.error?.message || 'An error occurred with the wallet connection.'}</p>
          </div>
          
          <button
            onClick={this.handleReset}
            className="flex items-center gap-1 text-sm px-3 py-1.5 bg-white/10 hover:bg-white/20 transition rounded-md"
          >
            <RefreshCw size={14} />
            <span>Reload Page</span>
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}