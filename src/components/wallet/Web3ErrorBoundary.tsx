import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: string;
}

/**
 * Web3ErrorBoundary component to catch and handle blockchain transaction errors
 * This prevents the entire app from crashing when a Web3 transaction fails
 */
export class Web3ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: '',
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: error.message,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error('Web3 Transaction Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
    
    this.setState({
      errorInfo: errorInfo.componentStack || '',
    });
  }

  handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: '',
    });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // Render custom fallback UI or the provided fallback
      return this.props.fallback || (
        <div className="p-4 rounded-lg bg-red-950/30 border border-red-700 text-white my-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="text-red-500" size={20} />
            <h3 className="font-medium">Transaction Error</h3>
          </div>
          
          <div className="mb-3 text-sm text-white/80">
            <p>{this.state.error?.message || 'An unknown error occurred during the blockchain transaction.'}</p>
            {this.state.error?.name === 'WalletSignTransactionError' && (
              <p className="mt-2">It seems the transaction was rejected. Please try again.</p>
            )}
            {this.state.error?.message?.includes('insufficient funds') && (
              <p className="mt-2">You don&apos;t have enough BONK tokens for this operation.</p>
            )}
          </div>
          
          <button
            onClick={this.handleReset}
            className="flex items-center gap-1 text-sm px-3 py-1.5 bg-white/10 hover:bg-white/20 transition rounded-md"
          >
            <RefreshCw size={14} />
            <span>Try Again</span>
          </button>
        </div>
      );
    }

    return this.props.children;
  }
} 