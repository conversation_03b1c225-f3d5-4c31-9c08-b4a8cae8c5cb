'use client';

import { FC, ReactNode, useMemo, useState, useEffect } from 'react';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import dynamic from 'next/dynamic';

// Dynamically import wallet profile handler
const WalletProfileHandler = dynamic(() => import('./WalletProfileHandler'), {
  ssr: false
});

// Import wallet adapter styles (will be loaded by bundler)
import '@solana/wallet-adapter-react-ui/styles.css';

interface SolanaWalletProviderProps {
  children: ReactNode;
}

// Memoize RPC endpoint calculation
const getRpcEndpoint = (): string => {
  const isDevnet = process.env.NEXT_PUBLIC_SOLANA_NETWORK === 'devnet';
  const customRPC = process.env.NEXT_PUBLIC_SOLANA_RPC;
  
  // If custom RPC is provided, use it
  if (customRPC) {
    return customRPC;
  }
  
  // Use Helius endpoints based on network
  return isDevnet 
    ? 'https://devnet.helius-rpc.com'
    : 'https://mainnet.helius-rpc.com';
};

export const SolanaWalletProvider: FC<SolanaWalletProviderProps> = ({ children }) => {
  // Initialize empty wallet array - we'll update this on the client side
  const [wallets, setWallets] = useState<unknown[]>([]);
  const [mounted, setMounted] = useState(false);
  
  // Memoize the endpoint for this environment
  const endpoint = useMemo(() => getRpcEndpoint(), []);
  
  // Initialize client-side only state after mounting
  useEffect(() => {
    // Only initialize wallets on client side to avoid SSR issues
    const initializeWallets = async () => {
      try {
        // Use timeout to avoid blocking the main thread
        const walletModules = await Promise.all([
          import('@solana/wallet-adapter-wallets').then(m => m.PhantomWalletAdapter),
          import('@solana/wallet-adapter-backpack').then(m => m.BackpackWalletAdapter)
        ]);
        
        const [PhantomWalletAdapter, BackpackWalletAdapter] = walletModules;
        
        setWallets([
          new PhantomWalletAdapter(),
          new BackpackWalletAdapter(),
        ]);
      } catch (error) {
        console.error('Error initializing wallets:', error);
        setWallets([]);
      } finally {
        setMounted(true);
      }
    };

    // Small delay to prevent blocking initial render
    const timeoutId = setTimeout(initializeWallets, 100);
    return () => clearTimeout(timeoutId);
  }, []);

  // Always provide the wallet context, even before wallets are initialized
  return (
    <ConnectionProvider endpoint={endpoint}>
      {/* autoConnect=true ensures wallet stays connected across page reloads and navigation */}
      <WalletProvider wallets={wallets as unknown as import('@solana/wallet-adapter-base').Adapter[]} autoConnect={mounted}>
        <WalletModalProvider>
          {/* Profile handler to create/update profiles in Supabase when wallet connects */}
          {mounted && <WalletProfileHandler />}
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
}; 