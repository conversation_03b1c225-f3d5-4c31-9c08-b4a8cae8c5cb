'use client';

import { useWallet } from '@solana/wallet-adapter-react';
import { useEffect, useState, useRef } from 'react';

/**
 * This component is responsible for creating/updating a profile in Supabase
 * whenever a wallet connects. It doesn't render anything visible.
 */
export default function WalletProfileHandler() {
  const { connected, publicKey } = useWallet();
  const [isProcessing, setIsProcessing] = useState(false);
  const processedWallets = useRef(new Set<string>());

  useEffect(() => {
    if (!connected || !publicKey || isProcessing) return;

    const walletAddress = publicKey.toString();
    
    // Skip if we've already processed this wallet
    if (processedWallets.current.has(walletAddress)) return;

    // Mark as processing to prevent duplicate requests
    setIsProcessing(true);
    processedWallets.current.add(walletAddress);

    const createProfile = async () => {
      try {
        // Call the profile API to create/update the profile
        const response = await fetch(`/en/api/profile?wallet=${walletAddress}`, {
          cache: 'no-store'
        });
        
        if (!response.ok) {
          console.warn('Profile creation failed:', response.status);
        }
      } catch (error) {
        console.error('Error creating profile:', error);
        // Remove from processed wallets on error so it can be retried
        processedWallets.current.delete(walletAddress);
      } finally {
        setIsProcessing(false);
      }
    };

    // Debounce the API call
    const timeout = setTimeout(createProfile, 1000);
    
    return () => {
      clearTimeout(timeout);
      setIsProcessing(false);
    };
  }, [connected, publicKey, isProcessing]); // Include all dependencies used in the effect

  // Clear processed wallets when disconnecting
  useEffect(() => {
    if (!connected) {
      processedWallets.current.clear();
      setIsProcessing(false);
    }
  }, [connected]);

  return null;
} 