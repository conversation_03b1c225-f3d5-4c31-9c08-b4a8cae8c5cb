'use client';

import { useEffect, useState } from 'react';
import { Analytics } from '@vercel/analytics/next';
import { isCookieCategoryAllowed } from '@/lib/cookies';

export function ConditionalAnalytics() {
  const [analyticsAllowed, setAnalyticsAllowed] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    setAnalyticsAllowed(isCookieCategoryAllowed('analytics'));

    // Listen for cookie preference changes
    const handleStorageChange = () => {
      setAnalyticsAllowed(isCookieCategoryAllowed('analytics'));
    };

    window.addEventListener('storage', handleStorageChange);
    
    // Also listen for custom events when preferences change in the same tab
    const handleCookieChange = () => {
      setAnalyticsAllowed(isCookieCategoryAllowed('analytics'));
    };
    
    window.addEventListener('cookie-preferences-changed', handleCookieChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cookie-preferences-changed', handleCookieChange);
    };
  }, []);

  // Don't render anything during SSR to avoid hydration mismatches
  if (!mounted) {
    return null;
  }

  // Only render Analytics if analytics cookies are allowed
  return analyticsAllowed ? <Analytics /> : null;
} 