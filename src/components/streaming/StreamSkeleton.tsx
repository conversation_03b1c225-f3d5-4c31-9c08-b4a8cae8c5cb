import { Skeleton } from "@/components/ui/Skeleton";

export function StreamSkeleton() {
  return (
    <div className="h-screen snap-start flex items-center justify-center relative overflow-hidden bg-black">
      {/* Background with gradient overlay */}
      <div className="absolute inset-0 z-0">
        <Skeleton className="w-full h-full" />
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
        
        {/* Decorative elements */}
        <div className="absolute -top-20 -right-20 w-40 h-40 bg-yellow-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-purple-500/10 rounded-full filter blur-3xl"></div>
      </div>
      
      {/* Content overlay skeleton */}
      <div className="absolute inset-0 flex flex-col justify-between p-4 pointer-events-none">
        <div className="flex justify-between">
          <div className="bg-black/30 backdrop-blur-sm p-2 rounded-full">
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
        
        <div className="flex items-end justify-between">
          {/* Stream info skeleton */}
          <div className="max-w-[70%]">
            <div className="flex items-center gap-3 mb-4">
              {/* Creator avatar skeleton */}
              <div className="rounded-full overflow-hidden bg-black/40 backdrop-blur-sm p-0.5">
                <Skeleton className="w-10 h-10 rounded-full" />
              </div>
              
              <div className="space-y-2">
                <Skeleton className="h-5 w-32 rounded-md" />
                <Skeleton className="h-4 w-24 rounded-md" />
              </div>
            </div>
            
            <Skeleton className="h-6 w-64 mb-3 rounded-md" />
            <Skeleton className="h-5 w-80 rounded-md" />
            <div className="flex gap-2 mt-4">
              <Skeleton className="h-6 w-16 rounded-full" />
              <Skeleton className="h-6 w-20 rounded-full" />
            </div>
          </div>
          
          {/* Action buttons skeleton */}
          <div className="flex flex-col items-center gap-6">
            <div className="flex flex-col items-center">
              <Skeleton className="w-12 h-12 rounded-full mb-1" />
              <Skeleton className="w-8 h-3 rounded-md" />
            </div>
            <div className="flex flex-col items-center">
              <Skeleton className="w-12 h-12 rounded-full mb-1" />
              <Skeleton className="w-8 h-3 rounded-md" />
            </div>
            <div className="flex flex-col items-center">
              <Skeleton className="w-12 h-12 rounded-full mb-1" />
              <Skeleton className="w-8 h-3 rounded-md" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Video play indicator skeleton */}
      <div className="absolute inset-0 flex items-center justify-center">
        <Skeleton className="w-16 h-16 rounded-full opacity-30" />
      </div>
    </div>
  );
} 