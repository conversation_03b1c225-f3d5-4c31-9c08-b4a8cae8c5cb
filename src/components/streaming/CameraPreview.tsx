'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { <PERSON>, VideoOff, Mic, MicOff, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';

interface CameraPreviewProps {
  enabled: boolean;
  cameraEnabled: boolean;
  microphoneEnabled: boolean;
  hasCamera: boolean;
  hasMicrophone: boolean;
  onCameraToggle: () => void;
  onMicrophoneToggle: () => void;
  className?: string;
}

export function CameraPreview({
  enabled,
  cameraEnabled,
  microphoneEnabled,
  hasCamera,
  hasMicrophone,
  onCameraToggle,
  onMicrophoneToggle,
  className = ''
}: CameraPreviewProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  
  // const [isPreviewActive, setIsPreviewActive] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const [showAudioGraph] = useState(false);

  const drawAudioGraph = useCallback((dataArray: Uint8Array) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.fillRect(0, 0, width, height);

    // Draw frequency bars
    const barWidth = width / dataArray.length * 2;
    let x = 0;

    ctx.fillStyle = '#8b5cf6';
    for (let i = 0; i < dataArray.length; i++) {
      const barHeight = (dataArray[i] / 255) * height;
      ctx.fillRect(x, height - barHeight, barWidth, barHeight);
      x += barWidth + 1;
    }
  }, []);

  const updateAudioLevel = useCallback(() => {
    if (!analyserRef.current) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    analyserRef.current.getByteFrequencyData(dataArray);

    // Calculate average volume
    const average = dataArray.reduce((a, b) => a + b, 0) / bufferLength;
    const normalizedLevel = average / 255;
    
    setAudioLevel(normalizedLevel);

    // Draw audio visualization if canvas is available and graph is shown
    if (showAudioGraph && canvasRef.current) {
      drawAudioGraph(dataArray);
    }

    animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
  }, [showAudioGraph, drawAudioGraph]);

  const setupAudioVisualization = useCallback((stream: MediaStream) => {
    try {
      const audioContext = new (window.AudioContext || (window as Window & { webkitAudioContext?: typeof AudioContext }).webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;
      microphone.connect(analyser);
      
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;
      
      updateAudioLevel();
    } catch (error) {
      console.error('Failed to setup audio visualization:', error);
    }
  }, [updateAudioLevel]);

  const startPreview = useCallback(async () => {
    try {
      setPreviewError(null);
      
      // Request user media with current device settings
      const constraints: MediaStreamConstraints = {
        video: cameraEnabled && hasCamera,
        audio: microphoneEnabled && hasMicrophone
      };

      if (!constraints.video && !constraints.audio) {
        setPreviewError('No devices enabled for preview');
        return;
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      mediaStreamRef.current = stream;

      // Set up video preview
      if (videoRef.current && constraints.video) {
        videoRef.current.srcObject = stream;
        videoRef.current.muted = true; // Prevent audio feedback
        // setIsPreviewActive(true);
      }

      // Set up audio visualization
      if (constraints.audio) {
        setupAudioVisualization(stream);
      }

    } catch (error) {
      console.error('Failed to start camera preview:', error);
      setPreviewError('Failed to access camera/microphone. Please check permissions.');
      // setIsPreviewActive(false);
    }
  }, [cameraEnabled, hasCamera, microphoneEnabled, hasMicrophone, setupAudioVisualization]);

  const stopPreview = useCallback(() => {
    // Stop media tracks
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    // Clean up video
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    // Clean up audio context
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
      analyserRef.current = null;
    }

    // Cancel animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // setIsPreviewActive(false);
    setAudioLevel(0);
  }, []);

  // Start preview when enabled
  useEffect(() => {
    if (enabled && (hasCamera || hasMicrophone)) {
      startPreview();
    } else {
      stopPreview();
    }

    return () => {
      stopPreview();
    };
  }, [enabled, cameraEnabled, microphoneEnabled, hasCamera, hasMicrophone, startPreview, stopPreview]);

  if (!enabled) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            Camera Preview
          </span>
          
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {previewError && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{previewError}</AlertDescription>
          </Alert>
        )}

        {/* Video Preview */}
        <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-video">
          {cameraEnabled && hasCamera ? (
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
              onLoadedMetadata={() => {
                if (videoRef.current) {
                  videoRef.current.play();
                }
              }}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-gray-400">
              <div className="text-center">
                <VideoOff className="h-12 w-12 mx-auto mb-2" />
                <p className="text-sm">
                  {!hasCamera ? 'No camera available' : 'Camera disabled'}
                </p>
              </div>
            </div>
          )}

          {/* Audio Level Indicator */}
          {microphoneEnabled && hasMicrophone && !showAudioGraph && (
            <div className="absolute bottom-4 right-4 bg-black/50 rounded-lg p-2">
              <div className="flex items-center gap-2">
                <Mic className="h-4 w-4 text-white" />
                <div className="w-20 h-2 bg-gray-600 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-green-500 transition-all duration-75"
                    style={{ width: `${Math.min(audioLevel * 100, 100)}%` }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Device Status Overlay */}
          <div className="absolute top-4 left-4 flex gap-2">
            {!hasCamera && (
              <div className="bg-red-500/80 text-white px-2 py-1 rounded text-xs flex items-center gap-1">
                <VideoOff className="h-3 w-3" />
                No Camera
              </div>
            )}
            {!hasMicrophone && (
              <div className="bg-red-500/80 text-white px-2 py-1 rounded text-xs flex items-center gap-1">
                <MicOff className="h-3 w-3" />
                No Mic
              </div>
            )}
          </div>
        </div>

        {/* Audio Visualization Graph */}
        {showAudioGraph && microphoneEnabled && hasMicrophone && (
          <div className="relative bg-gray-900 rounded-lg overflow-hidden h-32">
            <canvas
              ref={canvasRef}
              width={400}
              height={128}
              className="w-full h-full"
            />
            <div className="absolute top-2 left-2 text-white text-xs">
              Audio Frequency
            </div>
          </div>
        )}

        {/* Preview Controls */}
        <div className="flex justify-center gap-2">
          <Button
            variant={cameraEnabled ? "default" : "outline"}
            size="sm"
            onClick={onCameraToggle}
            disabled={!hasCamera}
          >
            {cameraEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
            Camera
          </Button>
          <Button
            variant={microphoneEnabled ? "default" : "outline"}
            size="sm"
            onClick={onMicrophoneToggle}
            disabled={!hasMicrophone}
          >
            {microphoneEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
            Microphone
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
