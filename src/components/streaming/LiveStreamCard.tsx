'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';
import { Heart, Share, Play, Eye, Coins, Gem } from 'lucide-react';

type LiveStreamCardProps = {
  stream: {
    id: string;
    title: string;
    description?: string;
    thumbnail_url?: string;
    playback_url?: string;
    created_at: string;
    view_count: number;
    tip_count: number;
    nft_count: number;
    like_count: number;
    is_actually_live?: boolean;
    creator: {
      id: string;
      username: string;
      avatar_url?: string;
      wallet_address: string;
    };
  };
  vertical?: boolean;
};

export function LiveStreamCard({ stream, vertical = false }: LiveStreamCardProps) {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);
  
  const handleClick = useCallback(() => {
    // Use router.push for faster navigation
    router.push(`/stream/${stream.id}`);
  }, [router, stream.id]);

  const handleCreatorClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/c/${stream.creator.username || stream.creator.wallet_address}`);
  }, [router, stream.creator.username, stream.creator.wallet_address]);

  const handleShareClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    try {
      const shareData = {
        title: stream.title,
        text: `Check out ${stream.creator.username}'s stream on bonkstream`,
        url: `${window.location.origin}/stream/${stream.id}`
      };
      
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback for browsers that don't support Web Share API
        navigator.clipboard.writeText(shareData.url);
        alert('Link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  return (
    <div
      className={`relative cursor-pointer bg-gray-900 rounded-lg overflow-hidden group ${
        vertical ? 'aspect-[9/16]' : 'aspect-video'
      } w-full`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {stream.thumbnail_url ? (
        <Image
          src={stream.thumbnail_url}
          alt={stream.title}
          fill
          className="object-cover transition-transform group-hover:scale-105"
          unoptimized={stream.thumbnail_url.startsWith('data:')}
        />
      ) : (
        <div className="absolute inset-0 bg-gradient-to-br from-[#3d2a54] to-[#15162c]" />
      )}
      
      {/* Live indicator */}
      {stream.is_actually_live && (
        <div className="absolute top-3 left-3 bg-red-600 text-white px-2 py-1 text-xs font-semibold rounded z-10 flex items-center gap-1">
          <span className="w-2 h-2 rounded-full bg-white animate-pulse inline-block"></span>
          LIVE
        </div>
      )}
      
      {/* Play button overlay */}
      <div className={`absolute inset-0 flex items-center justify-center transition-opacity ${
        isHovered ? 'opacity-100' : 'opacity-0'
      }`}>
        <div className="bg-black bg-opacity-40 rounded-full p-4">
          <Play size={32} className="text-white" fill="white" />
        </div>
      </div>
      
      {/* Content overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4">
        {/* Creator info */}
        <div className="flex items-center mb-2" onClick={handleCreatorClick}>
          <div className="w-8 h-8 rounded-full overflow-hidden bg-[#FFD700] mr-2 flex-shrink-0">
            {stream.creator.avatar_url ? (
              <Image
                src={stream.creator.avatar_url}
                alt={stream.creator.username}
                width={32}
                height={32}
                className="object-cover"
                unoptimized={stream.creator.avatar_url.startsWith('data:')}
              />
            ) : (
              <div className="w-full h-full bg-[#FFD700]" />
            )}
          </div>
          <div className="text-white font-medium truncate">{stream.creator.username}</div>
        </div>
        
        {/* Stream title */}
        <h3 className="text-white font-bold mb-1 line-clamp-2">{stream.title}</h3>
        
        {/* Stream description */}
        {stream.description && (
          <p className="text-white/70 text-sm mb-2 line-clamp-2">{stream.description}</p>
        )}
        
        {/* Stream stats */}
        <div className="flex items-center text-white/70 text-sm space-x-3">
          <div className="flex items-center">
            <Eye size={14} className="mr-1" />
            <span>{stream.view_count || 0}</span>
          </div>
          <div className="flex items-center">
            <Heart size={14} className="mr-1" />
            <span>{stream.like_count || 0}</span>
          </div>
          <div className="flex items-center">
            <Coins size={14} className="mr-1" />
            <span>{stream.tip_count || 0}</span>
          </div>
          <div className="flex items-center">
            <Gem size={14} className="mr-1" />
            <span>{stream.nft_count || 0}</span>
          </div>
          <div className="flex-1 text-right">
            {formatDistanceToNow(new Date(stream.created_at), { addSuffix: true })}
          </div>
          <div 
            className="cursor-pointer p-1 hover:bg-white/20 rounded"
            onClick={handleShareClick}
          >
            <Share size={16} />
          </div>
        </div>
      </div>
    </div>
  );
}
