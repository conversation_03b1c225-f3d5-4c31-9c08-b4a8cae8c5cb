'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useParams } from 'next/navigation';
import { MessageCircle, Send, Users, Minimize2, Maximize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import supabase from '@/lib/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';
import { ChatMessage } from '@/hooks/use-realtime-chat';

interface StreamingChatProps {
  streamId: string;
  className?: string;
  compact?: boolean;
  fixed?: boolean; // Whether to use fixed positioning
}

export function StreamingChat({ streamId, className = '', compact = false, fixed = false }: StreamingChatProps) {
  const { connected, publicKey } = useWallet();
  const params = useParams();
  const locale = params.locale as string;
  // const t = useTranslations('streaming');
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewerCount, setViewerCount] = useState(0);
  const [isMinimized, setIsMinimized] = useState(compact);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const channelRef = useRef<RealtimeChannel | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Auto-scroll to bottom
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);
  
  // Load initial chat messages
  const loadMessages = useCallback(async () => {
    if (!streamId) return;
    
    try {
      setError(null);
      const response = await fetch(`/${locale}/api/stream/chat/${streamId}?limit=30`);
      
      if (!response.ok) {
        console.warn('Chat messages endpoint returned:', response.status);
        // Don't show error for chat loading, just show empty state
        setMessages([]);
      } else {
        const data = await response.json();
        if (data.success) {
          setMessages(data.messages || []);
          setTimeout(scrollToBottom, 100);
        }
      }
    } catch (err) {
      console.warn('Error loading messages:', err);
      // Don't show error for initial load, just show empty chat
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  }, [streamId, locale, scrollToBottom]);
  
  // Send message
  const sendMessage = async () => {
    if (!newMessage.trim() || !connected || !publicKey || isSending) return;
    
    setIsSending(true);
    setError(null);
    
    try {
      // Get authentication headers
      let authHeaders: Record<string, string> = {};
      try {
        const { createAuthHeaders } = await import('@/lib/wallet-auth');
        authHeaders = createAuthHeaders(publicKey.toString());
      } catch (authError) {
        console.error('Authentication error:', authError);
        setError('Please reconnect your wallet to chat');
        return;
      }
      
      const response = await fetch(`/${locale}/api/stream/chat/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        },
        body: JSON.stringify({
          streamId,
          message: newMessage.trim(),
          walletAddress: publicKey.toString(),
          emotes: [],
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }
      
      // Clear input on success
      setNewMessage('');
      inputRef.current?.focus();
      
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err instanceof Error ? err.message : 'Failed to send message');
    } finally {
      setIsSending(false);
    }
  };
  
  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };
  
  // Set up realtime subscriptions
  useEffect(() => {
    if (!streamId) return;
    
    loadMessages();
    
    // Subscribe to chat messages
    const channel = supabase
      .channel(`streaming_chat:${streamId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `stream_id=eq.${streamId}`,
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage;
          if (!newMessage.is_deleted) {
            setMessages(prev => [...prev.slice(-29), newMessage]); // Keep only last 30 messages
            setTimeout(scrollToBottom, 100);
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'stream_viewer_count',
          filter: `stream_id=eq.${streamId}`,
        },
        (payload) => {
          if (payload.new) {
            setViewerCount((payload.new as { viewer_count?: number }).viewer_count || 0);
          }
        }
      )
      .subscribe();
    
    channelRef.current = channel;
    
    return () => {
      channel.unsubscribe();
    };
  }, [streamId, loadMessages, scrollToBottom]);
  
  // Auto-scroll when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);
  
  const containerClass = fixed 
    ? `fixed bottom-4 right-4 z-50 ${className}` 
    : className;
  const cardClass = fixed 
    ? "w-80 sm:w-80 bg-black/90 backdrop-blur border-gray-700 shadow-xl max-w-[calc(100vw-32px)]" 
    : "w-80";
  
  if (isMinimized) {
    return (
      <div className={containerClass}>
        <Card className={`${cardClass} h-12`}>
          <CardContent className="p-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              <span className="text-sm font-medium text-white">Chat</span>
              <Badge variant="secondary" className="flex items-center gap-1 bg-bonk-yellow/20 text-bonk-yellow">
                <Users className="w-3 h-3" />
                {viewerCount}
              </Badge>
              {messages.length > 0 && (
                <Badge variant="outline" className="text-xs">
                  {messages.length}
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(false)}
              className="text-gray-400 hover:text-white"
            >
              <Maximize2 className="w-4 h-4" />
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className={containerClass}>
      <Card className={`${cardClass} h-64 flex flex-col`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4" />
            <span>Live Chat</span>
            <Badge variant="secondary" className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              {viewerCount}
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMinimized(true)}
          >
            <Minimize2 className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col p-2 gap-2">
        {/* Messages area */}
        <div className="flex-1 overflow-y-auto space-y-1 pr-2">
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-bonk-yellow border-t-transparent"></div>
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center text-gray-500 py-4 text-xs">
              No messages yet. Start chatting!
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className="text-xs">
                <div className="flex items-center gap-1 mb-1">
                  <span className="font-medium text-bonk-yellow truncate max-w-[100px]">
                    {message.profiles?.username || 
                     `${message.sender_wallet_address.slice(0, 4)}...${message.sender_wallet_address.slice(-2)}`}
                  </span>
                  <span className="text-gray-400 text-xs">
                    {new Date(message.created_at).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </span>
                </div>
                <div className="text-white text-xs break-words pl-2 border-l-2 border-bonk-yellow/20">
                  {message.message_content}
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
        
        {/* Error message */}
        {error && (
          <div className="text-red-400 text-xs p-2 bg-red-900/20 rounded">
            {error}
          </div>
        )}
        
        {/* Input area */}
        {connected ? (
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Chat with viewers..."
              disabled={isSending}
              maxLength={500}
              className="text-sm h-8"
            />
            
            <Button
              onClick={sendMessage}
              disabled={!newMessage.trim() || isSending}
              size="sm"
              className="px-2 h-8"
            >
              {isSending ? (
                <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent" />
              ) : (
                <Send className="w-3 h-3" />
              )}
            </Button>
          </div>
        ) : (
          <div className="text-center py-2">
            <span className="text-gray-400 text-xs">
              Connect wallet to chat
            </span>
          </div>
        )}
      </CardContent>
    </Card>
    </div>
  );
}
