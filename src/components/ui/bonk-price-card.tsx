'use client';

import { useState, useEffect, useRef, memo, useCallback } from 'react';
import Image from 'next/image';
import { useParams } from 'next/navigation';

interface BonkPriceData {
  price: number;
  change24h: number;
  change24hPercent: number;
}

// TradingView widget component - no lazy loading needed here
function TradingViewWidgetComponent() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Only load TradingView when component becomes visible
  useEffect(() => {
    if (!mounted || !containerRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { rootMargin: '100px' }
    );

    observer.observe(containerRef.current);

    return () => observer.disconnect();
  }, [mounted, isVisible]);

  useEffect(() => {
    if (!isVisible || !containerRef.current) return;

    const container = containerRef.current;
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js';
    script.type = 'text/javascript';
    script.async = true;
    script.innerHTML = JSON.stringify({
      autosize: false,
      width: "100%",
      height: 400,
      symbol: "BYBIT:BONKUSDT",
      interval: "D",
      timezone: "Etc/UTC",
      theme: "dark",
      style: "1",
      locale: "en",
      enable_publishing: false,
      backgroundColor: "rgba(0, 0, 0, 0)",
      gridColor: "rgba(255, 255, 255, 0.06)",
      hide_top_toolbar: true,
      hide_legend: true,
      save_image: false,
      calendar: false,
      hide_volume: true,
      support_host: "https://www.tradingview.com"
    });

    container.appendChild(script);

    return () => {
      if (container && script.parentNode) {
        container.removeChild(script);
      }
    };
  }, [isVisible]);

  if (!mounted) {
    return (
      <div 
        className="flex items-center justify-center"
        style={{ height: '400px', width: '100%' }}
      >
        <div className="text-white/60 text-center">
          <div className="animate-spin w-8 h-8 border-2 border-bonk-orange border-t-transparent rounded-full mx-auto mb-2"></div>
          <div className="text-sm">Loading Chart...</div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="tradingview-widget-container"
      style={{ height: '400px', width: '100%' }}
    >
      {!isVisible ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-white/60 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-bonk-orange border-t-transparent rounded-full mx-auto mb-2"></div>
            <div className="text-sm">Loading Chart...</div>
          </div>
        </div>
      ) : (
        <div className="tradingview-widget-container__widget"></div>
      )}
    </div>
  );
}

const BonkPriceCard = memo(function BonkPriceCard() {
  const params = useParams();
  const locale = params.locale as string || 'en';
  const [priceData, setPriceData] = useState<BonkPriceData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchBonkPrice() {
      try {
        setLoading(true);
        // Use our API route with locale (same pattern as other components)
        const response = await fetch(`/${locale}/api/bonk-price`, {
          cache: 'no-cache'
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            setPriceData({
              price: result.data.price,
              change24h: result.data.change24h,
              change24hPercent: result.data.change24hPercent
            });
          } else {
            // Use fallback data from API response
            setPriceData(result.data);
          }
        } else {
          // Fallback data if API fails
          setPriceData({
            price: 0.000023,
            change24h: 5.67,
            change24hPercent: 5.67
          });
        }
      } catch (error) {
        console.error('Error fetching BONK price:', error);
        // Fallback data
        setPriceData({
          price: 0.000023,
          change24h: 5.67,
          change24hPercent: 5.67
        });
      } finally {
        setLoading(false);
      }
    }

    fetchBonkPrice();
    
    // Update price every 2 minutes to reduce API calls
    const interval = setInterval(fetchBonkPrice, 120000);
    
    return () => clearInterval(interval);
  }, [locale]);

  const formatPrice = useCallback((price: number) => {
    if (price >= 0.01) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(8)}`;
    }
  }, []);

  const formatChange = useCallback((change: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  }, []);

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
      {/* Price Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Image
            src="/bonk-token.png"
            alt="BONK Token"
            width={40}
            height={40}
            className="rounded-full"
          />
          <div>
            <h3 className="text-xl font-herborn font-bold text-white">BONK</h3>
            <p className="text-sm text-white/60">Bonk Token</p>
          </div>
        </div>
        <div className="text-right">
          {loading ? (
            <div className="animate-pulse">
              <div className="h-8 w-24 bg-white/20 rounded mb-1"></div>
              <div className="h-5 w-16 bg-white/20 rounded"></div>
            </div>
          ) : priceData ? (
            <>
              <div className="text-2xl font-herborn font-bold text-bonk-yellow">
                {formatPrice(priceData.price)}
              </div>
              <div 
                className={`text-sm font-medium ${
                  priceData.change24hPercent >= 0 ? 'text-green-400' : 'text-red-400'
                }`}
              >
                {formatChange(priceData.change24hPercent)}
              </div>
            </>
          ) : (
            <div className="text-white/60">Error loading price</div>
          )}
        </div>
      </div>
      
      {/* TradingView Chart - Lazy loaded */}
      <div className="bg-black/20 rounded-lg overflow-hidden">
        <TradingViewWidgetComponent />
      </div>
      
      {/* Live indicator */}
      <div className="flex items-center justify-center mt-4 gap-2">
        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span className="text-sm text-white/70">Live Chart</span>
      </div>
    </div>
  );
});

export default BonkPriceCard;