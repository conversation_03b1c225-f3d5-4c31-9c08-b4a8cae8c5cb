'use client';

import { useEffect, useRef } from 'react';

interface TradingViewChartProps {
  symbol?: string;
  theme?: 'light' | 'dark';
  height?: number;
  width?: string;
}

declare global {
  interface Window {
    TradingView: {
      widget: new (config: unknown) => void;
    };
  }
}

export default function TradingViewChart({
  symbol = 'BYBIT:BONKUSDT',
  theme = 'dark',
  height = 400,
  width = '100%'
}: TradingViewChartProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load TradingView script
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/tv.js';
    script.async = true;
    script.onload = () => {
      if (window.TradingView && containerRef.current) {
        try {
          new window.TradingView.widget({
            autosize: false,
            width: width,
            height: height,
            symbol: symbol,
            interval: '1D',
            timezone: 'Etc/UTC',
            theme: theme,
            style: '1',
            locale: 'en',
            toolbar_bg: 'transparent',
            enable_publishing: false,
            hide_top_toolbar: true,
            hide_legend: true,
            save_image: false,
            container_id: containerRef.current.id,
            backgroundColor: 'transparent',
            gridColor: 'rgba(255, 255, 255, 0.1)',
            hide_side_toolbar: true,
            allow_symbol_change: false,
            studies: [],
            show_popup_button: false,
            popup_width: '1000',
            popup_height: '650'
          });
        } catch (error) {
          console.error('Error loading TradingView widget:', error);
        }
      }
    };
    document.head.appendChild(script);

    return () => {
      // Cleanup
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [symbol, theme, height, width]);

  return (
    <div 
      ref={containerRef} 
      id={`tradingview-chart-${Math.random().toString(36).substr(2, 9)}`}
      className="rounded-lg overflow-hidden"
      style={{ height: `${height}px`, width: width }}
    />
  );
} 