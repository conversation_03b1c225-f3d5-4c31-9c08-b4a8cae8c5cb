import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  text?: string;
  className?: string;
  variant?: 'streams' | 'simple' | 'dots';
}

export function LoadingSpinner({ 
  size = 'lg', 
  text, 
  className = '', 
  variant = 'streams' 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  if (variant === 'simple') {
    return (
      <div className={`flex flex-col items-center ${className}`}>
        <div className={`${sizeClasses[size]} relative animate-spin`}>
          <div className="absolute inset-0 rounded-full border-2 border-gray-600"></div>
          <div className="absolute inset-0 rounded-full border-t-2 border-bonk-orange"></div>
        </div>
        {text && (
          <p className={`mt-4 bonk-body text-white/60 ${textSizeClasses[size]}`}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (variant === 'dots') {
    return (
      <div className={`flex flex-col items-center ${className}`}>
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-bonk-orange rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-bonk-orange rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-bonk-orange rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
        {text && (
          <p className={`mt-4 bonk-body text-white/60 ${textSizeClasses[size]}`}>
            {text}
          </p>
        )}
      </div>
    );
  }

  // Default 'streams' variant - the BONK-styled spinner from streams page
  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className={`${sizeClasses[size]} relative animate-spin`}>
        <div className="absolute inset-0 rounded-full border-4 border-bonk-widget-dark"></div>
        <div className="absolute inset-0 rounded-full border-t-4 border-bonk-orange"></div>
      </div>
      {text && (
        <p className={`mt-4 bonk-body text-white/60 ${textSizeClasses[size]}`}>
          {text}
        </p>
      )}
    </div>
  );
}

// Full-screen loading component using the streams page design
export function LoadingScreen({ 
  text = "Loading...", 
  className = "",
  variant = 'streams'
}: Omit<LoadingSpinnerProps, 'size'>) {
  return (
    <div className={`min-h-screen bg-bonk-gradient-bg flex items-center justify-center ${className}`}>
      <LoadingSpinner size="lg" text={text} variant={variant} />
    </div>
  );
}

export default LoadingSpinner;
