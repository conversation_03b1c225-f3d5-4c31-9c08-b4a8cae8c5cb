import * as React from "react"
import { cn } from "@/lib/utils"

// Button variant styles
const buttonVariants = {
  variant: {
    default: "bg-bonk-orange text-white shadow-lg hover:shadow-bonk-lg rounded-xl",
    destructive: "bg-bonk-red text-white shadow-lg hover:shadow-bonk-xl rounded-xl",
    outline: "border-2 border-bonk-orange bg-transparent text-bonk-orange hover:bg-bonk-orange hover:text-white rounded-xl",
    secondary: "bg-bonk-dark text-white hover:bg-bonk-dark-red shadow-lg rounded-xl",
    ghost: "hover:bg-bonk-orange/10 hover:text-bonk-orange text-white rounded-xl",
    link: "text-bonk-orange underline-offset-4 hover:underline",
    badge: "bg-bonk-orange text-white px-4 py-2 text-sm rounded-full",
    "bonk-example-1": "bg-[#FF5C01] text-white shadow-lg hover:shadow-bonk-lg rounded-xl",
    "bonk-example-2": "bg-[#FF5000] text-white shadow-lg hover:shadow-bonk-lg rounded-xl",
    "bonk-example-3": "bg-[#800000] text-white shadow-lg hover:shadow-bonk-lg rounded-xl",
    "bonk-example-4": "bg-white/20 text-white border border-white shadow-lg hover:shadow-bonk-lg rounded-xl backdrop-blur-sm",
  },
  size: {
    default: "h-12 px-6 py-3 text-base",
    sm: "h-8 px-4 py-2 text-sm rounded-lg",
    lg: "h-14 px-10 py-5 text-lg rounded-2xl",
    icon: "h-12 w-12",
  },
}

type ButtonVariant = keyof typeof buttonVariants.variant
type ButtonSize = keyof typeof buttonVariants.size

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant
  size?: ButtonSize
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", ...props }, ref) => {
    const baseClasses = "inline-flex items-center justify-center whitespace-nowrap font-herborn font-black uppercase tracking-wide transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-bonk-yellow focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-105 active:scale-95"
    const variantClasses = buttonVariants.variant[variant]
    const sizeClasses = buttonVariants.size[size]
    
    return (
      <button
        className={cn(baseClasses, variantClasses, sizeClasses, className)}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button } 