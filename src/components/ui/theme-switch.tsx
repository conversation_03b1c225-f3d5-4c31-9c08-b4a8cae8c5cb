'use client';

import { useState, useEffect } from 'react';
import { Moon, Sun } from 'lucide-react';

type Theme = 'dark' | 'light' | 'system';

export function ThemeSwitch() {
  const [theme, setTheme] = useState<Theme>('dark');
  const [isMounted, setIsMounted] = useState(false);

  // On mount, read the theme from localStorage or use the default (dark)
  useEffect(() => {
    setIsMounted(true);
    const storedTheme = localStorage.getItem('theme') as Theme;
    if (storedTheme) {
      setTheme(storedTheme);
      document.documentElement.classList.toggle('dark', storedTheme === 'dark');
    }
  }, []);

  // Handle theme change
  const toggleTheme = () => {
    const newTheme: Theme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
  };

  // Don't render anything during SSR
  if (!isMounted) {
    return null;
  }

  return (
    <button
      onClick={toggleTheme}
      className="relative inline-flex h-8 w-14 items-center rounded-full bg-black/30 p-1 transition-colors hover:bg-black/50 focus:outline-none dark:bg-white/10 dark:hover:bg-white/20"
      aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <span
        className={`${
          theme === 'dark' ? 'translate-x-6' : 'translate-x-0'
        } inline-block h-6 w-6 transform rounded-full bg-[#FFD700] shadow-lg ring-0 transition-transform duration-200`}
      >
        {theme === 'dark' ? (
          <Moon className="h-6 w-6 p-1 text-black" />
        ) : (
          <Sun className="h-6 w-6 p-1 text-black" />
        )}
      </span>
    </button>
  );
}

// Hook for accessing theme in other components
export function useTheme() {
  const [theme, setTheme] = useState<Theme>('dark');
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    const storedTheme = localStorage.getItem('theme') as Theme || 'dark';
    setTheme(storedTheme);

    // Listen for theme changes in other components
    const handleStorageChange = () => {
      const updatedTheme = localStorage.getItem('theme') as Theme || 'dark';
      setTheme(updatedTheme);
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const toggleTheme = () => {
    const newTheme: Theme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
    
    // Dispatch an event to sync theme changes across components
    window.dispatchEvent(new Event('storage'));
  };

  return {
    theme: isMounted ? theme : 'dark',
    toggleTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
  };
} 