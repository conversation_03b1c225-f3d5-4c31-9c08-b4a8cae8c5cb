import { cn } from "@/lib/utils";
import { memo } from "react";

interface SkeletonProps {
  className?: string;
}

export const Skeleton = memo(function Skeleton({ className }: SkeletonProps) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-gray-800/30", className)}
    />
  );
});

// Pre-built optimized skeleton components for common use cases
export const SkeletonText = memo(function SkeletonText({ 
  lines = 3, 
  className = "" 
}: { 
  lines?: number; 
  className?: string; 
}) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton key={i} className={`h-4 ${i === lines - 1 ? 'w-3/4' : 'w-full'}`} />
      ))}
    </div>
  );
});

export const SkeletonCard = memo(function SkeletonCard({ className = "" }: { className?: string }) {
  return (
    <div className={cn("rounded-lg border border-gray-700 p-4", className)}>
      <Skeleton className="h-32 w-full mb-4" />
      <Skeleton className="h-4 w-3/4 mb-2" />
      <Skeleton className="h-4 w-1/2" />
    </div>
  );
}); 