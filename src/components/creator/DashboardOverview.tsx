'use client';

import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from '@/components/ui/card';
import { DollarSign, Users, Video, Sparkles } from 'lucide-react';
import { BONK_DECIMALS } from '@/lib/bonk';
import Image from 'next/image';

interface Profile {
  id: string;
  wallet_address: string;
  username?: string;
  avatar_url?: string;
  is_creator: boolean;
  created_at: string;
}

interface Stream {
  id: string;
  title: string;
  status: string;
  views?: number;
  created_at: string;
  playback_url?: string;
  thumbnail_url?: string;
  is_actually_live?: boolean;
}

interface Tip {
  id: string;
  amount: number;
  created_at: string;
  tipper_username?: string;
}

interface NFTMint {
  id: string;
  nft_name: string;
  created_at: string;
  buyer_username?: string;
}

interface EarningsData {
  total_bonk: number;
  tip_count: number;
  recent_tips: Tip[];
}

interface DashboardOverviewProps {
  profile: Profile | null;
  earnings: EarningsData | null;
  streams: Stream[];
  nfts: NFTMint[];
  activeStream: Stream | null;
  locale: string;
}

// Format BONK amounts consistently (convert from lamports)
const formatBonkAmount = (amountInLamports: number = 0) => {
  // Convert from lamports to BONK (divide by 10^5)
  const bonkAmount = amountInLamports / Math.pow(10, BONK_DECIMALS);
  return bonkAmount.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 5 });
};

// A simple component to display a stat card
const StatCard = ({ title, value, icon, description, unit }: { title: string; value: string | number; icon: React.ReactNode; description?: string, unit?: string }) => (
  <Card className="bg-gray-800 border-gray-700">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium text-gray-300">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold text-white">{value} {unit && <span className="text-sm text-gray-400">{unit}</span>}</div>
      {description && <p className="text-xs text-gray-400 pt-1">{description}</p>}
    </CardContent>
  </Card>
);

export function DashboardOverview({ profile, earnings, streams, nfts, activeStream }: DashboardOverviewProps) {
  const t = useTranslations('creator.dashboard.overview');
  const commonT = useTranslations('common');

  const totalStreams = streams?.length || 0;
  const totalNftsMinted = nfts?.length || 0;

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h2 className="text-2xl font-semibold text-white">{t('title')}</h2>
            <p className="text-gray-400">{t('welcome', { name: profile?.username || commonT('creatorFallbackName') })}</p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard 
            title={t('stats.totalEarnings')} 
            value={formatBonkAmount(earnings?.total_bonk)} 
            unit="BONK" 
            icon={<DollarSign className="h-5 w-5 text-yellow-400" />} 
            description={t('stats.totalEarningsDescription')}
        />
        <StatCard 
            title={t('stats.totalTips')} 
            value={earnings?.tip_count?.toLocaleString() || '0'} 
            icon={<Users className="h-5 w-5 text-green-400" />} 
            description={t('stats.totalTipsDescription', { count: earnings?.tip_count || 0 })}
        />
        <StatCard 
            title={t('stats.totalStreams')} 
            value={totalStreams} 
            icon={<Video className="h-5 w-5 text-blue-400" />} 
            description={t('stats.totalStreamsDescription', { count: totalStreams })}
        />
        <StatCard 
            title={t('stats.nftsMinted')} 
            value={totalNftsMinted} 
            icon={<Sparkles className="h-5 w-5 text-purple-400" />} 
            description={t('stats.nftsMintedDescription', { count: totalNftsMinted })}
        />
      </div>

      {/* Active Stream Section & Recent Activity */}
      <div className="grid gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-2 bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">{t('recentActivity.title')}</CardTitle>
            <CardDescription className="text-gray-400">{t('recentActivity.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Placeholder for recent activity - tips, new followers, new mints etc. */}
            {earnings?.recent_tips && earnings.recent_tips.length > 0 ? (
                <ul className="space-y-3">
                    {earnings.recent_tips.slice(0, 5).map(tip => (
                        <li key={tip.id} className="flex justify-between items-center text-sm p-2 bg-gray-700/50 rounded-md">
                            <span className="text-gray-300">{t('recentActivity.tipReceived', { user: tip.tipper_username || commonT('anonymousUser') })}</span>
                            <span className="font-semibold text-yellow-400">{formatBonkAmount(tip.amount)} BONK</span>
                        </li>
                    ))}
                </ul>
            ) : (
                <p className="text-gray-500 text-center py-4">{t('recentActivity.noRecentActivity')}</p>
            )}
          </CardContent>
        </Card>

        {activeStream ? (
            <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                    <CardTitle className="text-white">{t('activeStream.title')}</CardTitle>
                    <CardDescription className="text-gray-400">{activeStream.title}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                    {activeStream.thumbnail_url && (
                      <div className="relative aspect-video rounded-md overflow-hidden">
                        <Image src={activeStream.thumbnail_url} alt={activeStream.title} fill className="object-cover" sizes="(max-width: 768px) 100vw, 50vw" />
                      </div>
                    )}
                    <p className="text-sm text-gray-300"><span className="font-semibold">{t('activeStream.status')}:</span> <span className={activeStream.is_actually_live ? "text-green-400" : "text-gray-400"}>{activeStream.is_actually_live ? "LIVE" : "ENDED"}</span></p>
                    <p className="text-sm text-gray-300"><span className="font-semibold">{t('activeStream.views')}:</span> {activeStream.views || 0}</p>
                </CardContent>
            </Card>
        ) : (
            <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                    <CardTitle className="text-white">{t('activeStream.noActiveStreamTitle')}</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                    <Video className="mx-auto h-12 w-12 text-gray-500 mb-3" />
                    <p className="text-sm text-gray-400 mb-4">{t('activeStream.noActiveStreamDescription')}</p>

                </CardContent>
            </Card>
        )}
      </div>

      {/* Placeholder for a simple earnings chart */}
      {/* <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">{t('earningsChart.title')}</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          <div className="flex items-center justify-center h-full">
            <BarChartBig className="w-16 h-16 text-gray-600"/>
            <p className="ml-4 text-gray-500">{t('earningsChart.placeholder')}</p>
          </div>
           Add a simple chart component here if available, e.g., from recharts or similar 
        </CardContent>
      </Card> */}
    </div>
  );
} 