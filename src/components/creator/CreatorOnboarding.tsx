'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2 } from 'lucide-react';
import { Link as I18nLink } from '@/lib/i18n'; // Assuming you have this for localized links

type Profile = {
  id: string;
  wallet_address: string;
  username?: string;
  avatar_url?: string;
  is_creator: boolean;
  created_at: string;
};

interface CreatorOnboardingProps {
  profile: Profile;
  locale: string;
  onOnboardingComplete: (updatedProfile: Profile) => void;
}

// Define request info interface for type safety
interface RequestInfo {
  url: string;
  method: string;
  wallet: string;
  timestamp: string;
  statusCode?: number;
  statusText?: string;
}

export function CreatorOnboarding({ profile, locale, onOnboardingComplete }: CreatorOnboardingProps) {
  const t = useTranslations('creator.onboarding');
  const [step, setStep] = useState(1);
  const [displayName, setDisplayName] = useState(profile.username || '');
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [acceptFees, setAcceptFees] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [debugInfo, setDebugInfo] = useState<string | null>(null);

  useEffect(() => {
    // Pre-fill display name if profile username exists, or use a default based on wallet
    if (profile.username) {
      setDisplayName(profile.username);
    } else if (profile.wallet_address) {
      setDisplayName(`user-${profile.wallet_address.substring(0, 4)}`);
    }
  }, [profile.username, profile.wallet_address]);

  const validateStep2 = () => {
    const errors: Record<string, string> = {};
    if (!displayName.trim()) {
      errors.displayName = t('errors.displayNameRequired');
    } else if (displayName.trim().length < 3) {
      errors.displayName = t('errors.displayNameMinLength');
    }
    if (!acceptTerms) {
      errors.acceptTerms = t('errors.termsRequired');
    }
    if (!acceptFees) {
      errors.acceptFees = t('errors.feesRequired');
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleBecomeCreator = () => {
    setStep(2);
  };

  const handleFinalizeOnboarding = async () => {
    if (!validateStep2()) {
      return;
    }
    
    setLoading(true);
    setError(null);
    setDebugInfo(null);
    
    // Store debug info for potential errors
    const requestInfo: RequestInfo = {
      url: `/${locale}/api/profile`,
      method: 'PUT',
      wallet: profile.wallet_address,
      timestamp: new Date().toISOString()
    };
    
    try {
      
      // Attempt profile update
      const response = await fetch(`/${locale}/api/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wallet_address: profile.wallet_address,
          username: displayName, 
          avatar_url: profile.avatar_url || '',
          is_creator: true, // Mark user as creator
        }),
        credentials: 'include',
      });
      
      // Get full response details
      const responseText = await response.text();
      requestInfo.statusCode = response.status;
      requestInfo.statusText = response.statusText;
      
      if (!response.ok) {
        console.error("Profile update failed:", response.status, response.statusText);
        console.error("Response body:", responseText);
        
        let errorMessage = t('errors.onboardingFailed');
        let errorDetails = null;
        
        try {
          // Try to parse as JSON to get detailed error
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.message || t('errors.onboardingFailed');
          errorDetails = errorData.details || null;
          
          setDebugInfo(JSON.stringify({
            status: response.status,
            message: errorMessage,
            details: errorDetails
          }, null, 2));
        } catch (parseError) {
          console.error("Error parsing JSON response:", parseError);
          setDebugInfo(`Non-JSON response (${response.status}): ${responseText.substring(0, 100)}...`);
        }
        
        throw new Error(errorMessage);
      }
      
      // Try to parse successful response
      try {
        const updatedProfileData = JSON.parse(responseText);
        
        if (!updatedProfileData.profile) {
          console.error("Response lacks profile data:", updatedProfileData);
          setDebugInfo(JSON.stringify(updatedProfileData, null, 2));
          throw new Error('No profile data returned from server');
        }
        
        onOnboardingComplete(updatedProfileData.profile);
      } catch (parseError) {
        console.error("Error parsing successful response:", parseError);
        setDebugInfo(`Parse error for ${response.status} response: ${responseText.substring(0, 100)}...`);
        throw new Error('Failed to parse server response');
      }
    } catch (err) {
      console.error('Onboarding error:', err);
      
      // Set main error for the user
      setError((err as Error).message || t('errors.onboardingFailed'));
      
      // If we don't have debug info yet, add request details
      if (!debugInfo) {
        setDebugInfo(JSON.stringify(requestInfo, null, 2));
      }
    } finally {
      setLoading(false);
    }
  };

  if (step === 1) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center p-6 bonk-widget rounded-lg shadow-xl border border-bonk-orange/20">
        <div className="w-24 h-24 rounded-full bg-bonk-orange-red-2 flex items-center justify-center mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </div>
        <h2 className="bonk-header text-3xl mb-4 text-white">Welcome to bonkstream!</h2>
        <p className="bonk-body text-white/80 mb-6 max-w-md">
          Set up your profile to start streaming, earning BONK tips, and connecting with your audience.
        </p>
        <p className="bonk-body text-white/60 mb-8 text-sm max-w-md">
          No complicated setup required - just a username and you&apos;re ready to go live!
        </p>
        <Button
          onClick={handleBecomeCreator}
          size="lg"
          className="bonk-btn bg-bonk-orange-red-2 text-white hover:bg-bonk-orange px-8 py-3 text-lg"
        >
          Set Up Profile
        </Button>
      </div>
    );
  }

  if (step === 2) {
    return (
      <div className="w-full max-w-lg mx-auto p-8 bonk-widget rounded-lg shadow-xl border border-bonk-orange/20">
        <h2 className="bonk-header text-2xl mb-6 text-center text-white">Complete Your Profile</h2>
        
        {error && (
          <div className="mb-4 p-3 bg-red-900/30 border border-red-500/50 rounded-md text-center">
            <p className="text-red-400 text-sm">{error}</p>
            {debugInfo && (
              <details className="mt-2 text-left">
                <summary className="text-xs text-gray-400 cursor-pointer">Debug Information</summary>
                <pre className="mt-2 p-2 bg-gray-950 text-gray-400 text-xs overflow-auto max-h-40 rounded-sm">
                  {debugInfo}
                </pre>
              </details>
            )}
          </div>
        )}

        <div className="space-y-6">
          <div>
            <Label htmlFor="displayName" className="bonk-body text-white">Username</Label>
            <Input
              id="displayName"
              type="text"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              placeholder="Choose a unique username"
              className={`mt-1 bg-bonk-widget-black border-bonk-orange/30 focus:border-bonk-orange focus:ring-bonk-orange bonk-body text-white ${formErrors.displayName ? 'border-red-500' : ''}`}
            />
            {formErrors.displayName && <p className="text-red-500 text-xs mt-1">{formErrors.displayName}</p>}
            <p className="text-xs text-white/50 mt-1">This will be your public display name on bonkstream</p>
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="acceptTerms"
                checked={acceptTerms}
                onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
                className={`mt-1 ${formErrors.acceptTerms ? 'border-red-500' : 'border-bonk-orange/30 data-[state=checked]:bg-bonk-orange-red-2 data-[state=checked]:text-white'}`}
              />
              <div className="grid gap-1.5 leading-none">
                <label
                  htmlFor="acceptTerms"
                  className={`bonk-body text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${formErrors.acceptTerms ? 'text-red-400' : 'text-white'}`}
                >
                  I agree to the Terms of Service
                </label>
                <p className="text-xs text-white/60">
                  <I18nLink href="/legal/terms" locale={locale} className="text-bonk-orange hover:text-bonk-yellow">
                    Terms of Service
                  </I18nLink>
                  {' & '}
                  <I18nLink href="/legal/content-guidelines" locale={locale} className="text-bonk-orange hover:text-bonk-yellow">
                    Content Guidelines
                  </I18nLink>.
                </p>
              </div>
            </div>
            {formErrors.acceptTerms && <p className="text-red-500 text-xs ml-7">{formErrors.acceptTerms}</p>}
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="acceptFees"
                checked={acceptFees}
                onCheckedChange={(checked) => setAcceptFees(checked as boolean)}
                className={`mt-1 ${formErrors.acceptFees ? 'border-red-500' : 'border-bonk-orange/30 data-[state=checked]:bg-bonk-orange-red-2 data-[state=checked]:text-white'}`}
              />
              <div className="grid gap-1.5 leading-none">
                <label
                  htmlFor="acceptFees"
                  className={`bonk-body text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${formErrors.acceptFees ? 'text-red-400' : 'text-white'}`}
                >
                  I understand the platform fee structure
                </label>
                <p className="text-xs text-white/60">
                  Platform takes 5% fee from tips to maintain the service.{' '}
                  <I18nLink href="/legal/platform-fee" locale={locale} className="text-bonk-orange hover:text-bonk-yellow">
                    Learn more
                  </I18nLink>.
                </p>
              </div>
            </div>
            {formErrors.acceptFees && <p className="text-red-500 text-xs ml-7">{formErrors.acceptFees}</p>}
          </div>
          
          <Button
            onClick={handleFinalizeOnboarding}
            disabled={loading}
            className="bonk-btn w-full bg-bonk-orange-red-2 text-white hover:bg-bonk-orange py-3 text-base"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Setting up...
              </>
            ) : (
              'Complete Setup'
            )}
          </Button>
        </div>
      </div>
    );
  }

  return null; // Should not happen
} 