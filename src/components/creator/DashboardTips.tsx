'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { ExternalLink, ArrowUpDown, Coins } from 'lucide-react';
import { BONK_DECIMALS } from '@/lib/bonk';

interface Tip {
  id: string;
  amount: number; // In lamports (needs conversion)
  platform_fee_amount?: number; // In lamports (needs conversion)
  creator_amount?: number; // In lamports (needs conversion)
  tx_signature: string;
  created_at: string;
  tipper_id?: string; // Could be wallet address or profile ID
  tipper_username?: string; // Denormalized for display
  stream_title?: string; // Denormalized for display
}

interface DashboardTipsProps {
  tips: Tip[];
  locale: string;
}

const formatDate = (dateString: string, locale: string) => {
  return new Date(dateString).toLocaleString(locale, {
    year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' 
  });
};

const formatBonkAmount = (amountInLamports: number = 0) => {
    // Convert from lamports to BONK (divide by 10^5)
    const bonkAmount = amountInLamports / Math.pow(10, BONK_DECIMALS);
    return bonkAmount.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 5 });
};

const viewOnExplorer = (txSignature: string) => {
    const baseUrl = process.env.NEXT_PUBLIC_SOLANA_EXPLORER_URL || 'https://explorer.solana.com/tx';
    // Ensure NEXT_PUBLIC_SOLANA_NETWORK is set in your .env.local (e.g., 'devnet')
    const networkParam = process.env.NEXT_PUBLIC_SOLANA_NETWORK === 'mainnet-beta' ? '' : `?cluster=${process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'devnet'}`;
    window.open(`${baseUrl}/${txSignature}${networkParam}`, '_blank');
};

export function DashboardTips({ tips, locale }: DashboardTipsProps) {
  const t = useTranslations('creator.dashboard.tips');
  const commonT = useTranslations('common');
  const [sortColumn, setSortColumn] = useState<'created_at' | 'amount'>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const sortedTips = [...tips].sort((a, b) => {
    let comparison = 0;
    if (sortColumn === 'amount') {
      comparison = (a.creator_amount || a.amount) - (b.creator_amount || b.amount);
    } else { // Default to created_at
      comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    }
    return sortDirection === 'asc' ? comparison : -comparison;
  });

  const handleSort = (column: 'created_at' | 'amount') => {
    if (sortColumn === column) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('desc');
    }
  };

  const getSortIndicator = (column: 'created_at' | 'amount') => {
    if (sortColumn === column) {
      return sortDirection === 'asc' ? <ArrowUpDown className="ml-2 h-3 w-3 inline-block transform rotate-180" /> : <ArrowUpDown className="ml-2 h-3 w-3 inline-block" />;
    }
    return <ArrowUpDown className="ml-2 h-3 w-3 inline-block opacity-30" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-white">{t('title')}</h2>
        {/* Add export button or filters if needed */}
      </div>

      {sortedTips.length > 0 ? (
        <div className="overflow-x-auto bg-gray-800 border border-gray-700 rounded-lg">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-700 hover:bg-gray-700/30">
                <TableHead 
                    className="cursor-pointer text-gray-300 hover:text-white"
                    onClick={() => handleSort('created_at')}
                >
                    {t('table.date')} {getSortIndicator('created_at')}
                </TableHead>
                <TableHead className="text-gray-300">{t('table.tipper')}</TableHead>
                <TableHead className="text-gray-300 hidden md:table-cell">{t('table.stream')}</TableHead>
                <TableHead 
                    className="text-right cursor-pointer text-gray-300 hover:text-white"
                    onClick={() => handleSort('amount')}
                >
                    {t('table.amountNet')} {getSortIndicator('amount')}
                </TableHead>
                <TableHead className="text-right text-gray-300 hidden sm:table-cell">{t('table.platformFee')}</TableHead>
                <TableHead className="text-right text-gray-300"><span className="sr-only">{t('table.explorer')}</span></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedTips.map((tip) => (
                <TableRow key={tip.id} className="border-gray-700 hover:bg-gray-700/30">
                  <TableCell className="text-gray-300">{formatDate(tip.created_at, locale)}</TableCell>
                  <TableCell className="text-gray-300">{tip.tipper_username || commonT('anonymousUser')}</TableCell>
                  <TableCell className="text-gray-300 hidden md:table-cell">{tip.stream_title || '-'}</TableCell>
                  <TableCell className="text-right font-medium text-yellow-400">
                    {formatBonkAmount(tip.creator_amount || tip.amount)} BONK
                  </TableCell>
                  <TableCell className="text-right text-gray-400 hidden sm:table-cell">
                    {tip.platform_fee_amount ? `${formatBonkAmount(tip.platform_fee_amount)} BONK` : '-'}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button 
                        variant="outline"
                        size="icon"
                        className="h-8 w-8 border-gray-600 hover:bg-gray-700 text-gray-300"
                        onClick={() => viewOnExplorer(tip.tx_signature)}
                        title={t('table.viewOnExplorerTitle')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-800 border border-gray-700 rounded-lg">
          <Coins className="mx-auto h-16 w-16 text-gray-500 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">{t('noTips.title')}</h3>
          <p className="text-gray-400">{t('noTips.description')}</p>
        </div>
      )}
      {/* Add pagination if many tips */}
    </div>
  );
} 