'use client';

import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { BarChartHorizontalBig, TrendingUp, Users, Eye } from 'lucide-react';

// Define basic structures for props, can be expanded as API provides more data
interface AnalyticsData {
  totalViews?: number;
  uniqueViewers?: number;
  averageWatchTime?: number; // in seconds
  topPerformingStream?: { title: string; views: number };
  earningsTrend?: { date: string; amount: number }[]; // for a small chart
}

interface DashboardAnalyticsProps {
  analyticsData: AnalyticsData | null; // This would come from a dedicated analytics API endpoint
  locale: string;
}

const StatDisplay = ({ label, value, unit, icon }: { label: string; value: string | number; unit?: string; icon?: React.ReactNode }) => (
    <div className="p-4 bg-gray-800/50 rounded-lg flex items-center">
        {icon && <div className="mr-3 text-blue-400">{icon}</div>}
        <div>
            <div className="text-xs text-gray-400">{label}</div>
            <div className="text-lg font-semibold text-white">
                {value} {unit && <span className="text-sm font-normal text-gray-300">{unit}</span>}
            </div>
        </div>
    </div>
);

export function DashboardAnalytics({ analyticsData }: DashboardAnalyticsProps) {
  const t = useTranslations('creator.dashboard.analytics');

  // Mock data 
  const displayData = analyticsData || {
    totalViews: 0,
    uniqueViewers: 0,
    averageWatchTime: 0,
    topPerformingStream: { title: t('mock.noData'), views: 0 },
    earningsTrend: [],
  };

  const formatWatchTime = (seconds: number = 0) => {
    const m = Math.floor(seconds / 60);
    const s = Math.floor(seconds % 60);
    return `${m}m ${s}s`;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-white">{t('title')}</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <StatDisplay label={t('stats.totalViews')} value={displayData.totalViews?.toLocaleString() || '0'} icon={<Eye className="w-5 h-5" />} />
        <StatDisplay label={t('stats.uniqueViewers')} value={displayData.uniqueViewers?.toLocaleString() || '0'} icon={<Users className="w-5 h-5" />} />
        <StatDisplay label={t('stats.avgWatchTime')} value={formatWatchTime(displayData.averageWatchTime)} icon={<TrendingUp className="w-5 h-5" />} />
      </div>

      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">{t('charts.earningsTrendTitle')}</CardTitle>
          <CardDescription className="text-gray-400">{t('charts.earningsTrendDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
            <div className="text-center text-gray-500">
                <BarChartHorizontalBig className="mx-auto h-16 w-16 mb-3" />
                <p>{t('charts.placeholder')}</p>
                <p className="text-xs">{t('charts.dataUnavailable')}</p>
            </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">{t('highlights.topStreamTitle')}</CardTitle>
        </CardHeader>
        <CardContent>
          {displayData.topPerformingStream && displayData.topPerformingStream.views > 0 ? (
            <div>
              <p className="text-lg font-medium text-blue-300">{displayData.topPerformingStream.title}</p>
              <p className="text-gray-400">{t('highlights.views', { count: displayData.topPerformingStream.views.toLocaleString() })}</p>
            </div>
          ) : (
            <p className="text-gray-500">{t('highlights.noTopStream')}</p>
          )}
        </CardContent>
      </Card>
       <p className="text-center text-sm text-gray-500">{t('dataNotice')}</p>
    </div>
  );
} 