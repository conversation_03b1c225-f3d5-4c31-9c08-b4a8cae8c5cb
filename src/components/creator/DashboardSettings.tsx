'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea'; // For Bio
import { Loader2 } from 'lucide-react';
import { useWallet } from '@solana/wallet-adapter-react'; // To ensure user is connected
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

interface Profile {
  id: string;
  wallet_address: string;
  username?: string;
  avatar_url?: string;
  is_creator: boolean;
  created_at: string;
  bio?: string; // Adding bio here for the form, though it might not be in the core DB schema yet
}

interface DashboardSettingsProps {
  profile: Profile | null;
  locale: string;
  onProfileUpdate: (updatedProfile: Profile) => void;
}

export function DashboardSettings({ profile, locale, onProfileUpdate }: DashboardSettingsProps) {
  const t = useTranslations('creator.dashboard.settings');
  const { publicKey } = useWallet(); // Use to verify ownership if needed, though API should handle this

  const [displayName, setDisplayName] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');
  const [bio, setBio] = useState(''); // For creator's bio
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    if (profile) {
      setDisplayName(profile.username || '');
      setAvatarUrl(profile.avatar_url || '');
      setBio(profile.bio || ''); // Assuming bio might come from profile if it exists
    }
  }, [profile]);

  const handleSaveChanges = async () => {
    if (!profile || !publicKey) {
      setError(t('errors.notConnectedOrNoProfile'));
      return;
    }

    // Basic validation
    if (!displayName.trim()) {
        setError(t('errors.displayNameRequired'));
        return;
    }
    if (displayName.trim().length < 3) {
        setError(t('errors.displayNameMinLength'));
        return;
    }
    if (avatarUrl && !avatarUrl.startsWith('http')) {
        setError(t('errors.avatarUrlInvalid'));
        return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await fetch(`/${locale}/api/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          wallet_address: profile.wallet_address, // Required field
          username: displayName, // This is the creator's public display name
          avatar_url: avatarUrl,
          bio: bio, // Sending bio, backend needs to handle it
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t('errors.updateFailed'));
      }

      const updatedProfileData = await response.json();
      onProfileUpdate(updatedProfileData.profile); // Update parent state
      setSuccessMessage(t('success.profileUpdated'));
    } catch (err) {
      console.error('Profile update error:', err);
      setError((err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  if (!profile) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      <div>
        <h2 className="text-2xl font-semibold text-white">{t('title')}</h2>
        <p className="text-gray-400 mt-1">{t('description')}</p>
      </div>

      {error && (
        <div className="p-3 bg-red-900/30 border border-red-500/50 rounded-md text-center">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}
      {successMessage && (
        <div className="p-3 bg-green-900/30 border border-green-500/50 rounded-md text-center">
          <p className="text-green-400 text-sm">{successMessage}</p>
        </div>
      )}

      <Card className="bonk-widget border-bonk-orange/20">
        <CardHeader>
            <CardTitle className="bonk-header text-lg text-white">{t('sections.profileInfo.title')}</CardTitle>
            <CardDescription className="bonk-body text-white/60">{t('sections.profileInfo.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
            <div>
                <Label htmlFor="displayName" className="bonk-body text-white">{t('fields.displayName.label')}</Label>
                <Input
                id="displayName"
                type="text"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder={t('fields.displayName.placeholder')}
                className="mt-1 bg-bonk-widget-black border-bonk-orange/30 focus:border-bonk-orange focus:ring-bonk-orange bonk-body text-white"
                />
                 <p className="text-xs text-white/50 mt-1">{t('fields.displayName.hint')}</p>
            </div>
            <div>
                <Label htmlFor="avatarUrl" className="bonk-body text-white">{t('fields.avatarUrl.label')}</Label>
                <Input
                id="avatarUrl"
                type="url"
                value={avatarUrl}
                onChange={(e) => setAvatarUrl(e.target.value)}
                placeholder={t('fields.avatarUrl.placeholder')}
                className="mt-1 bg-bonk-widget-black border-bonk-orange/30 focus:border-bonk-orange focus:ring-bonk-orange bonk-body text-white"
                />
            </div>
             <div>
                <Label htmlFor="bio" className="bonk-body text-white">{t('fields.bio.label')}</Label>
                <Textarea
                    id="bio"
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    placeholder={t('fields.bio.placeholder')}
                    className="mt-1 bg-bonk-widget-black border-bonk-orange/30 focus:border-bonk-orange focus:ring-bonk-orange bonk-body text-white min-h-[100px]"
                    rows={4}
                />
                <p className="text-xs text-white/50 mt-1">{t('fields.bio.hint')}</p>
            </div>
        </CardContent>
      </Card>
      
      {/* Placeholder for Social Links section if needed, can reuse parts of creator/register/page.tsx form */}
      {/* <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
            <CardTitle className="text-lg text-white">{t('sections.socialLinks.title')}</CardTitle>
            <CardDescription className="text-gray-400">{t('sections.socialLinks.description')}</CardDescription>
        </CardHeader>
        <CardContent>
            <p className="text-gray-500">Social links editing coming soon.</p>
        </CardContent>
      </Card> */}

      <div className="flex justify-end">
        <Button onClick={handleSaveChanges} disabled={loading} className="bonk-btn bg-bonk-orange-red-2 text-white hover:bg-bonk-orange">
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            t('saveChangesButton')
          )}
        </Button>
      </div>
    </div>
  );
} 