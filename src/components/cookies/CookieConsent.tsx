'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { X, Settings, Check } from 'lucide-react';
import {
  CookiePreferences,
  getCookiePreferences,
  applyCookiePreferences,
  hasGivenCookieConsent
} from '@/lib/cookies';
import { useTranslations } from 'next-intl';

function useTranslationsWithFallback(namespace: string) {
  try {
    return useTranslations(namespace);
  } catch (error) {
    return (key: string) => key;
  }
}

export default function CookieConsent() {
  const t = useTranslationsWithFallback('common');
  const [showBanner, setShowBanner] = useState(false);
  const [showPreferencesModal, setShowPreferencesModal] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    essential: true, // Essential cookies cannot be disabled
    preferences: false,
    analytics: false,
  });

  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const savedPreferences = getCookiePreferences();
    setPreferences(savedPreferences);

    setShowBanner(!hasGivenCookieConsent());
  }, []);
  
  const savePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem('cookie-consent', JSON.stringify(prefs));

    applyCookiePreferences(prefs);

    window.dispatchEvent(new CustomEvent('cookie-preferences-changed'));
    
    setPreferences(prefs);
    setShowBanner(false);
    setShowPreferencesModal(false);
  };
  
  const acceptAll = () => {
    const allAccepted = {
      essential: true,
      preferences: true,
      analytics: true,
    };
    savePreferences(allAccepted);
  };
  
  const essentialOnly = () => {
    const essentialOnly = {
      essential: true,
      preferences: false,
      analytics: false,
    };
    savePreferences(essentialOnly);
  };
  
  const openPreferences = () => {
    setShowPreferencesModal(true);
  };
  
  const handlePreferenceChange = (category: keyof CookiePreferences) => {
    if (category === 'essential') return; // Essential cannot be toggled
    
    setPreferences({
      ...preferences,
      [category]: !preferences[category],
    });
  };
  
  const saveCurrentPreferences = () => {
    savePreferences(preferences);
  };
  
  // Preferences modal component
  const PreferencesModal = () => (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-auto">
        <div className="p-4 border-b border-white/10 flex justify-between items-center">
          <h2 className="text-xl font-semibold">{t('cookies.preferences')}</h2>
          <button 
            onClick={() => setShowPreferencesModal(false)} 
            className="text-white/60 hover:text-white p-2"
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          <div>
            <p className="text-white/80 mb-4">
              {t('cookies.enhance_experience')}
              {' '}
              <Link href="/legal/cookies" className="text-[#FFD700] hover:underline">
                {t('cookies.policy_link')}
              </Link>.
            </p>
          </div>
          
          {/* Essential cookies */}
          <div className="border border-white/10 rounded-lg overflow-hidden">
            <div 
              className="p-4 bg-white/5 flex justify-between items-center"
            >
              <div>
                <h3 className="font-medium">{t('cookies.essential.title')}</h3>
                <p className="text-sm text-white/60">{t('cookies.essential.subtitle')}</p>
              </div>
              <div className="bg-gray-700 p-1 px-2 rounded text-xs">{t('cookies.always_on')}</div>
            </div>
            <div className="p-4 text-sm text-white/70">
              <p>{t('cookies.essential.description')}</p>
            </div>
          </div>
          
          {/* Preference cookies */}
          <div className="border border-white/10 rounded-lg overflow-hidden">
            <div 
              className="p-4 bg-white/5 flex justify-between items-center cursor-pointer"
              onClick={() => handlePreferenceChange('preferences')}
            >
              <div>
                <h3 className="font-medium">{t('cookies.preferences_category.title')}</h3>
                <p className="text-sm text-white/60">{t('cookies.preferences_category.subtitle')}</p>
              </div>
              <div 
                className={`w-12 h-6 rounded-full relative transition-colors ${
                  preferences.preferences ? 'bg-[#FFD700]' : 'bg-gray-700'
                }`}
              >
                <div 
                  className={`absolute top-1 w-4 h-4 rounded-full bg-white transition-all ${
                    preferences.preferences ? 'right-1' : 'left-1'
                  }`}
                />
              </div>
            </div>
            <div className="p-4 text-sm text-white/70">
              <p>{t('cookies.preferences_category.description')}</p>
            </div>
          </div>
          
          {/* Analytics cookies */}
          <div className="border border-white/10 rounded-lg overflow-hidden">
            <div 
              className="p-4 bg-white/5 flex justify-between items-center cursor-pointer"
              onClick={() => handlePreferenceChange('analytics')}
            >
              <div>
                <h3 className="font-medium">{t('cookies.analytics.title')}</h3>
                <p className="text-sm text-white/60">{t('cookies.analytics.subtitle')}</p>
              </div>
              <div 
                className={`w-12 h-6 rounded-full relative transition-colors ${
                  preferences.analytics ? 'bg-[#FFD700]' : 'bg-gray-700'
                }`}
              >
                <div 
                  className={`absolute top-1 w-4 h-4 rounded-full bg-white transition-all ${
                    preferences.analytics ? 'right-1' : 'left-1'
                  }`}
                />
              </div>
            </div>
            <div className="p-4 text-sm text-white/70">
              <p>{t('cookies.analytics.description')}</p>
            </div>
          </div>
        </div>
        
        <div className="p-4 border-t border-white/10 flex justify-between gap-4">
          <div>
            <button 
              onClick={essentialOnly}
              className="text-sm py-2 px-4 border border-white/30 rounded-md hover:bg-white/10 transition"
            >
              {t('cookies.reject_all')}
            </button>
          </div>
          <div className="flex gap-2">
            <button 
              onClick={acceptAll}
              className="text-sm py-2 px-4 border border-white/30 rounded-md hover:bg-white/10 transition"
            >
              {t('cookies.accept_all')}
            </button>
            <button 
              onClick={saveCurrentPreferences}
              className="text-sm py-2 px-4 bg-[#FFD700] text-black rounded-md hover:bg-[#E6C300] transition flex items-center gap-1"
            >
              <Check size={16} />
              {t('cookies.save_preferences')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
  
  // Don't render anything on server side
  if (!mounted) {
    return null;
  }

  // Main cookie consent banner
  if (!showBanner && !showPreferencesModal) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={openPreferences}
          className="bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition"
          aria-label={t('cookies.settings')}
        >
          <Settings size={20} />
        </button>
      </div>
    );
  }

  return (
    <>
      {showPreferencesModal && <PreferencesModal />}

      {showBanner && (
        <div className="fixed bottom-0 left-0 right-0 bg-black/95 text-white p-4 shadow-lg z-50">
          <div className="container mx-auto max-w-4xl">
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">{t('cookies.value_privacy')}</h3>
                <p className="text-sm text-white/80">
                  {t('cookies.banner_description')}{' '}
                  <Link href="/legal/cookies" className="text-[#FFD700] hover:underline">
                    {t('cookies.policy_link')}
                  </Link>.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  onClick={essentialOnly}
                  className="text-sm py-2 px-4 border border-white/30 rounded-md hover:bg-white/10 transition"
                >
                  {t('cookies.reject_all')}
                </button>
                <button
                  onClick={acceptAll}
                  className="text-sm py-2 px-4 bg-[#FFD700] text-black rounded-md hover:bg-[#E6C300] transition flex items-center gap-1"
                >
                  {t('cookies.accept_all')}
                </button>
                <button
                  onClick={openPreferences}
                  className="text-sm py-2 px-4 text-white/80 hover:text-white transition flex items-center gap-1"
                >
                  <Settings size={16} />
                  {t('cookies.customize')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}