'use client';

import { useState, useCallback, useEffect } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/wallet/WalletButton';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Transaction } from '@solana/web3.js';
import { AlertCircle, CheckCircle, Loader2, Wallet, Users, Gift } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { 
  getBonkBalance, 
  checkSufficientBalance,
  getBonkToSolRate
} from '@/lib/bonk';
import confetti from 'canvas-confetti';

type TipButtonProps = {
  streamId: string;
  creatorWalletAddress: string;
  creatorName?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  onSuccess?: () => void;
};

type TransactionStatus = 'idle' | 'creating' | 'signing' | 'confirming' | 'success' | 'error';

export function TipButton({ 
  streamId, 
  creatorWalletAddress, 
  creatorName,
  className = '',
  onSuccess
}: TipButtonProps) {
  const { connected, publicKey, signTransaction, sendTransaction } = useWallet();
  const { connection } = useConnection();
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string || 'en';
  
  const [isOpen, setIsOpen] = useState(false);
  const [amount, setAmount] = useState('');
  const [tipperName, setTipperName] = useState('');
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState<TransactionStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [bonkBalance, setBonkBalance] = useState<number>(0);
  const [bonkToSolRate, setBonkToSolRate] = useState<number>(0);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);
  const [tipData, setTipData] = useState<{
    platformFeePercentage: number;
    platformFeeAmount: number;
    creatorAmount: number;
    transactionSignature?: string;
    explorerUrl?: string;
  } | null>(null);

  // Predefined tip amounts
  const tipOptions = [10, 50, 100, 500, 1000];

  // Load BONK balance
  const loadBonkBalance = useCallback(async () => {
    if (!publicKey) return;
    
    try {
      const [balance, bonkPerSol] = await Promise.all([
        getBonkBalance(publicKey),
        getBonkToSolRate()
      ]);
      
      setBonkBalance(balance);
      
      // Calculate how much SOL the user's BONK is worth
      const solValue = balance / bonkPerSol;
      setBonkToSolRate(solValue);
      
    } catch (error) {
      console.error('❌ Error loading BONK balance:', error);
      setBonkBalance(0);
      setBonkToSolRate(0);
    }
  }, [publicKey]);

  // Load balance when wallet connects
  useEffect(() => {
    if (connected && publicKey && isOpen) {
      setIsLoadingBalance(true);
      loadBonkBalance().finally(() => setIsLoadingBalance(false));
    } else if (!connected) {
      setBonkBalance(0);
      setBonkToSolRate(0);
    }
  }, [connected, publicKey, isOpen, loadBonkBalance]);

  // Handle tip button click
  const handleOpenTipModal = () => {
    if (!connected) return;
    setIsOpen(true);
    setStatus('idle');
    setError(null);
    setTipperName('');
    setMessage('');
  };

  // Handle tip amount selection
  const handleSelectAmount = (value: number) => {
    setAmount(value.toString());
  };

  // Handle custom amount input
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow positive numbers with up to 6 decimal places (as per requirements)
    const value = e.target.value;
    if (/^\d*\.?\d{0,6}$/.test(value) || value === '') {
      setAmount(value);
    }
  };

  // Handle message input (max 280 chars)
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= 280) {
      setMessage(value);
    }
  };

  // Format BONK amount with up to 6 decimal places
  const formatBonk = useCallback((value: number) => {
    return value.toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 6,
    });
  }, []);

  // Trigger confetti animation
  const triggerConfetti = () => {
    // Main burst
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    });
    
    // Side bursts
    setTimeout(() => {
      confetti({
        particleCount: 50,
        angle: 60,
        spread: 55,
        origin: { x: 0 }
      });
    }, 100);
    
    setTimeout(() => {
      confetti({
        particleCount: 50,
        angle: 120,
        spread: 55,
        origin: { x: 1 }
      });
    }, 200);
  };

  // Process the tip
  const handleTip = useCallback(async () => {
    if (!amount || !connected || !publicKey || !signTransaction || !sendTransaction) return;
    
    const tipAmount = parseFloat(amount);
    if (isNaN(tipAmount) || tipAmount <= 0) {
      setError('Please enter a valid amount');
      return;
    }
    
    try {
      // Check sufficient balance
      setStatus('creating');
      setError(null);
      
      const balanceCheck = await checkSufficientBalance(publicKey, tipAmount);
      if (!balanceCheck.sufficient) {
        setError(`Insufficient BONK balance. You have ${formatBonk(balanceCheck.balance)} BONK but need ${formatBonk(balanceCheck.required)} BONK.`);
        setStatus('error');
        return;
      }
      
      // Create transaction
      const createResponse = await fetch(`/${locale}/api/tip`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          streamId,
          amount: tipAmount,
          creatorWalletAddress,
          fromWalletAddress: publicKey.toString(),
          tipperName: tipperName.trim() || undefined,
          message: message.trim() || undefined,
        }),
      });
      
      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        throw new Error(errorData.error || 'Failed to create transaction');
      }
      
      const { transaction: serializedTransaction, platformFeePercentage, platformFeeAmount, creatorAmount } = await createResponse.json();
      
      // Update state with fee data
      setTipData({
        platformFeePercentage,
        platformFeeAmount,
        creatorAmount,
      });
      
      // Deserialize transaction
      const transaction = Transaction.from(Buffer.from(serializedTransaction));
      
      // Sign transaction
      setStatus('signing');
      const signedTransaction = await signTransaction(transaction);
      
      // Send transaction
      setStatus('confirming');
      const signature = await sendTransaction(signedTransaction, connection);
      
      // Record transaction in database
      const recordResponse = await fetch(`/${locale}/api/tip`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          streamId,
          amount: tipAmount,
          creatorWalletAddress,
          fromWalletAddress: publicKey.toString(),
          transactionSignature: signature,
          platformFeePercentage,
          platformFeeAmount,
          creatorAmount,
          tipperName: tipperName.trim() || undefined,
          message: message.trim() || undefined,
        }),
      });
      
      if (!recordResponse.ok) {
        const errorData = await recordResponse.json();
        throw new Error(errorData.error || 'Failed to record transaction');
      }
      
      const responseData = await recordResponse.json();
      
      // Set success status and trigger confetti
      setStatus('success');
      setTipData(prev => prev ? { 
        ...prev, 
        transactionSignature: signature,
        explorerUrl: responseData.explorerUrl 
      } : null);
      triggerConfetti();
      
      // Update balance
      loadBonkBalance();
      
      // Call onSuccess callback if provided
      onSuccess?.();
      
      // Refresh the page after a delay
      setTimeout(() => {
        router.refresh();
      }, 3000);
    } catch (err) {
      console.error('Tip error:', err);
      setStatus('error');
      
      // Enhanced error handling with specific messages
      const errorMessage = (err as Error).message;
      if (errorMessage.includes('User rejected')) {
        setError('Transaction was cancelled by user.');
      } else if (errorMessage.includes('insufficient funds')) {
        setError('Insufficient funds to complete transaction. Please check your BONK balance and SOL for gas fees.');
      } else if (errorMessage.includes('blockhash')) {
        setError('Network error. Please try again in a moment.');
      } else if (errorMessage.includes('timeout')) {
        setError('Transaction timed out. Please check your network connection and try again.');
      } else {
        setError(errorMessage || 'An unexpected error occurred. Please try again.');
      }
    }
  }, [amount, connected, publicKey, signTransaction, sendTransaction, streamId, creatorWalletAddress, router, tipperName, message, locale, connection, loadBonkBalance, formatBonk, onSuccess]);

  // View transaction on explorer
  const viewTransaction = () => {
    if (!tipData?.explorerUrl && !tipData?.transactionSignature) return;
    
    // Use explorerUrl if available, otherwise construct it
    const explorerUrl = tipData.explorerUrl || (() => {
      const baseUrl = 'https://explorer.solana.com/tx';
      const networkParam = process.env.NEXT_PUBLIC_SOLANA_NETWORK === 'devnet' ? '?cluster=devnet' : '';
      return `${baseUrl}/${tipData.transactionSignature}${networkParam}`;
    })();
    
    window.open(explorerUrl, '_blank');
  };

  // Status component
  const StatusDisplay = () => {
    switch (status) {
      case 'creating':
        return (
          <div className="flex items-center justify-center p-6">
            <Loader2 className="w-8 h-8 mr-3 animate-spin text-[#FFD700]" />
            <div className="text-center">
              <p className="font-medium">Creating transaction...</p>
              <p className="text-sm text-gray-500 mt-1">Checking balance and preparing transaction</p>
            </div>
          </div>
        );
      case 'signing':
        return (
          <div className="flex items-center justify-center p-6">
            <Loader2 className="w-8 h-8 mr-3 animate-spin text-[#FFD700]" />
            <div className="text-center">
              <p className="font-medium">Please sign the transaction</p>
              <p className="text-sm text-gray-500 mt-1">Check your wallet for the signature request</p>
            </div>
          </div>
        );
      case 'confirming':
        return (
          <div className="flex items-center justify-center p-6">
            <Loader2 className="w-8 h-8 mr-3 animate-spin text-[#FFD700]" />
            <div className="text-center">
              <p className="font-medium">Confirming transaction...</p>
              <p className="text-sm text-gray-500 mt-1">Broadcasting to Solana network</p>
            </div>
          </div>
        );
      case 'success':
        return (
          <div className="text-center p-6">
            <CheckCircle className="w-16 h-16 mx-auto mb-4 text-green-500" />
            <h3 className="text-xl font-medium mb-2">🎉 Tip Successful!</h3>
            <p className="text-gray-600 mb-4">
              You sent {formatBonk(parseFloat(amount))} BONK to {creatorName || 'this creator'}!
            </p>
            {tipData?.transactionSignature && (
              <Button variant="outline" size="sm" onClick={viewTransaction} className="mb-2">
                View on Explorer
              </Button>
            )}
            <p className="text-xs text-gray-400">
              Your support means everything to creators! 🙏
            </p>
          </div>
        );
      case 'error':
        return (
          <div className="text-center p-6">
            <AlertCircle className="w-16 h-16 mx-auto mb-4 text-red-500" />
            <h3 className="text-xl font-medium mb-2">Transaction Failed</h3>
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
            <div className="space-y-2">
              <Button variant="outline" onClick={() => setStatus('idle')} className="w-full">
                Try Again
              </Button>
              <details className="text-xs text-gray-500">
                <summary className="cursor-pointer hover:text-gray-700">Troubleshooting</summary>
                <div className="mt-2 text-left">
                  <p>• Ensure your wallet has sufficient BONK tokens</p>
                  <p>• Check you have SOL for transaction fees</p>
                  <p>• Verify your network connection</p>
                  <p>• Try refreshing and connecting your wallet again</p>
                </div>
              </details>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      {connected ? (
        <button
          onClick={handleOpenTipModal}
          className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors font-bold ${className}`}
          id="tip-button"
          style={{ backgroundColor: '#FF5C01', ...(className?.includes('style') ? {} : { border: 'none' }) }}
          onMouseEnter={(e) => {
            if (!className?.includes('hover:')) {
              e.currentTarget.style.backgroundColor = '#e5530a';
            }
          }}
          onMouseLeave={(e) => {
            if (!className?.includes('hover:')) {
              e.currentTarget.style.backgroundColor = '#FF5C01';
            }
          }}
        >
        <Gift className="h-4 w-4 mr-2" />
          Send BONK
        </button>
      ) : (
        <WalletButton>Connect Wallet</WalletButton>
      )}
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md !top-[5%] !translate-y-0">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              💸 Tip {creatorName || 'Creator'} with BONK
            </DialogTitle>
          </DialogHeader>
          
          {status === 'idle' ? (
            <>
              <div className="grid gap-4 py-4">
                {/* BONK Balance Display */}
                {connected && (
                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Wallet className="w-4 h-4 text-gray-500" />
                        <span className="text-sm font-medium">Your BONK Balance</span>
                      </div>
                      {isLoadingBalance ? (
                        <Loader2 className="w-4 h-4 animate-spin text-gray-500" />
                      ) : (
                        <div className="text-right flex-shrink-0">
                          <div className="font-mono text-sm font-medium whitespace-nowrap">
                            {formatBonk(bonkBalance)} BONK
                          </div>
                          {bonkToSolRate > 0 && (
                            <div className="text-xs text-gray-500 whitespace-nowrap">
                              ≈ {bonkToSolRate.toFixed(6)} SOL
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    {bonkBalance === 0 && !isLoadingBalance && (
                      <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-2">
                        You don&apos;t have any BONK tokens. Buy some to tip creators!
                      </p>
                    )}
                  </div>
                )}

                
                {/* Predefined amounts */}
                <div className="grid grid-cols-3 gap-2">
                  {tipOptions.map((option) => (
                    <Button
                      key={option}
                      variant={amount === option.toString() ? 'default' : 'outline'}
                      onClick={() => handleSelectAmount(option)}
                      className="w-full text-sm"
                      disabled={bonkBalance < option}
                    >
                      {formatBonk(option)}
                    </Button>
                  ))}
                </div>
                
                {/* Custom amount input */}
                <div className="space-y-2">
                  <Label htmlFor="amount">Custom Amount (BONK)</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="amount"
                      value={amount}
                      onChange={handleAmountChange}
                      type="text"
                      placeholder="Enter BONK amount (max 6 decimals)"
                      className="flex-1 min-w-0"
                    />
                    <span className="font-medium text-sm whitespace-nowrap flex-shrink-0">BONK</span>
                  </div>
                  

                </div>
                
                {/* Optional name field */}
                <div className="space-y-2">
                  <Label htmlFor="tipperName">Your Name (Optional)</Label>
                  <Input
                    id="tipperName"
                    value={tipperName}
                    onChange={(e) => setTipperName(e.target.value)}
                    type="text"
                    placeholder="Enter your name (public)"
                    maxLength={50}
                  />
                </div>
                
                {/* Optional message field */}
                <div className="space-y-2">
                  <Label htmlFor="message">Message to Creator (Optional)</Label>
                  <textarea
                    id="message"
                    value={message}
                    onChange={handleMessageChange}
                    placeholder="Write a message to the creator (max 280 characters)"
                    className="w-full min-h-[80px] px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none dark:bg-gray-800 dark:border-gray-600"
                    maxLength={280}
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {message.length}/280
                  </div>
                </div>
                
                {/* Transaction fee preview */}
                {amount && !isNaN(parseFloat(amount)) && (
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-700 p-3 rounded-md space-y-1">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <Users className="w-4 h-4 text-yellow-600" />
                      BONK Transaction Preview
                    </h4>
                    <div className="flex justify-between text-sm items-center">
                      <span className="flex-shrink-0">Creator Receives (97.5%):</span>
                      <span className="font-medium text-green-600 whitespace-nowrap">{formatBonk(parseFloat(amount) * 0.975)} BONK</span>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 items-center">
                      <span className="flex-shrink-0">Platform Fee (5%):</span>
                      <span className="whitespace-nowrap">{formatBonk(parseFloat(amount) * 0.025)} BONK</span>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 items-center">
                      <span className="flex-shrink-0">Estimated Gas Fee:</span>
                      <span className="whitespace-nowrap">~0.001 SOL</span>
                    </div>
                    <div className="border-t border-yellow-200 dark:border-yellow-700 pt-1 mt-1 flex justify-between font-medium items-center">
                      <span className="flex-shrink-0">Total BONK to Send:</span>
                      <span className="text-yellow-700 dark:text-yellow-400 whitespace-nowrap">{formatBonk(parseFloat(amount))} BONK</span>
                    </div>
                    <div className="mt-2 text-xs text-gray-600 dark:text-gray-400 bg-white/60 dark:bg-black/20 p-2 rounded">
                      💡 Your wallet will show 2 BONK transfers: one to the creator and one platform fee.
                    </div>
                  </div>
                )}
                


                {/* Insufficient balance warning */}
                {amount && !isNaN(parseFloat(amount)) && parseFloat(amount) > bonkBalance && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                    <p className="text-red-800 dark:text-red-200 text-sm break-words">
                      ❌ Insufficient BONK balance. You need <span className="whitespace-nowrap">{formatBonk(parseFloat(amount) - bonkBalance)} more BONK.</span>
                    </p>
                    <p className="text-red-600 dark:text-red-300 text-xs mt-1 break-words">
                      Current balance: <span className="whitespace-nowrap">{formatBonk(bonkBalance)} BONK</span>
                    </p>
                  </div>
                )}
              </div>
              
              <DialogFooter className="gap-2">
                <Button variant="outline" onClick={() => setIsOpen(false)} className="flex-1">
                  Cancel
                </Button>
                <Button 
                  onClick={handleTip} 
                  disabled={
                    !amount || 
                    isNaN(parseFloat(amount)) || 
                    parseFloat(amount) <= 0 ||
                    parseFloat(amount) > bonkBalance ||
                    isLoadingBalance
                  }
                  className="flex-1 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black hover:from-yellow-500 hover:to-yellow-600"
                >
                  {isLoadingBalance ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Loading...</span>
                    </div>
                  ) : (
                    'Send BONK'
                  )}
                </Button>
              </DialogFooter>
            </>
          ) : (
            <StatusDisplay />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
} 