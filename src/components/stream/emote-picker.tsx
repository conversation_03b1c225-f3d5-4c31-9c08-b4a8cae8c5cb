'use client'

import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Smile } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

const EMOJI_CATEGORIES = {
  faces: ['😀', '😃', '😄', '😁', '😅', '😂', '🤣', '😊', '😇', '🙂', '😉', '😍', '🥰', '😘', '😗', '😙', '😚'],
  gestures: ['👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '👇', '☝️', '✋', '🤚'],
  symbols: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘'],
  activities: ['🎉', '🎊', '🎈', '🎁', '🏆', '🥇', '🥈', '🥉', '⚡', '🔥', '💎', '🚀', '🌙', '⭐', '✨', '💫']
}

const BONK_EMOTES = {
  'bonk_thumbs_up': { display: '👍', code: 'bonk_thumbs_up', name: 'Thumbs Up' },
  'bonk_fire': { display: '🔥', code: 'bonk_fire', name: 'Fire' },
  'bonk_rocket': { display: '🚀', code: 'bonk_rocket', name: 'To the Moon' },
  'bonk_diamond': { display: '💎', code: 'bonk_diamond', name: 'Diamond Hands' },
  'bonk_moon': { display: '🌙', code: 'bonk_moon', name: 'Moon' },
  'bonk_100': { display: '💯', code: 'bonk_100', name: '100' }
}

interface EmotePickerProps {
  onEmoteSelect: (emote: { type: 'emoji' | 'bonk_emote', code: string, display: string }) => void
  disabled?: boolean
}

export function EmotePicker({ onEmoteSelect, disabled }: EmotePickerProps) {
  const t = useTranslations('stream.chat')
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof EMOJI_CATEGORIES>('faces')
  const [isOpen, setIsOpen] = useState(false)

  const handleEmoteClick = (emote: string, type: 'emoji' | 'bonk_emote' = 'emoji') => {
    onEmoteSelect({ type, code: emote, display: emote })
    setIsOpen(false)
  }

  const handleBonkEmoteClick = (emoteData: typeof BONK_EMOTES[keyof typeof BONK_EMOTES]) => {
    onEmoteSelect({ 
      type: 'bonk_emote', 
      code: emoteData.code, 
      display: emoteData.display 
    })
    setIsOpen(false)
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          disabled={disabled}
          className="h-8 w-8 p-0 hover:bg-primary/10"
          title={t('addEmote')}
        >
          <Smile className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-2" align="end">
        <div className="space-y-2">
          {/* BONK Emotes Section */}
          <div>
            <h3 className="text-sm font-medium text-primary mb-2 px-2">
              {t('bonkEmotes')}
            </h3>
            <div className="grid grid-cols-6 gap-1 p-2 bg-primary/5 rounded-lg">
              {Object.values(BONK_EMOTES).map((emote) => (
                <Button
                  key={emote.code}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-primary/20 transition-colors"
                  onClick={() => handleBonkEmoteClick(emote)}
                  title={emote.name}
                >
                  <span className="text-lg">{emote.display}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Category Tabs */}
          <div className="flex gap-1 border-b">
            {Object.keys(EMOJI_CATEGORIES).map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "ghost"}
                size="sm"
                className="h-8 px-2 text-xs"
                onClick={() => setSelectedCategory(category as keyof typeof EMOJI_CATEGORIES)}
              >
                {t(`categories.${category}`)}
              </Button>
            ))}
          </div>

          {/* Emoji Grid */}
          <div className="grid grid-cols-8 gap-1 max-h-48 overflow-y-auto">
            {EMOJI_CATEGORIES[selectedCategory].map((emoji) => (
              <Button
                key={emoji}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-muted transition-colors"
                onClick={() => handleEmoteClick(emoji)}
              >
                <span className="text-lg">{emoji}</span>
              </Button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
