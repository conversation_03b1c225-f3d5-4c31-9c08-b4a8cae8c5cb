'use client'

import { cn } from '@/lib/utils'
import { ChatMessageItem } from './chat-message-item'
import { EmotePicker } from './emote-picker'
import { useChatScroll } from '@/hooks/use-chat-scroll'
import { useRealtimeChat, type ChatMessage, type ChatEmote } from '@/hooks/use-realtime-chat'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Send, Users, AlertCircle } from 'lucide-react'
import { useCallback, useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useWallet } from '@solana/wallet-adapter-react'
import { WalletButton } from '@/components/wallet/WalletButton'

interface RealtimeChatProps {
  streamId: string
  creatorWallet?: string
  className?: string
  showViewerCount?: boolean
  onViewerCountUpdate?: (count: number) => void
}

/**
 * Realtime chat component for bonkstream
 * Integrated with wallet authentication and BONK emotes
 */
export const RealtimeChat = ({
  streamId,
  creatorWallet,
  className,
  showViewerCount = true,
  onViewerCountUpdate
}: RealtimeChatProps) => {
  const t = useTranslations('stream.chat')
  const { connected } = useWallet()
  const { containerRef, scrollToBottom } = useChatScroll()
  
  const {
    messages,
    sendMessage,
    deleteMessage,
    viewerCount,
    isConnected,
    isLoading,
    error,
    canSendMessages,
    user
  } = useRealtimeChat({ streamId })

  // Call viewer count update callback when count changes
  useEffect(() => {
    if (onViewerCountUpdate && typeof viewerCount === 'number') {
      onViewerCountUpdate(viewerCount)
    }
  }, [viewerCount, onViewerCountUpdate])

  const [newMessage, setNewMessage] = useState('')
  const [pendingEmotes, setPendingEmotes] = useState<ChatEmote[]>([])
  const [isSending, setIsSending] = useState(false)

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  const handleSendMessage = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      if (!newMessage.trim() || !canSendMessages || isSending) return

      try {
        setIsSending(true)
        await sendMessage(newMessage, pendingEmotes)
        setNewMessage('')
        setPendingEmotes([])
      } catch (err) {
        console.error('Failed to send message:', err)
        // TODO: Show toast error message
      } finally {
        setIsSending(false)
      }
    },
    [newMessage, canSendMessages, isSending, sendMessage, pendingEmotes]
  )

  const handleEmoteSelect = useCallback(
    (emote: { type: 'emoji' | 'bonk_emote', code: string, display: string }) => {
      const currentMessage = newMessage
      const cursorPosition = currentMessage.length

      const newEmote: ChatEmote = {
        type: emote.type,
        code: emote.code,
        position: cursorPosition,
        length: emote.display.length
      }

      setNewMessage(prev => prev + emote.display)
      setPendingEmotes(prev => [...prev, newEmote])
    },
    [newMessage]
  )

  const handleDeleteMessage = useCallback(
    async (messageId: string) => {
      try {
        await deleteMessage(messageId)
      } catch (err) {
        console.error('Failed to delete message:', err)
        // TODO: Show toast error message
      }
    },
    [deleteMessage]
  )

  const canDeleteMessage = useCallback(
    (message: ChatMessage) => {
      if (!user) return false
      // User can delete their own messages, or creator can delete any message in their stream
      return message.sender_id === user.id || user.wallet_address === creatorWallet
    },
    [user, creatorWallet]
  )

  if (isLoading) {
    return (
      <div className={cn('flex flex-col h-full w-full bg-background text-foreground', className)}>
        <div className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>{t('loading')}</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('flex flex-col h-full w-full bg-background text-foreground border border-border rounded-lg overflow-hidden', className)}>
      {/* Header with viewer count */}
      {showViewerCount && (
        <div className="px-4 py-2 border-b border-border bg-muted/50">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-sm">{t('liveChat')}</h3>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>{viewerCount}</span>
            </div>
          </div>
        </div>
      )}

      {/* Connection status */}
      {error && (
        <Alert className="m-2 border-red-500/50 bg-red-900/20 text-red-400">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!isConnected && !error && (
        <Alert className="m-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{t('connecting')}</AlertDescription>
        </Alert>
      )}

      {/* Messages */}
      <div ref={containerRef} className="flex-1 overflow-y-auto p-4 space-y-1">
        {messages.length === 0 ? (
          <div className="text-center text-sm text-muted-foreground py-8">
            {t('noMessages')}
          </div>
        ) : (
          <div className="space-y-1">
            {messages.map((message, index) => {
              const prevMessage = index > 0 ? messages[index - 1] : null
              const showHeader = !prevMessage || 
                prevMessage.sender_id !== message.sender_id ||
                new Date(message.created_at).getTime() - new Date(prevMessage.created_at).getTime() > 300000 // 5 minutes

              return (
                <div
                  key={message.id}
                  className="animate-in fade-in slide-in-from-bottom-2 duration-300"
                >
                  <ChatMessageItem
                    message={message}
                    isOwnMessage={message.sender_id === user?.id}
                    showHeader={showHeader}
                    canDelete={canDeleteMessage(message)}
                    onDelete={handleDeleteMessage}
                  />
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Message input */}
      <div className="border-t border-border bg-background">
        {!connected ? (
          <div className="p-4 text-center">
            <p className="text-sm text-muted-foreground mb-3">
              {t('connectWalletToChat')}
            </p>
            <WalletButton>{t('connectWallet')}</WalletButton>
          </div>
        ) : !canSendMessages ? (
          <div className="p-4 text-center">
            <p className="text-sm text-muted-foreground">
              {t('cannotSendMessages')}
            </p>
          </div>
        ) : (
          <form onSubmit={handleSendMessage} className="flex gap-2 p-4">
            <div className="flex-1 flex gap-1">
              <Input
                className="flex-1 bg-background"
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder={t('typeMessage')}
                disabled={isSending || !isConnected}
                maxLength={500}
              />
              <EmotePicker 
                onEmoteSelect={handleEmoteSelect}
                disabled={isSending || !isConnected}
              />
            </div>
            <Button
              type="submit"
              disabled={!newMessage.trim() || isSending || !isConnected}
              className="px-3"
            >
              {isSending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        )}
      </div>
    </div>
  )
}
