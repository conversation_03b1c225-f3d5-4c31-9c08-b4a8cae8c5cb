'use client';

import { useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Heart } from 'lucide-react';

interface FloatingHeart {
  id: string;
  x: number;
  y: number;
  size: number;
  delay: number;
  wave: number;
  speed: number;
}

export interface VideoFloatingHeartsRef {
  triggerHearts: () => void;
}

interface VideoFloatingHeartsProps {
  className?: string;
}

export const VideoFloatingHearts = forwardRef<VideoFloatingHeartsRef, VideoFloatingHeartsProps>(
  ({ className = '' }, ref) => {
    const [floatingHearts, setFloatingHearts] = useState<FloatingHeart[]>([]);
    const containerRef = useRef<HTMLDivElement>(null);

    // Create floating hearts animation
    const triggerHearts = () => {
      const container = containerRef.current;
      if (!container) return;

      const rect = container.getBoundingClientRect();

      const newHearts: FloatingHeart[] = [];
      
      // Create 5 overlapping waves with scattered timing
      for (let wave = 0; wave < 5; wave++) {
        const heartsInWave = wave === 0 ? 20 : wave === 1 ? 15 : wave === 2 ? 12 : wave === 3 ? 8 : 5;
        
        for (let i = 0; i < heartsInWave; i++) {
          const startX = Math.random() * (rect.width - 30) + 15;
          const startY = Math.random() * 80; // More vertical scatter
          
          // Even more varied sizes including tiny and giant hearts
          let size;
          const sizeRand = Math.random();
          if (sizeRand < 0.2) size = Math.random() * 4 + 14; // Tiny hearts (14-18px)
          else if (sizeRand < 0.4) size = Math.random() * 6 + 20; // Small hearts (20-26px)
          else if (sizeRand < 0.6) size = Math.random() * 8 + 28; // Medium hearts (28-36px)
          else if (sizeRand < 0.8) size = Math.random() * 10 + 38; // Large hearts (38-48px)
          else if (sizeRand < 0.95) size = Math.random() * 12 + 50; // Extra large hearts (50-62px)
          else size = Math.random() * 15 + 65; // Giant hearts (65-80px)
          
          // Faster, more varied speeds
          let speed;
          const speedRand = Math.random();
          if (speedRand < 0.4) speed = 2.5 + Math.random() * 0.8; // Quick (2.5-3.3s)
          else if (speedRand < 0.7) speed = 3.0 + Math.random() * 0.8; // Medium (3.0-3.8s)
          else if (speedRand < 0.9) speed = 3.5 + Math.random() * 0.8; // Slower (3.5-4.3s)
          else speed = 4.0 + Math.random() * 0.5; // Slowest (4.0-4.5s)
          
          // Faster, more concentrated timing
          const baseDelay = wave * 200 + i * 20; // Faster wave timing
          const randomDelay = Math.random() * 600; // Reduced random delay
          
          newHearts.push({
            id: `heart-${Date.now()}-${wave}-${i}`,
            x: startX,
            y: startY,
            size: size,
            delay: baseDelay + randomDelay,
            wave: wave,
            speed: speed,
          });
        }
      }

      setFloatingHearts(prev => [...prev, ...newHearts]);

      // Remove hearts after animation completes
      setTimeout(() => {
        setFloatingHearts(prev => 
          prev.filter(heart => !newHearts.some(newHeart => newHeart.id === heart.id))
        );
      }, 4000); // Reduced from 6000 to 4000 for faster cleanup
    };

    // Expose the triggerHearts function to parent components
    useImperativeHandle(ref, () => ({
      triggerHearts
    }));

    return (
      <div 
        ref={containerRef} 
        className={`absolute inset-0 pointer-events-none overflow-hidden ${className}`}
        style={{ zIndex: 25 }}
      >
        {/* Floating hearts */}
        {floatingHearts.map((heart) => (
          <div
            key={heart.id}
            className="absolute pointer-events-none"
          style={{
            left: heart.x,
            bottom: heart.y,
            animationDelay: `${heart.delay}ms`,
            animation: `floatUpWave${heart.wave + 1} ${heart.speed}s ease-out forwards`
          }}
          >
            <Heart 
              className="text-red-500 fill-red-500 drop-shadow-lg" 
              size={heart.size}
              style={{
                filter: 'drop-shadow(0 0 12px rgba(239, 68, 68, 0.8))',
              }}
            />
          </div>
        ))}

        <style jsx>{`
          /* Wave 1 - Pop and drift left */
          @keyframes floatUpWave1 {
            0% { opacity: 0; transform: translateY(0) translateX(0) scale(0.3); }
            8% { opacity: 1; transform: translateY(-40px) translateX(3px) scale(1.1); }
            15% { opacity: 1; transform: translateY(-80px) translateX(2px) scale(1.0); }
            75% { opacity: 1; transform: translateY(-280px) translateX(-2px) scale(1.0); }
            85% { opacity: 0.8; transform: translateY(-320px) translateX(-8px) scale(1.3); }
            95% { opacity: 0.3; transform: translateY(-360px) translateX(-15px) scale(0.8); }
            100% { opacity: 0; transform: translateY(-380px) translateX(-20px) scale(0.4); }
          }
          
          /* Wave 2 - Pop and straight up */
          @keyframes floatUpWave2 {
            0% { opacity: 0; transform: translateY(0) translateX(0) scale(0.4); }
            10% { opacity: 1; transform: translateY(-50px) translateX(-1px) scale(1.2); }
            18% { opacity: 1; transform: translateY(-90px) translateX(0px) scale(1.0); }
            70% { opacity: 1; transform: translateY(-260px) translateX(1px) scale(1.0); }
            82% { opacity: 0.7; transform: translateY(-300px) translateX(0px) scale(1.4); }
            94% { opacity: 0.2; transform: translateY(-340px) translateX(0px) scale(0.9); }
            100% { opacity: 0; transform: translateY(-360px) translateX(0px) scale(0.5); }
          }
          
          /* Wave 3 - Pop and drift right */
          @keyframes floatUpWave3 {
            0% { opacity: 0; transform: translateY(0) translateX(0) scale(0.5); }
            6% { opacity: 1; transform: translateY(-30px) translateX(-2px) scale(1.0); }
            12% { opacity: 1; transform: translateY(-60px) translateX(-1px) scale(0.9); }
            78% { opacity: 1; transform: translateY(-270px) translateX(2px) scale(0.9); }
            88% { opacity: 0.6; transform: translateY(-310px) translateX(10px) scale(1.2); }
            96% { opacity: 0.2; transform: translateY(-350px) translateX(18px) scale(0.7); }
            100% { opacity: 0; transform: translateY(-370px) translateX(25px) scale(0.3); }
          }
          
          /* Wave 4 - Quick pop up */
          @keyframes floatUpWave4 {
            0% { opacity: 0; transform: translateY(0) translateX(0) scale(0.6); }
            12% { opacity: 1; transform: translateY(-60px) translateX(1px) scale(1.1); }
            20% { opacity: 1; transform: translateY(-100px) translateX(0px) scale(1.0); }
            72% { opacity: 1; transform: translateY(-250px) translateX(-1px) scale(1.0); }
            80% { opacity: 0.8; transform: translateY(-280px) translateX(-5px) scale(1.5); }
            92% { opacity: 0.3; transform: translateY(-320px) translateX(-12px) scale(1.0); }
            100% { opacity: 0; transform: translateY(-340px) translateX(-18px) scale(0.6); }
          }
          
          /* Wave 5 - Super quick burst */
          @keyframes floatUpWave5 {
            0% { opacity: 0; transform: translateY(0) translateX(0) scale(0.7); }
            4% { opacity: 1; transform: translateY(-20px) translateX(2px) scale(1.3); }
            8% { opacity: 1; transform: translateY(-40px) translateX(1px) scale(1.0); }
            80% { opacity: 1; transform: translateY(-290px) translateX(-2px) scale(1.0); }
            86% { opacity: 0.7; transform: translateY(-320px) translateX(8px) scale(1.4); }
            94% { opacity: 0.2; transform: translateY(-360px) translateX(16px) scale(0.8); }
            100% { opacity: 0; transform: translateY(-380px) translateX(22px) scale(0.4); }
          }
        `}</style>
      </div>
    );
  }
);

VideoFloatingHearts.displayName = 'VideoFloatingHearts';
