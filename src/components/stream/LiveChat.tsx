'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useParams } from 'next/navigation';
import { Send, Smile, MoreVertical, Trash2, MessageCircle, ChevronDown, ChevronUp } from 'lucide-react';

// Simple emotes cache to avoid repeated API calls
let emotesCache: { custom: Emote[]; standard: Emote[] } | null = null;
let emotesLoadPromise: Promise<{ custom: Emote[]; standard: Emote[] }> | null = null;
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import supabase from '@/lib/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';
import { getStoredAuth, createAuthMessage, storeAuth } from '@/lib/wallet-auth';
import { ChatMessage } from '@/hooks/use-realtime-chat';

interface Emote {
  id: string;
  name: string;
  image_url: string | null;
}

interface LiveChatProps {
  streamId: string;
  isCreator?: boolean;
  className?: string;
}

export function LiveChat({ streamId, isCreator = false, className = '' }: LiveChatProps) {
  const wallet = useWallet();
  const { connected, publicKey, signMessage } = wallet;
  const params = useParams();
  const locale = params.locale as string;
  
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showEmotePicker, setShowEmotePicker] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  
  // Emotes state
  const [availableEmotes, setAvailableEmotes] = useState<{
    custom: Emote[];
    standard: Emote[];
  }>({ custom: [], standard: [] });
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const channelRef = useRef<RealtimeChannel | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const errorTimeoutRef = useRef<NodeJS.Timeout | null>(null);
   
  // Auto-dismiss error messages after 5 seconds
  useEffect(() => {
    if (error) {
      errorTimeoutRef.current = setTimeout(() => {
        setError(null);
      }, 5000);
    }
    return () => {
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }
    };
  }, [error]);
  
  // Load initial chat messages - optimized to avoid redundant calls
  const loadMessages = useCallback(async () => {
    try {
      // Skip if already loading or no streamId
      if (isLoading || !streamId) return;
      
      setError(null);
      const response = await fetch(`/${locale}/api/stream/chat/${streamId}?limit=100`);
      
      if (!response.ok) {
        console.warn('Chat messages endpoint returned:', response.status);
        // Don't show error for chat loading, just show empty state
        setMessages([]);
      } else {
        const data = await response.json();
        if (data.success) {
          setMessages(data.messages || []);
        }
      }
    } catch (err) {
      console.warn('Error loading messages:', err);
      // Don't show error for initial load, just show empty chat
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  }, [streamId, locale, isLoading]);
  
  // Load available emotes with caching
  const loadEmotes = useCallback(async () => {
    try {
      // Return cached emotes if available
      if (emotesCache) {
        setAvailableEmotes(emotesCache);
        return;
      }

      // Prevent multiple simultaneous requests
      if (emotesLoadPromise) {
        const result = await emotesLoadPromise;
        setAvailableEmotes(result);
        return;
      }

      // Create new load promise
      emotesLoadPromise = fetch(`/${locale}/api/stream/chat/emotes`)
        .then(response => response.ok ? response.json() : null)
        .then(data => {
          if (data?.success) {
            emotesCache = data.emotes;
            return data.emotes;
          }
          return { custom: [], standard: [] };
        })
        .catch(err => {
          console.error('Error loading emotes:', err);
          return { custom: [], standard: [] };
        })
        .finally(() => {
          emotesLoadPromise = null;
        });

      const emotes = await emotesLoadPromise;
      setAvailableEmotes(emotes);
    } catch (err) {
      console.error('Error loading emotes:', err);
    }
  }, [locale]);
  
  // Authenticate wallet if needed
  const authenticateWallet = async (): Promise<boolean> => {
    if (!connected || !publicKey || !signMessage || isAuthenticating) {
      return false;
    }
    
    setIsAuthenticating(true);
    
    try {
      const walletAddress = publicKey.toString();
      
      // Check if we already have valid auth
      const existingAuth = getStoredAuth(walletAddress);
      if (existingAuth) {
        return true;
      }
      
      // Generate auth message
      const message = createAuthMessage(walletAddress);
      const encodedMessage = new TextEncoder().encode(message);
      
      // Request signature
      const signature = await signMessage(encodedMessage);
      const base64Signature = Buffer.from(signature).toString('base64');
      
      // Store auth data
      storeAuth({
        signature: base64Signature,
        message,
        walletAddress
      });
      
      setError(null);
      return true;
      
    } catch (err) {
      console.error('Wallet authentication error:', err);
      setError('Failed to authenticate wallet. Please try again.');
      return false;
    } finally {
      setIsAuthenticating(false);
    }
  };
  
  // Send message with simplified authentication
  const sendMessage = async () => {
    if (!newMessage.trim() || !connected || !publicKey || isSending) return;
    
    setIsSending(true);
    setError(null);
    
    try {
      const walletAddress = publicKey.toString();
      
      // Check and get auth data
      let authData = getStoredAuth(walletAddress);
      
      // If no auth data, try to authenticate
      if (!authData) {
        const authSuccess = await authenticateWallet();
        if (!authSuccess) {
          throw new Error('Please authenticate your wallet to send messages');
        }
        authData = getStoredAuth(walletAddress);
      }
      
      if (!authData) {
        throw new Error('Authentication failed');
      }
      
      const authHeaders: Record<string, string> = {
        'Authorization': `Wallet ${walletAddress}`,
        'x-wallet-signature': authData.signature,
        'x-wallet-message': authData.message
      };
      
      const response = await fetch(`/${locale}/api/stream/chat/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        },
        body: JSON.stringify({
          streamId,
          message: newMessage.trim(),
          walletAddress: publicKey.toString(),
          emotes: [],
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        // Check for specific error types
        if (response.status === 401) {
          throw new Error('Please reconnect your wallet to send messages');
        } else if (response.status === 429) {
          throw new Error('Sending messages too quickly. Please wait a moment.');
        } else {
          throw new Error(errorData.error || 'Failed to send message');
        }
      }
      
      // Clear input on success
      setNewMessage('');
      inputRef.current?.focus();
      
    } catch (err) {
      console.error('Error sending message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      // Show user-friendly error messages
      if (errorMessage.includes('Authentication') || errorMessage.includes('expired')) {
        setError('Please reconnect your wallet to send messages');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsSending(false);
    }
  };
  
  // Delete message (for creators and message senders)
  const deleteMessage = async (messageId: string) => {
    if (!connected || !publicKey) return;
    
    try {
      const walletAddress = publicKey.toString();
      
      // Check and get auth data
      let authData = getStoredAuth(walletAddress);
      
      // If no auth data, try to authenticate
      if (!authData) {
        const authSuccess = await authenticateWallet();
        if (!authSuccess) {
          throw new Error('Please authenticate your wallet to delete messages');
        }
        authData = getStoredAuth(walletAddress);
      }
      
      if (!authData) {
        throw new Error('Authentication required');
      }
      
      const authHeaders = {
        'Authorization': `Wallet ${walletAddress}`,
        'x-wallet-signature': authData.signature,
        'x-wallet-message': authData.message
      };
      
      const response = await fetch(`/${locale}/api/stream/chat/moderate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        },
        body: JSON.stringify({
          messageId,
          action: 'delete',
          walletAddress: publicKey.toString(),
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete message');
      }
      
      // Remove message from local state
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      
    } catch (err) {
      console.error('Error deleting message:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete message');
    }
  };
  
  // Add emote to message
  const addEmote = (emoteName: string) => {
    const currentMessage = newMessage;
    const newMessageWithEmote = currentMessage + (currentMessage ? ' ' : '') + emoteName;
    setNewMessage(newMessageWithEmote);
    setShowEmotePicker(false);
    inputRef.current?.focus();
  };
  
  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };
  
  // Check if user can moderate message
  const canModerateMessage = (message: ChatMessage) => {
    if (!connected || !publicKey) return false;
    return isCreator || message.sender_wallet_address === publicKey.toString();
  };
  
  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) { // Less than 1 minute
      return 'now';
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000);
      return `${minutes}m ago`;
    } else if (diff < 86400000) { // Less than 1 day
      const hours = Math.floor(diff / 3600000);
      return `${hours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };
  
  // Set up realtime subscriptions
  useEffect(() => {
    if (!streamId) return;
    
    loadMessages();
    loadEmotes();
    
    // Subscribe to chat messages
    const channel = supabase
      .channel(`live_chat:${streamId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `stream_id=eq.${streamId}`,
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage;
          if (!newMessage.is_deleted) {
            setMessages(prev => [...prev, newMessage]);
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'chat_messages',
          filter: `stream_id=eq.${streamId}`,
        },
        (payload) => {
          const updatedMessage = payload.new as ChatMessage;
          if (updatedMessage.is_deleted) {
            setMessages(prev => prev.filter(msg => msg.id !== updatedMessage.id));
          }
        }
      )
      .subscribe();
    
    channelRef.current = channel;
    
    return () => {
      channel.unsubscribe();
    };
  }, [streamId, loadMessages, loadEmotes]);
  
  return (
    <div className={`w-full ${className}`}>
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              <span>Live Chat</span>
              {messages.length > 0 && (
                <Badge variant="secondary" className="bg-bonk-yellow/20 text-bonk-yellow">
                  {messages.length}
                </Badge>
              )}
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="text-gray-400 hover:text-white"
            >
              {isCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
            </Button>
          </div>
        </CardHeader>
        
        {!isCollapsed && (
          <CardContent className="space-y-4">
          {/* Messages area */}
          <div 
            ref={messagesContainerRef}
            className="h-96 overflow-y-auto space-y-3 pr-2 bg-black/20 rounded-lg p-4"
          >
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-bonk-yellow border-t-transparent"></div>
              </div>
            ) : messages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <MessageCircle className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p className="text-sm">No messages yet. Start the conversation!</p>
              </div>
            ) : (
              messages.map((message) => (
                <div key={message.id} className="group">
                  <div className="flex items-start gap-3 p-2 rounded hover:bg-gray-800/50 transition-colors">
                    {/* Avatar */}
                    <Avatar className="w-8 h-8 flex-shrink-0">
                      <AvatarImage src={message.profiles?.avatar_url || undefined} />
                      <AvatarFallback className="bg-gradient-to-br from-bonk-yellow to-bonk-orange text-black text-xs font-bold">
                        {message.profiles?.username?.charAt(0).toUpperCase() || 
                         message.sender_wallet_address.slice(0, 1).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    {/* Message content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-bonk-yellow text-sm">
                          {message.profiles?.username || 
                           `${message.sender_wallet_address.slice(0, 6)}...${message.sender_wallet_address.slice(-4)}`}
                        </span>
                        <span className="text-gray-400 text-xs">
                          {formatTime(message.created_at)}
                        </span>
                      </div>
                      <p className="text-white text-sm break-words leading-relaxed">
                        {message.message_content}
                      </p>
                    </div>
                    
                    {/* Message actions */}
                    {canModerateMessage(message) && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 text-gray-400 hover:text-white"
                          >
                            <MoreVertical className="w-3 h-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className="text-red-400 hover:text-red-300"
                            onClick={() => deleteMessage(message.id)}
                          >
                            <Trash2 className="w-3 h-3 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>
          
          {/* Error message */}
          {error && (
            <div className="text-yellow-400 text-xs p-2 bg-yellow-900/20 rounded animate-fade-in">
              {error}
            </div>
          )}
          
          {/* Input area */}
          {connected ? (
            <div className="flex gap-3">
              <div className="flex-1 relative">
                <Input
                  ref={inputRef}
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a message..."
                  disabled={isSending}
                  maxLength={500}
                  className="pr-10 bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-bonk-yellow"
                />
                
                {/* Emote picker */}
                <Popover open={showEmotePicker} onOpenChange={setShowEmotePicker}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-gray-400 hover:text-white"
                      type="button"
                    >
                      <Smile className="w-4 h-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 p-2" align="end">
                    <div className="grid grid-cols-6 gap-1">
                      {/* Standard emojis */}
                      {availableEmotes.standard.map((emote) => (
                        <Button
                          key={emote.id}
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-lg hover:bg-gray-700"
                          onClick={() => addEmote(emote.name)}
                        >
                          {emote.name}
                        </Button>
                      ))}
                      
                      {/* Custom emotes */}
                      {availableEmotes.custom.map((emote) => (
                        <Button
                          key={emote.id}
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-gray-700"
                          onClick={() => addEmote(`:${emote.name}:`)}
                          title={emote.name}
                        >
                          {emote.image_url ? (
                            <Image
                              src={emote.image_url}
                              alt={emote.name}
                              width={24}
                              height={24}
                              className="w-6 h-6 object-contain"
                            />
                          ) : (
                            <span className="text-xs">{emote.name}</span>
                          )}
                        </Button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              
              <Button
                onClick={sendMessage}
                disabled={!newMessage.trim() || isSending || isAuthenticating}
                className="bg-bonk-yellow text-black hover:bg-bonk-yellow/90 font-medium"
              >
                {isAuthenticating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent" />
                ) : isSending ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            </div>
          ) : (
            <div className="text-center py-4 bg-gray-800/50 rounded-lg">
              <p className="text-gray-400 text-sm mb-3">
                Connect your wallet to join the conversation
              </p>
              <Button className="bg-bonk-yellow text-black hover:bg-bonk-yellow/90">
                Connect Wallet
              </Button>
            </div>
          )}
          </CardContent>
        )}
      </Card>
    </div>
  );
}
