import { cn } from '@/lib/utils'
import type { ChatMessage, ChatEmote } from '@/hooks/use-realtime-chat'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface ChatMessageItemProps {
  message: ChatMessage
  isOwnMessage: boolean
  showHeader: boolean
  canDelete?: boolean
  onDelete?: (messageId: string) => void
}

const BONK_EMOTES = {
  'bonk_thumbs_up': '👍',
  'bonk_fire': '🔥',
  'bonk_rocket': '🚀',
  'bonk_diamond': '💎',
  'bonk_moon': '🌙',
  'bonk_100': '💯'
}

function renderMessageWithEmotes(content: string, emotes: ChatEmote[] | null) {
  if (!emotes || emotes.length === 0) {
    return <span>{content}</span>
  }

  // Sort emotes by position in reverse order to replace from end to beginning
  const sortedEmotes = [...emotes].sort((a, b) => b.position - a.position)
  let processedContent = content

  for (const emote of sortedEmotes) {
    const before = processedContent.slice(0, emote.position)
    const after = processedContent.slice(emote.position + emote.length)
    
    let emoteDisplay = emote.code
    if (emote.type === 'bonk_emote' && emote.code in BONK_EMOTES) {
      emoteDisplay = BONK_EMOTES[emote.code as keyof typeof BONK_EMOTES]
    }
    
    processedContent = before + emoteDisplay + after
  }

  return <span>{processedContent}</span>
}

export const ChatMessageItem = ({ 
  message, 
  isOwnMessage, 
  showHeader, 
  canDelete = false,
  onDelete 
}: ChatMessageItemProps) => {
  const t = useTranslations('stream.chat')

  const handleDelete = () => {
    if (onDelete) {
      onDelete(message.id)
    }
  }

  return (
    <div className={`flex mt-2 group ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
      <div
        className={cn('max-w-[75%] w-fit flex gap-2', {
          'flex-row-reverse': isOwnMessage,
        })}
      >
        {showHeader && !isOwnMessage && (
          <Avatar className="h-6 w-6 flex-shrink-0">
            <AvatarImage src={message.profiles?.avatar_url || undefined} />
            <AvatarFallback className="text-xs bg-primary/10">
              {(message.profiles?.username || message.sender_wallet_address).slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        )}
        
        <div className={cn('flex flex-col gap-1', {
          'items-end': isOwnMessage,
        })}>
          {showHeader && (
            <div
              className={cn('flex items-center gap-2 text-xs px-3', {
                'justify-end flex-row-reverse': isOwnMessage,
              })}
            >
              <span className="font-medium text-primary">
                {message.profiles?.username || message.sender_wallet_address.slice(0, 8)}
              </span>
              <span className="text-muted-foreground text-xs">
                {new Date(message.created_at).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true,
                })}
              </span>
            </div>
          )}
          
          <div className="relative">
            <div
              className={cn(
                'py-2 px-3 rounded-xl text-sm w-fit relative',
                isOwnMessage 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted text-foreground border border-border'
              )}
            >
              {renderMessageWithEmotes(message.message_content, message.emotes)}
            </div>
            
            {canDelete && (
              <Button
                size="sm"
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={handleDelete}
                title={t('deleteMessage')}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
