'use client';

import { useState, useEffect, useRef } from 'react';
import { Heart } from 'lucide-react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useLocale } from 'next-intl';
import { useToast } from '@/components/ui/toast';

interface FloatingHeart {
  id: string;
  x: number;
  y: number;
}

interface LikeButtonProps {
  streamId: string;
  onLikeChange?: (isLiked: boolean, likeCount: number) => void;
  onHeartAnimation?: () => void;
  className?: string;
  showCount?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'mobile';
}

export function LikeButton({ 
  streamId, 
  onLikeChange,
  onHeartAnimation,
  className = '',
  showCount = true,
  variant = 'default'
}: LikeButtonProps) {
  const { connected, publicKey } = useWallet();
  const locale = useLocale();
  const { toast } = useToast();
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [floatingHearts, setFloatingHearts] = useState<FloatingHeart[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load initial like status - optimized to avoid redundant calls
  useEffect(() => {
    const fetchLikeStatus = async () => {
      try {
        // Skip API call if we don't have a valid streamId
        if (!streamId) return;
        
        let url = `/${locale}/api/streams/${streamId}/like`;
        if (connected && publicKey) {
          url += `?walletAddress=${publicKey.toString()}`;
        }
        const response = await fetch(url);
        if (response.ok) {
          const data = await response.json();
          setIsLiked(data.isLiked);
          setLikeCount(data.likeCount);
        }
      } catch (error) {
        console.error('Error fetching like status:', error);
      }
    };

    fetchLikeStatus();
  }, [streamId, locale, connected, publicKey]);

  // Create floating hearts animation
  const createFloatingHearts = () => {
    const container = containerRef.current;
    if (!container) return;

    const rect = container.getBoundingClientRect();
    const heartsCount = 1; // 3-5 hearts

    const newHearts: FloatingHeart[] = [];
    for (let i = 0; i < heartsCount; i++) {
      newHearts.push({
        id: `heart-${Date.now()}-${i}`,
        x: Math.random() * rect.width,
        y: rect.height
      });
    }

    setFloatingHearts(prev => [...prev, ...newHearts]);

    // Remove hearts after animation
    setTimeout(() => {
      setFloatingHearts(prev => 
        prev.filter(heart => !newHearts.some(newHeart => newHeart.id === heart.id))
      );
    }, 2000);
  };

  // Handle like toggle
  const handleLike = async () => {
    if (!connected || !publicKey) {
      // Show toast notification
      toast({
        description: 'Connect your wallet to like this stream',
        variant: 'default'
      });
      
      // Create a custom wallet connection event
      const connectEvent = new CustomEvent('requestWalletConnection', {
        detail: { action: 'like', message: 'Connect your wallet to like this stream' }
      });
      window.dispatchEvent(connectEvent);
      return;
    }

    if (isLoading) return;

    setIsLoading(true);

    try {
      const response = await fetch(`/${locale}/api/streams/${streamId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: publicKey.toString()
        })
      });

      if (response.ok) {
        const data = await response.json();
        setIsLiked(data.isLiked);
        setLikeCount(data.likeCount);
        
        // Trigger floating hearts animation on successful like
        if (data.isLiked) {
          createFloatingHearts();
          // Also trigger video overlay hearts if callback provided
          onHeartAnimation?.();
        }

        // Notify parent component
        onLikeChange?.(data.isLiked, data.likeCount);
      } else {
        const errorData = await response.json();
        toast({
          description: errorData.error || 'Failed to like stream',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        description: 'Something went wrong. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Mobile variant (TikTok-style)
  if (variant === 'mobile') {
    return (
      <div ref={containerRef} className={`relative flex flex-col items-center ${className}`}>
        {/* Floating hearts */}
        {floatingHearts.map((heart) => (
          <div
            key={heart.id}
            className="absolute pointer-events-none z-50"
            style={{
              left: heart.x,
              bottom: heart.y,
              animation: 'floatUp 2s ease-out forwards'
            }}
          >
            <Heart 
              className="text-red-500 fill-red-500" 
              size={Math.random() * 8 + 16} 
            />
          </div>
        ))}

        <button
          onClick={handleLike}
          disabled={isLoading}
          className={`
            w-10 h-10 rounded-full flex items-center justify-center p-0 sm:w-12 sm:h-12
            transition-all duration-200 relative
            ${isLiked 
              ? 'bg-red-500 text-white hover:bg-red-600' 
              : 'bg-white/20 text-white hover:bg-white/30'
            }
            ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}
            backdrop-blur-sm border border-white/20
          `}
        >
          <Heart 
            size={16}
            className={`transition-all duration-200 ${
              isLiked ? 'fill-white text-white scale-110' : ''
            }`}
          />
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            </div>
          )}
        </button>

        {showCount && (
          <span className="text-xs text-white mt-1 text-center">
            {likeCount > 999 ? `${(likeCount / 1000).toFixed(1)}k` : likeCount}
          </span>
        )}

        <style jsx>{`
          @keyframes floatUp {
            0% {
              opacity: 1;
              transform: translateY(0) scale(0.8) rotate(0deg);
            }
            50% {
              opacity: 1;
              transform: translateY(-100px) scale(1.2) rotate(180deg);
            }
            100% {
              opacity: 0;
              transform: translateY(-200px) scale(0.6) rotate(360deg);
            }
          }
        `}</style>
      </div>
    );
  }

  // Default variant (desktop)
  return (
    <div ref={containerRef} className="relative">
      {/* Floating hearts */}
      {floatingHearts.map((heart) => (
        <div
          key={heart.id}
          className="absolute pointer-events-none z-50"
          style={{
            left: heart.x,
            bottom: heart.y,
            animation: 'floatUp 2s ease-out forwards'
          }}
        >
          <Heart 
            className="text-red-500 fill-red-500" 
            size={Math.random() * 8 + 20} 
          />
        </div>
      ))}

      <button 
        onClick={handleLike}
        disabled={isLoading}
        className={`${isLiked 
          ? 'bg-red-500 hover:bg-red-600' 
          : 'bg-gray-800 hover:bg-gray-700'
        } text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors relative
        ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <Heart className={`w-4 h-4 ${isLiked ? 'fill-white' : ''}`} />
        {showCount ? (
          likeCount > 999 ? `${(likeCount / 1000).toFixed(1)}k` : likeCount
        ) : (
          'Like'
        )}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-white rounded-full animate-spin" />
          </div>
        )}
      </button>

      <style jsx>{`
        @keyframes floatUp {
          0% {
            opacity: 1;
            transform: translateY(0) scale(0.8) rotate(0deg);
          }
          50% {
            opacity: 1;
            transform: translateY(-100px) scale(1.2) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: translateY(-200px) scale(0.6) rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
}
