'use client';

import { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';

interface SignatureVerificationProps {
  challenge: string;
  onSignatureComplete: (signature: string, message: string) => void;
  onCancel: () => void;
  operationDescription: string;
}

export default function SignatureVerification({
  challenge,
  onSignatureComplete,
  onCancel,
  operationDescription
}: SignatureVerificationProps) {
  const { publicKey, signMessage } = useWallet();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const handleSign = async () => {
    if (!publicKey || !signMessage) {
      setError('Wallet connection required');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Convert challenge message to Uint8Array for signing
      const messageUint8 = new TextEncoder().encode(challenge);
      
      // Sign the message with the wallet
      const signature = await signMessage(messageUint8);
      
      // Convert the signature to a base58 string (bs58 package would be used in the backend)
      const bs58Signature = Buffer.from(signature).toString('base64');
      
      // Call the completion callback with the signature and message
      onSignatureComplete(bs58Signature, challenge);
    } catch (error) {
      console.error('Error signing message:', error);
      setError(
        error instanceof Error 
          ? error.message 
          : 'Failed to sign message. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="p-6 bg-gray-900 border border-white/20 rounded-lg shadow-lg">
      <h3 className="text-xl font-bold text-white mb-4">Additional Verification Required</h3>
      
      <p className="text-white/80 mb-4">
        This sensitive operation requires additional verification:
      </p>
      
      <div className="bg-black/30 p-4 rounded mb-6 border border-white/10">
        <p className="text-amber-400 font-medium">{operationDescription}</p>
      </div>
      
      <p className="text-white/80 mb-6">
        Please sign the following message with your wallet to confirm your identity and authorization.
      </p>
      
      <div className="bg-black/30 p-4 rounded mb-6 overflow-auto font-mono text-sm text-white/80 border border-white/10">
        {challenge}
      </div>
      
      {error && (
        <div className="bg-red-900/30 border border-red-500/50 p-3 rounded mb-4 text-red-200">
          {error}
        </div>
      )}
      
      <div className="flex justify-end space-x-4">
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded"
          disabled={isLoading}
        >
          Cancel
        </button>
        
        <button
          onClick={handleSign}
          className="px-4 py-2 bg-[#FFD700] hover:bg-[#FFD700]/80 text-black font-medium rounded flex items-center"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing...
            </>
          ) : (
            'Sign and Authorize'
          )}
        </button>
      </div>
    </div>
  );
} 