'use client'

/* eslint-disable react-hooks/exhaustive-deps */

import supabase from '@/lib/supabase/client'
import { useCallback, useEffect, useState } from 'react'
import { useWalletAuth } from '@/lib/useWalletAuth'
import { useWallet } from '@solana/wallet-adapter-react'
import { useParams } from 'next/navigation'
import { z } from 'zod'

interface UseRealtimeChatProps {
  streamId: string
  enabled?: boolean
}

interface UserProfile {
  id: string
  username?: string
  wallet_address: string
  avatar_url?: string
}

export interface ChatEmote {
  type: 'emoji' | 'bonk_emote'
  code: string
  position: number
  length: number
}

export interface ChatMessage {
  id: string
  stream_id: string
  sender_id: string
  sender_wallet_address: string
  message_content: string
  emotes: ChatEmote[] | null
  is_deleted: boolean
  deleted_by: string | null
  created_at: string
  updated_at: string
  profiles: {
    username: string | null
    avatar_url: string | null
  } | null
}

export interface ChatUser {
  id: string
  name: string
  avatar?: string
  wallet_address: string
}

const emoteSchema = z.object({
  type: z.enum(['emoji', 'bonk_emote']),
  code: z.string(),
  position: z.number(),
  length: z.number()
})

const messageSchema = z.object({
  message: z.string().min(1).max(500),
  emotes: z.array(emoteSchema).optional()
})

const EVENT_MESSAGE_TYPE = 'new_message'
const EVENT_DELETE_TYPE = 'delete_message'
const EVENT_VIEWER_COUNT_TYPE = 'viewer_count'

export function useRealtimeChat({ streamId, enabled = true }: UseRealtimeChatProps) {
  const { isAuthenticated } = useWalletAuth()
  const { publicKey } = useWallet()
  const params = useParams()
  const locale = params.locale as string
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [viewerCount, setViewerCount] = useState(0)
  const [channel, setChannel] = useState<ReturnType<typeof supabase.channel> | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null)

  // Fetch current user profile when authenticated
  useEffect(() => {
    if (!isAuthenticated || !publicKey) {
      setCurrentUser(null)
      return
    }

    const fetchUserProfile = async () => {
      try {
        const walletAddress = publicKey.toString()
        const response = await fetch(`/${locale}/api/profile?wallet=${walletAddress}`)
        if (response.ok) {
          const data = await response.json()
          setCurrentUser(data.profile)
        } else {
          console.error('Failed to fetch user profile:', response.statusText)
        }
      } catch (error) {
        console.error('Error fetching user profile:', error)
      }
    }

    fetchUserProfile()
  }, [isAuthenticated, publicKey, locale])

  // Load initial messages
  useEffect(() => {
    if (!enabled || !streamId) return

    const loadMessages = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/${locale}/api/stream/chat/${streamId}?limit=20`) // Reduced from 100 to 20 for faster loading
        
        if (!response.ok) {
          throw new Error('Failed to load messages')
        }
        
        const data = await response.json()
        if (data.success) {
          setMessages(data.messages || [])
        } else {
          throw new Error(data.error || 'Failed to load messages')
        }
      } catch (err) {
        console.error('Error loading messages:', err)
        setError('Failed to load chat messages')
      } finally {
        setIsLoading(false)
      }
    }

    loadMessages()
  }, [streamId, enabled, locale])

  // Set up real-time subscription
  useEffect(() => {
    if (!enabled || !streamId) return

    const channelName = `stream_chat_${streamId}`
    const newChannel = supabase.channel(channelName)

    newChannel
      .on('broadcast', { event: EVENT_MESSAGE_TYPE }, (payload) => {
        const message = payload.payload as ChatMessage
        setMessages((current) => [...current, message])
      })
      .on('broadcast', { event: EVENT_DELETE_TYPE }, (payload) => {
        const { messageId } = payload.payload as { messageId: string }
        setMessages((current) => 
          current.map(msg => 
            msg.id === messageId 
              ? { ...msg, is_deleted: true, deleted_by_creator: true }
              : msg
          )
        )
      })
      .on('broadcast', { event: EVENT_VIEWER_COUNT_TYPE }, (payload) => {
        const { count } = payload.payload as { count: number }
        setViewerCount(count)
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
          setError(null)
        } else if (status === 'CHANNEL_ERROR') {
          setError('Connection failed')
          setIsConnected(false)
        }
      })

    setChannel(newChannel)

    return () => {
      supabase.removeChannel(newChannel)
      setIsConnected(false)
    }
  }, [streamId, enabled])

  const sendMessage = useCallback(
    async (content: string, emotes?: ChatEmote[]) => {
      if (!channel || !isConnected || !isAuthenticated || !currentUser) {
        throw new Error('Not connected or not authenticated')
      }

      try {
        // Validate message
        const validated = messageSchema.parse({ message: content, emotes })

        // Send to API endpoint for database storage and broadcasting
        const response = await fetch(`/${locale}/api/stream/chat/send`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Wallet ${currentUser.wallet_address}`,
            'x-wallet-signature': localStorage.getItem('walletAuthSignature') || '',
            'x-wallet-message': localStorage.getItem('walletAuthMessage') || '',
          },
          body: JSON.stringify({
            streamId,
            message: validated.message,
            walletAddress: currentUser.wallet_address,
            emotes: validated.emotes || []
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to send message')
        }

        const { message } = await response.json()
        
        // Broadcast to other users (sender gets it via database response)
        await channel.send({
          type: 'broadcast',
          event: EVENT_MESSAGE_TYPE,
          payload: message,
        })

      } catch (err) {
        console.error('Error sending message:', err)
        throw err
      }
    },
    [channel, isConnected, isAuthenticated, currentUser, streamId]
  )

  const deleteMessage = useCallback(
    async (messageId: string) => {
      if (!channel || !isConnected || !isAuthenticated || !currentUser) {
        throw new Error('Not connected or not authenticated')
      }

      try {
        const response = await fetch(`/${locale}/api/stream/chat/delete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Wallet ${currentUser.wallet_address}`,
            'x-wallet-signature': localStorage.getItem('walletAuthSignature') || '',
            'x-wallet-message': localStorage.getItem('walletAuthMessage') || '',
          },
          body: JSON.stringify({
            messageId,
            streamId
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to delete message')
        }

        // Broadcast deletion to other users
        await channel.send({
          type: 'broadcast',
          event: EVENT_DELETE_TYPE,
          payload: { messageId },
        })

      } catch (err) {
        console.error('Error deleting message:', err)
        throw err
      }
    },
    [channel, isConnected, isAuthenticated, currentUser, streamId]
  )

  const updateViewerCount = useCallback(
    async (count: number) => {
      if (!channel || !isConnected) return

      await channel.send({
        type: 'broadcast',
        event: EVENT_VIEWER_COUNT_TYPE,
        payload: { count },
      })
    },
    [channel, isConnected]
  )

  return {
    messages: messages.filter(msg => !msg.is_deleted),
    sendMessage,
    deleteMessage,
    updateViewerCount,
    viewerCount,
    isConnected,
    isLoading,
    error,
    canSendMessages: isAuthenticated && isConnected,
    user: currentUser ? {
      id: currentUser.id,
      name: currentUser.username || currentUser.wallet_address.slice(0, 8),
      avatar: currentUser.avatar_url || undefined,
      wallet_address: currentUser.wallet_address
    } as ChatUser : null
  }
}
