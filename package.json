{"name": "bonkstream", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "setup-db": "node scripts/setup-supabase.js", "generate-types": "node scripts/generate-types.js", "fix-types": "node scripts/fix-profile-types.js"}, "dependencies": {"@livepeer/core": "^3.3.1", "@livepeer/react": "^4.3.5", "@metaplex-foundation/js": "^0.19.5", "@metaplex-foundation/mpl-token-metadata": "^2.13.0", "@metaplex-foundation/umi": "^1.2.0", "@metaplex-foundation/umi-bundle-defaults": "^1.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@solana/spl-token": "^0.3.8", "@solana/wallet-adapter-backpack": "^0.1.14", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-react-ui": "^0.9.34", "@solana/wallet-adapter-wallets": "^0.19.36", "@solana/web3.js": "^1.87.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.16", "@vercel/analytics": "^1.5.0", "bs58": "^6.0.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "hls.js": "^1.6.5", "livepeer": "^3.5.0", "lodash": "^4.17.21", "lucide-react": "^0.446.0", "next": "^15.3.5", "next-intl": "^4.1.0", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-player": "^2.13.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "webrtc-adapter": "^9.0.3", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20.17.57", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "supabase": "^2.22.6", "supabase-mcp": "^1.5.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}