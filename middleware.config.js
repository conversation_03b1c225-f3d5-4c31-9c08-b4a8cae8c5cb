/** @type {import('next-intl/middleware').MiddlewareConfig} */
module.exports = {
  // Locales that are supported
  locales: ['en', 'de'],
  
  // Default locale to use when visiting a non-locale prefixed path
  defaultLocale: 'en',
  
  // Paths that should not be processed by the middleware (these include API routes)
  pathnames: {
    // Automatically determine pathnames
    autoDetect: true
  },
  
  // Locale prefix strategy
  localePrefix: 'always',
  
  // Routes to exclude from localization
  excludedRoutes: [
    // API routes
    '/api/:path*',
    // Static files
    '/_next/:path*',
    '/favicon.ico',
    '/robots.txt',
  ]
}; 